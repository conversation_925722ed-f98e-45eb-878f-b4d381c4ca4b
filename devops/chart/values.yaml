# Default values for choerodon-front.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

image:
  repository: tone.tcl.com/devops/docker/snapshot/ai/ai-purchase-front
  pullPolicy: IfNotPresent

imagePullSecret:
  enable: true
  name: tone-docker-secret

logs:
  parser: ai-purchase-front

service:
  # service名称
  name: ai-purchase-front
  # Service 类型
  type: ClusterIP
  ports:
    # 服务端口
    http:
      port: 80
# Liveness 和 Readiness 探针相关配置
# ref: https://kubernetes.io/docs/tasks/configure-pod-container/configure-liveness-readiness-probes/
livenessProbe:
  initialDelaySeconds: 30
  periodSeconds: 15
  timeoutSeconds: 3
  successThreshold: 1
  failureThreshold: 3
readinessProbe:
  initialDelaySeconds: 30
  periodSeconds: 15
  timeoutSeconds: 3
  successThreshold: 1
  failureThreshold: 3
env:
  open:
    TZ: Asia/Shanghai

resources:
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources,such as Minikube. If you do want to specify resources,uncomment the following
  # lines,adjust them as necessary,and remove the curly braces after 'resources:'.
  limits:
    # cpu: 100m
    # memory: 2Gi
  requests:
    # cpu: 100m
    # memory: 1Gi
