apiVersion: v1
kind: Service
metadata:
  name: {{ .Values.service.name }}
  labels:
    app.poros-vue-cli.io/release: {{ .Values.service.name }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.ports.http.port }}
      targetPort: {{ .Values.service.ports.http.port }}
      protocol: TCP
      name: http
  selector:
    app.poros-vue-cli.io/release: {{ .Values.service.name }}