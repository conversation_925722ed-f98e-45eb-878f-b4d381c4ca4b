#!/bin/bash
set -e

find /usr/local/openresty/nginx/html -name '*.js' | xargs sed -i "s BUILD_HTTP_BASE_URL $BUILD_HTTP_BASE_URL g"
find /usr/local/openresty/nginx/html -name '*.js' | xargs sed -i "s BUILD_HTTP_TIMEOUT $BUILD_HTTP_TIMEOUT g"
find /usr/local/openresty/nginx/html -name '*.js' | xargs sed -i "s BUILD_CLIENT_ID $BUILD_CLIENT_ID g"
find /usr/local/openresty/nginx/html -name '*.js' | xargs sed -i "s BUILD_ROLE_CODE $BUILD_ROLE_CODE g"
find /usr/local/openresty/nginx/html -name '*.js' | xargs sed -i "s BUILD_ENV $BUILD_ENV g"
find /usr/local/openresty/nginx/html -name '*.js' | xargs sed -i "s BUILD_HELP_URL $BUILD_HELP_URL g"
find /usr/local/openresty/nginx/html -name '*.js' | xargs sed -i "s BUILD_LOGO_URL $BUILD_LOGO_URL g"
find /usr/local/openresty/nginx/html -name '*.js' | xargs sed -i "s BUILD_IFRAME_REPORT_DOMAIN $BUILD_IFRAME_REPORT_DOMAIN g"


exec "$@"
