FROM tone.tcl.com/devops/docker/release/ea/frontbase:1.0.0

# Copy static files to nginx serve directory
ADD ./dist /usr/local/openresty/nginx/html
# Deploy without sourcemap files
RUN find /usr/local/openresty/nginx/html -name "*.*.map" -exec rm {} \;
# Copy shell script file, which replace placeholders with env variables
ADD ./env.sh /usr/local/openresty/nginx/html
# Make env.sh file runnable
RUN chmod 777 /usr/local/openresty/nginx/html/env.sh

ENTRYPOINT ["/usr/local/openresty/nginx/html/env.sh"]
CMD ["nginx", "-g", "daemon off;"]

EXPOSE 80
