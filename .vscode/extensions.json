{
  "recommendations": [
    // Vue 3开发必备
    "Vue.volar",
    "Vue.vscode-typescript-vue-plugin",

    // 代码格式化和检查
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",

    // TypeScript支持
    "ms-vscode.vscode-typescript-next",

    // 其他有用的扩展
    "bradlc.vscode-tailwindcss",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense",
    "ms-vscode.vscode-json",
    "redhat.vscode-yaml",

    // Git相关
    "eamodio.gitlens"
  ],
  "unwantedRecommendations": [
    // 禁用可能冲突的扩展
    "octref.vetur",
    "hookyqr.beautify",
    "ms-vscode.vscode-typescript-tslint-plugin"
  ]
}
