image: registry.getech.cn/poros/cifront:2.3

stages:
  - build
  - docker-build

# 前端构建
build:
  stage: build
  only:
    - release
    - master
    - dev
  script:
    - node -v
    - npm install --registry=https://registry.npmmirror.com/
    - npm run build
    - echo "build complete..."
  cache:
    paths:
      - node_modules/
  artifacts:
    name: $CI_PROJECT_NAME
    expire_in: 1 day
    paths:
      - dist/*

# 构建docker镜像
docker-build:
  stage: docker-build
  only:
    - release
    - master
    - dev
  script:
    - docker_build
    - chart_build

.auto_devops: &auto_devops |
  curl -o .auto_devops.sh \
      "${CHOERODON_URL}/devops/ci?token=${Token}&type=microservice"
  if [ $? -ne 0 ];then
    cat .auto_devops.sh
    exit 1
  fi
  source .auto_devops.sh
  function docker_build(){
    docker build --pull -t ${DOCKER_REGISTRY}/${GROUP_NAME}/${PROJECT_NAME}:${CI_COMMIT_TAG} -f docker/Dockerfile .
    docker push ${DOCKER_REGISTRY}/${GROUP_NAME}/${PROJECT_NAME}:${CI_COMMIT_TAG}
  }

before_script:
  - *auto_devops
