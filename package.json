{"name": "ai-purchase-front", "version": "0.0.1", "scripts": {"serve": "vite --mode=dev --open", "sit": "vite --mode=sit", "uat": "vite --mode=uat", "prod": "vite --mode=prod", "build": "vite build", "lint": "npm run eslint && npm run stylelint", "eslint": "eslint src/**/*.{js,jsx,vue,ts,tsx} --fix", "preview": "vite preview --port 4173", "stylelint": "stylelint src/**/*.{css,less,vue} --fix", "prepare": "husky install"}, "dependencies": {"@ant-design/icons-vue": "^6.1.0", "@highlightjs/vue-plugin": "^2.1.0", "@microsoft/fetch-event-source": "^2.0.1", "ant-design-vue": "3.2.20", "axios": "^1.1.2", "ai-purchase-front": "file:", "crypto-js": "^4.1.1", "dayjs": "^1.11.7", "dexie": "^3.2.2", "echarts": "^5.4.3", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "github-markdown-css": "^5.1.0", "highlight.js": "^11.11.1", "html2canvas": "^1.4.1", "js-cookie": "^3.0.1", "js-yaml": "^4.1.0", "jsencrypt": "^3.3.2", "jspdf": "^2.5.1", "lodash-es": "^4.17.21", "lottie-web": "^5.12.2", "mammoth": "^1.6.0", "markdown-it": "^14.0.0", "marked": "^12.0.2", "marked-highlight": "^2.0.4", "microsoft-cognitiveservices-speech-sdk": "^1.38.0", "mobile-detect": "^1.4.5", "nprogress": "^0.2.0", "pdfjs-dist": "3.5.141", "pinia": "^2.0.21", "pinia-plugin-persist": "^1.0.0", "qs": "^6.11.0", "sa-sdk-javascript": "^1.26.14", "screenfull": "^6.0.2", "sql-formatter": "^15.4.11", "uuid": "^9.0.0", "vant": "^3.6.12", "vconsole": "^3.15.1", "vue": "3.5.11", "vue-clipboard3": "^2.0.0", "vue-draggable-plus": "^0.5.0", "vue-easy-lightbox": "^1.19.0", "vue-json-pretty": "^2.4.0", "vue-pdf-embed": "^2.1.2", "vue-router": "^4.1.5", "vue-virtual-scroller": "^2.0.0-beta.8", "vue3-draggable-resizable": "^1.6.5", "vue3-infinite-scroll-better": "^2.2.0", "xlsx": "^0.18.5", "xss": "^1.0.14", "ywl-watermark-vue": "^1.2.1", "@ea/ai-sdk": "0.3.14"}, "devDependencies": {"@babel/eslint-plugin": "^7.19.1", "@vitejs/plugin-vue": "^3.0.3", "@vitejs/plugin-vue-jsx": "^2.0.1", "autoprefixer": "^10.4.20", "eslint": "^8.25.0", "eslint-plugin-vue": "^9.6.0", "husky": "^8.0.1", "less": "^4.1.3", "less-loader": "^11.1.0", "lint-staged": "^13.0.3", "markdown-it-abbr": "^2.0.0", "markdown-it-anchor": "^8.6.5", "markdown-it-footnote": "^4.0.0", "markdown-it-highlightjs": "^4.0.1", "markdown-it-named-code-blocks": "^0.2.0", "markdown-it-sub": "^2.0.0", "markdown-it-sup": "^2.0.0", "markdown-it-task-lists": "^2.1.1", "markdown-it-toc-done-right": "^4.2.0", "postcss": "^8.4.47", "rollup-plugin-visualizer": "^5.8.3", "stylelint": "^13.12.0", "stylelint-config-standard": "^21.0.0", "stylelint-order": "^4.1.0", "tailwindcss": "^3.4.17", "validate-commit-msg": "^2.14.0", "vconsole": "^3.15.1", "vite": "^3.0.9", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-markdown": "^0.22.1", "vue-eslint-parser": "9.1.0"}, "lint-staged": {"src/**/*.{js,ts,tsx,jsx,vue}": ["eslint"], "*.{css,less,vue}": ["stylelint"]}}