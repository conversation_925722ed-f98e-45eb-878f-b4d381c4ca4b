import { defineConfig, loadEnv } from "vite";
import vue from "@vitejs/plugin-vue";
import vueJsx from "@vitejs/plugin-vue-jsx";
import { visualizer } from "rollup-plugin-visualizer";
import { createSvgIconsPlugin } from "vite-plugin-svg-icons";
import path from "path";

const getEnv = (key) => {
  const { cwd, argv } = process;
  let mode = argv[argv.length - 1].split("=")[1];
  return loadEnv(mode, cwd())[key];
};

const isDev = getEnv("VITE_NODE_ENV") === "development";

const domainMap = {
  DEV: "https://ai-dev-gateway.eads.tcl.com",
  // TEST: 'http://10.88.45.31'
  TEST: "https://uat.poros.getech.cn",
};

const proxyAPI = domainMap.TEST;

// https://vitejs.dev/config/
export default defineConfig({
  base: "", //开发或生产环境服务的公共基础路径
  plugins: [
    vue({
      include: [/\.vue$/, /\.md$/],
    }),
    vueJsx(),
    createSvgIconsPlugin({
      iconDirs: [path.resolve(__dirname, "./src/assets/svgs")],
      symbolId: "icon-[name]",
    }),
    visualizer({ filename: "analyzer.html" }),
  ],
  resolve: {
    // 别名
    alias: {
      "@": path.resolve(__dirname, "./src"),
      "@sdk": path.resolve(__dirname, "./public/sdk"),
    },
    // 忽略后缀名配置
    extensions: [".js", "jsx", ".vue", ".json"],
  },
  lintOnSave: true,
  build: {
    rollupOptions: {
      output: {
        // 拆包
        manualChunks: {
          vue: ["vue", "vue-router", "pinia"],
          antDesignVue: ["ant-design-vue"],
        },
        // 用于从入口点创建的块的打包输出格式[name]表示文件名,[hash]表示该文件内容hash值
        entryFileNames: "js/[name].[hash].js",
        // 用于输出静态资源的命名，[ext]表示文件扩展名
        assetFileNames: "[ext]/[name].[hash].[ext]",
        chunkFileNames: (chunkInfo) => {
          // const facadeModuleId = chunkInfo.facadeModuleId ? chunkInfo.facadeModuleId.split('/') : [];
          // const fileName = facadeModuleId[facadeModuleId.length - 2] || '[name]';
          return `js/[name].[hash].js`;
        },
      },
    },
  },
  css: {
    preprocessorOptions: {
      less: {
        modifyVars: {
          "primary-color": "#6355FF",
          "link-color": "#6355FF",
          "@font-size-base": "12px",
          "text-color": "#333",
          "box-shadow-base": "0 1px 8px rgba(0, 0, 0, 0.15)",
        },
        javascriptEnabled: true,
      },
    },
    postcss: {
      plugins: [
        require('tailwindcss'),
        require('autoprefixer')
      ]
    }
  },
  server: {
    host: "0.0.0.0",
    port: 1024,
    proxy: {
      "/api/my_attendance": {
        target: "https://api-gw-uat.tcl.com",
        changeOrigin: true,
      },
      // 添加供应商接口代理
      "/srm": {
        target: "https://srm-uat-main.eads.tcl.com",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/srm/, ""),
        configure: (proxy) => {
          proxy.on('proxyReq', (proxyReq) => {
            // 添加 SRM 系统的 Cookie
            const srmCookie = 'system=; token-localhost=%22eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.e30.kouGI1Ps93NC6i8PzMQN1Kln46lKozD1KQ2fsDZyHKo%22; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%2200147285%2CTV%22%2C%22first_id%22%3A%221985abb548a2ce-0b1061c3bd6b3f-17525636-3686400-1985abb548b1cdb%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22url%E7%9A%84domain%E8%A7%A3%E6%9E%90%E5%A4%B1%E8%B4%A5%22%2C%22%24latest_search_keyword%22%3A%22url%E7%9A%84domain%E8%A7%A3%E6%9E%90%E5%A4%B1%E8%B4%A5%22%2C%22%24latest_referrer%22%3A%22url%E7%9A%84domain%E8%A7%A3%E6%9E%90%E5%A4%B1%E8%B4%A5%22%7D%2C%22%24device_id%22%3A%221985abb548a2ce-0b1061c3bd6b3f-17525636-3686400-1985abb548b1cdb%22%7D';
            proxyReq.setHeader('Cookie', srmCookie);
          });
        },
      },
      "/api": {
        target: proxyAPI,
        changeOrigin: true,
      },
    },
  },
});
