# UiTable 组件最佳实践

## 概述

UiTable 是项目中的通用表格组件，为了保持组件的通用性和可维护性，业务逻辑应该放在具体的业务组件中，而不是在 UiTable 组件内部。

## 架构原则

### ✅ 正确做法：业务逻辑在业务组件中

```vue
<!-- 业务组件：weekly-supplier-capacity/index.vue -->
<template>
  <UiTable
    :show-confirm="true"
    :show-invalid="true"
    @delete="handleDelete"
    @confirm="handleConfirm"
    @invalid="handleInvalid"
  />
</template>

<script setup>
  // 删除操作 - 包含业务状态检查
  const handleDelete = async selectedRows => {
    // 业务逻辑：检查选中行的状态，只有草稿才能删除
    const selectedStatuses = selectedRows.map(row => row.status)
    const hasNonDraftStatus = selectedStatuses.some(status => status !== 1)

    if (hasNonDraftStatus) {
      message.warning('只有草稿状态的数据才能删除')
      return
    }
    
    // 确认对话框和API调用
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除选中的 ${selectedRows.length} 条记录吗？`,
      onOk: async () => {
        // 调用删除API
      }
    })
  }

  // 确认操作 - 包含业务状态检查
  const handleConfirm = async selectedRows => {
    // 业务逻辑：只有草稿和采方待确认才能点确认
    const selectedStatuses = selectedRows.map(row => row.status)
    const hasInvalidStatus = selectedStatuses.some(status => status !== 1 && status !== 2)

    if (hasInvalidStatus) {
      message.warning('只有草稿和采方待确认状态的数据才能确认')
      return
    }
    
    // 确认对话框和API调用
  }
</script>
```

### ❌ 错误做法：业务逻辑在通用组件中

```vue
<!-- 通用组件：UiTable.vue -->
<script setup>
  const handleDelete = () => {
    // ❌ 不应该在通用组件中包含特定业务逻辑
    const selectedStatuses = tableSelectValue.value.map(row => row.status)
    const hasNonDraftStatus = selectedStatuses.some(status => status !== 1)
    
    if (hasNonDraftStatus) {
      notification.warning({
        message: '只有草稿状态的数据才能删除', // ❌ 硬编码的业务规则
      })
      return
    }
  }
</script>
```

## 重构说明

### 已完成的重构

1. **移除 UiTable 中的状态检查**：
   - 删除了删除操作的状态检查
   - 删除了确认操作的状态检查  
   - 删除了失效操作的状态检查

2. **简化 UiTable 职责**：
   - 只负责基础的选择检查（是否选中了行）
   - 将具体的业务逻辑通过事件传递给业务组件

3. **业务组件承担业务逻辑**：
   - 各业务组件在自己的事件处理函数中实现状态检查
   - 根据自己的业务规则决定操作是否可执行

### 优势

1. **通用性**：UiTable 可以被任何业务场景复用
2. **可维护性**：业务逻辑集中在业务组件中，便于维护
3. **灵活性**：不同业务可以有不同的状态检查规则
4. **测试性**：业务逻辑和UI逻辑分离，便于单独测试

## 按钮控制

对于不需要某些操作的页面，使用 props 控制按钮显示：

```vue
<UiTable
  :show-confirm="false"  <!-- 隐藏确认按钮 -->
  :show-invalid="false"  <!-- 隐藏失效按钮 -->
  :show-delete="true"    <!-- 显示删除按钮 -->
/>
```

## 状态码约定

建议在项目中统一状态码的定义：

```javascript
// 状态枚举
export const STATUS = {
  DRAFT: 1,              // 草稿
  WAITING_CONFIRM: 2,    // 采方待确认
  REJECT: 3,             // 采方已拒绝
  CONFIRMED: 4,          // 采方已确认
  INVALID: 5,            // 失效
}
```

## 总结

通过将业务逻辑从通用组件中分离出来，我们实现了：
- 更好的代码组织结构
- 更高的组件复用性
- 更清晰的职责分离
- 更容易的维护和扩展

这种架构模式应该在整个项目中保持一致。
