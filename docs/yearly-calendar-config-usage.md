# 年度日历配置组件使用说明

## 概述

年度日历配置组件已成功从Element UI迁移到Ant Design Vue，并适配了项目的Vue 3 + Composition API架构。

## 组件功能

### 主要功能
1. **年度月份选择**：显示12个月份，每个月显示工作天数
2. **月度日历配置**：可以点击具体日期设置工作日/休息日
3. **BU和供应商筛选**：支持按BU和供应商进行数据筛选
4. **数据保存**：支持保存工作日配置到后端

### 组件结构
```
yearly-calender-config/
├── index.vue              # 主组件
├── components/
│   ├── YearCalendar.vue   # 年度月份选择组件
│   └── MonthCalendar.vue  # 月度日历配置组件
└── api.js                 # API接口
```

## 技术改造

### 1. Vue版本升级
- 从 `@vue/composition-api` 升级到 Vue 3 原生 Composition API
- 移除了 `getCurrentInstance()` 的使用
- 使用标准的 `emit` 和 `ref` 语法

### 2. UI组件迁移
**Element UI → Ant Design Vue**
- `el-form` → `a-form`
- `el-form-item` → `a-form-item`
- `el-select` → `a-select`
- `el-option` → `a-select-option`
- `el-date-picker` → `a-date-picker`
- `el-button` → `a-button`
- `v-loading` → `a-spin`

### 3. API请求方式
- 从自定义axios实例改为项目统一的 `CustomRequest`
- 统一错误处理和消息提示

### 4. 消息提示
- 从 `proxy.$error` / `proxy.$success` 改为 `message.error` / `message.success`

## 访问方式

组件已添加到路由中，可通过以下路径访问：
```
/yearly-calender-config
```

## API接口

### 1. 获取年度月份工作天数
```javascript
getYearMonths({
  buCode: 'ALL',
  supplierCode: 'ALL', 
  year: 2025
})
```

### 2. 获取月度工作日详情
```javascript
getMonthDays({
  buCode: 'ALL',
  month: '2025-01',
  supplierCode: 'ALL',
  year: 2025
})
```

### 3. 保存月度工作日配置
```javascript
saveMonthDays({
  buCode: 'ALL',
  adjustDayList: [
    { dateDay: '2025-01-01', isHoliday: '1' },
    { dateDay: '2025-01-02', isHoliday: '0' }
  ],
  month: '2025-01',
  supplierCode: 'ALL',
  year: 2025
})
```

## 使用示例

```vue
<template>
  <div class="calendar-page">
    <YearlyCalenderConfig />
  </div>
</template>

<script>
import YearlyCalenderConfig from '@/views/yearly-calender-config/index.vue'

export default {
  components: {
    YearlyCalenderConfig
  }
}
</script>
```

## 注意事项

1. **数据格式**：确保API返回的数据格式与组件期望的格式一致
2. **权限控制**：根据需要添加相应的权限控制
3. **样式调整**：可能需要根据项目整体风格调整样式
4. **错误处理**：已集成项目统一的错误处理机制

## 后续优化建议

1. 添加加载状态优化
2. 增加数据验证
3. 支持批量操作
4. 添加快捷设置功能（如一键设置工作日模式）
