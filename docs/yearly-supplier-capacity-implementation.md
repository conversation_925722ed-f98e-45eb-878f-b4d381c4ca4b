# 年度供应商配额&日产能维护页面实现文档

## 概述

已成功完成 yearly-supplier-capacity（供应商配额&日产能维护）页面的开发，该页面参考了项目中其他页面的交互模式，使用了统一的 UiTable 组件。

## 实现的功能

### 1. 查询条件

- **年份**：多选下拉框，通过 `getYear()` API 动态获取年份列表
- **产品类型**：多选下拉框，通过 `getProductType()` API 动态获取产品类型列表
- **尺寸分档**：多选下拉框，通过 `getSizeSegment()` API 动态获取尺寸分档列表
- **供应商**：多选下拉框，通过 `getSuppliers()` API 动态获取供应商列表

### 2. 表格列字段

根据用户提供的表格图片，实现了以下列：

- 年份
- 产品类型
- 尺寸分档
- 供应商
- 比例（显示为百分比格式）
- 单机用量
- 类型
- 型号
- 日产能
- 创建人
- 创建时间
- 更新人
- 更新时间

### 3. 操作功能

- **查询**：支持条件查询和重置
- **刷新**：刷新表格数据
- **删除**：批量删除选中记录
- **导入**：支持 Excel 文件导入
- **导出**：支持数据导出为 Excel
- **下载模板**：下载导入模板

## 文件结构

```
src/views/yearly-supplier-capacity/
├── index.vue          # 主页面组件
├── api.js            # API接口定义
└── enum.js           # 枚举常量定义
```

## 技术实现

### 1. 组件架构

- 使用 Vue 3 Composition API
- 采用项目统一的 UiTable 组件
- 遵循项目现有的代码规范和结构

### 2. API 设计

主要 API 接口：

**数据操作接口：**

- `getYearlySupplierCapacityList()` - 获取列表数据
- `deleteYearlySupplierCapacityApi()` - 删除数据
- `importYearlySupplierCapacityApi()` - 导入数据
- `exportYearlySupplierCapacityApi()` - 导出数据
- `downloadYearlySupplierCapacityTemplateApi()` - 下载模板

**查询条件数据接口：**

- `getYear()` - 获取年份选项列表
- `getProductType()` - 获取产品类型选项列表
- `getSizeSegment()` - 获取尺寸分档选项列表
- `getSuppliers()` - 获取供应商选项列表（来自 @/api/weekly）

### 3. 状态管理

- 使用响应式数据管理表格状态
- 支持分页功能
- 统一的错误处理和消息提示

### 4. 动态选项加载

所有查询条件都采用动态加载方式：

- **年份、产品类型、尺寸分档**：通过对应的 API 接口获取选项数据
- **数据映射**：自动处理不同的数据结构（name/code、label/value 等）
- **错误处理**：API 调用失败时返回空数组，确保页面正常显示
- **异步加载**：使用 Promise 方式加载选项，支持异步渲染

### 5. 样式设计

- 使用 Ant Design Vue 组件库
- 遵循项目统一的 UI 设计规范
- 支持响应式布局

## 路由配置

页面已正确配置在路由系统中：

- 路径：`/yearly-supplier-capacity`
- 菜单位置：年度供需平衡测算 > 供应商配额&日产能维护

## 测试数据

为了便于测试，在 API 文件中添加了模拟数据，包含 3 条测试记录，展示了不同状态和数据格式。

## 使用方式

1. 启动项目：`npm run serve`
2. 访问：`http://localhost:1025/yearly-supplier-capacity`
3. 或通过菜单导航：年度供需平衡测算 > 供应商配额&日产能维护

## 后续优化建议

1. **年份选择器优化**：可考虑扩展 UiTable 组件支持年份选择器类型
2. **数据验证**：添加表单数据验证规则
3. **权限控制**：根据用户权限控制操作按钮显示
4. **批量操作优化**：优化批量操作的用户体验
5. **API 集成**：将模拟数据替换为真实 API 调用

## 注意事项

- 目前使用模拟数据进行测试，实际部署时需要替换为真实 API
- 供应商列表通过`getSuppliers()`动态获取
- 所有操作都包含确认对话框和成功/失败提示
- **重要**：此页面没有状态字段，已移除所有状态相关功能（状态查询、确认、失效操作等）

## 按钮控制配置

如果页面不需要确认和失效按钮，可以通过以下方式配置：

```vue
<UiTable
  :show-confirm="false"
  :show-invalid="false"
  <!-- 其他props -->
/>
```

### 可控制的按钮

UiTable 组件支持以下按钮的显示控制：

- `show-confirm` - 控制确认按钮显示（默认：true）
- `show-invalid` - 控制失效按钮显示（默认：true）
- `show-import` - 控制导入按钮显示（默认：true）
- `show-export` - 控制导出按钮显示（默认：true）
- `show-delete` - 控制删除按钮显示（默认：true）
- `show-refresh` - 控制刷新按钮显示（默认：true）
