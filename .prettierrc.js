module.exports = {
  // 基本格式化选项
  semi: false,                    // 不使用分号
  singleQuote: true,             // 使用单引号
  quoteProps: 'as-needed',       // 仅在需要时给对象属性加引号
  trailingComma: 'es5',          // 在ES5中有效的尾随逗号（对象、数组等）
  bracketSpacing: true,          // 对象花括号内加空格 { foo: bar }
  bracketSameLine: false,        // 将>放在下一行
  arrowParens: 'avoid',          // 箭头函数参数只有一个时不加括号
  
  // 缩进和换行
  tabWidth: 2,                   // 缩进宽度
  useTabs: false,                // 使用空格而不是tab
  printWidth: 100,               // 每行最大字符数
  endOfLine: 'lf',               // 换行符使用 lf
  
  // Vue特定配置
  vueIndentScriptAndStyle: true, // Vue文件中的script和style标签缩进
  
  // HTML相关
  htmlWhitespaceSensitivity: 'css', // HTML空白敏感性
  
  // 覆盖特定文件类型的配置
  overrides: [
    {
      files: '*.vue',
      options: {
        parser: 'vue',
        printWidth: 120,           // Vue文件可以稍微长一些
      },
    },
    {
      files: ['*.json', '*.jsonc'],
      options: {
        printWidth: 200,
        trailingComma: 'none',
      },
    },
    {
      files: '*.md',
      options: {
        printWidth: 80,
        proseWrap: 'preserve',
      },
    },
  ],
}
