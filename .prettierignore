# 依赖和构建产物
node_modules/
dist/
build/
.vite/
.nuxt/
.next/
.svelte-kit/

# 配置文件
*.config.js
*.config.ts
vite.config.*
vitest.config.*
cypress.config.*
playwright.config.*

# 包管理器文件
package-lock.json
yarn.lock
pnpm-lock.yaml

# 环境文件
.env
.env.*

# 日志文件
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# 系统文件
.DS_Store
Thumbs.db

# IDE文件
.vscode/
.idea/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# 测试覆盖率
coverage/
*.lcov

# 自动生成的文件
auto-imports.d.ts
components.d.ts
src/vite-env.d.ts

# 公共资源
public/
static/

# 文档
CHANGELOG.md
LICENSE
README.md

# 特定格式文件
*.min.js
*.min.css
