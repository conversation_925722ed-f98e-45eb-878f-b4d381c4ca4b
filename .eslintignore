# 依赖目录
node_modules/
dist/
build/

# 配置文件
*.config.js
*.config.ts
vite.config.*
vitest.config.*
cypress.config.*
playwright.config.*

# 构建产物
/dist
/build
/.vite
/.nuxt
/.next
/.svelte-kit

# 环境文件
.env
.env.*

# 日志文件
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# 临时文件
.DS_Store
.vscode/
.idea/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# 测试覆盖率
coverage/
*.lcov

# 自动生成的文件
auto-imports.d.ts
components.d.ts

# 公共资源
public/
static/

# 特定文件
src/vite-env.d.ts
