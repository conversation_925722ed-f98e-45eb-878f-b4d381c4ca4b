<template>
  <div id="app">
    <a-config-provider :locale="zh_CN" :auto-insert-space-in-button="false" :getPopupContainer="getPopupContainer">
      <router-view />
    </a-config-provider>
  </div>
</template>

<script>
  import zh_CN from 'ant-design-vue/lib/locale-provider/zh_CN'
  import { userStore } from '@/stores'
  import { Tracking } from '@ea/ai-sdk'
  import { getDeployEnv } from '@/utils'

  export default {
    name: 'App',
    data() {
      return {
        zh_CN,
      }
    },
    beforeMount() {
      const { userInfo } = userStore()
      Tracking.init({
        env: getDeployEnv(),
        project_id: '1050',
        project_name: '采购供需平衡助手',
        oa: userInfo.username || '',
      })
    },
    methods: {
      getPopupContainer(node) {
        if (node) {
          return node.parentNode
        }
        return document.body
      },
    },
  }
</script>

<style>
  #app {
    width: 100%;
    height: 100%;
    /* display: flex; */
  }
</style>
