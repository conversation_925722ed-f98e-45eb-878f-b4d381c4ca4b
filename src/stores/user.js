import { defineStore } from 'pinia'
import { getTclAuth } from '@/api/chat'
import {
  loginInAiPlatformApi,
  loginInPaasApi,
  loginInIdmApi,
  getUserInfoApi,
  loginInTxinApi,
} from '@/api/user'
import { message } from 'ant-design-vue'
import { createExpiresStorage } from '@/utils/storage'
import { Tracking } from '@ea/ai-sdk'

// token过期时间 1天
const EXPIRES_TIME = 1 * 24 * 60 * 60 * 1000

export default defineStore('user', {
  state: () => ({
    leaderNameCn: '',
    leaderUserAccount: '',
    staffId: '',
    uid: '',
    upDirectlyLeaderId: '',
    username: '',
    tclToken: '',
    appCode: '',
    formAppCode: '', // 表单专用
    formAppToken: {},
    auth: '',
    token: '',
    userInfo: {},
    from: '',
  }),
  actions: {
    clearUserInfo() {
      this.token = ''
      this.userInfo = {}
    },
    setToken(token) {
      this.token = token
    },
    setFrom(from) {
      this.from = from
    },
    setTclToken(tclToken) {
      this.tclToken = tclToken
    },
    setTclAppCode(appCode) {
      this.appCode = appCode
    },
    setFormAppCode(appCode) {
      this.formAppCode = appCode
    },
    getFormTokenByCode(appCode) {
      console.log('formAppToken', this.formAppToken, appCode)
      return this.formAppToken[appCode] || ''
    },
    getFormAppToken() {
      if (this.tclToken && this.formAppCode) {
        getTclAuth(this.tclToken, this.formAppCode).then(res => {
          this.formAppToken[this.formAppCode] = res.data?.access_token || ''
        })
      }
    },
    // 原来的用户信息
    // getUserInfo() {
    //   return new Promise((resolve, inject) => {
    //     if (this.uid) {
    //       resolve(this.$state);
    //     } else {
    //       getTclAuth(this.tclToken, this.appCode)
    //         .then((res) => {
    //           if (res.data && res.data.user) {
    //             Object.keys(this.$state).forEach((key) => {
    //               if (res.data.user[key]) {
    //                 this[key] = res.data.user[key];
    //               }
    //             });
    //             resolve(res.data);
    //           } else {
    //             throw new Error(res.msg);
    //           }
    //         })
    //         .catch((err) => {
    //           inject(err);
    //         });
    //     }
    //   });
    // },
    async getUserInfo() {
      try {
        const res = await getUserInfoApi()
        if (!res) {
          throw Error()
        }
        this.userInfo = res
        Tracking.setOA(res.username)
      } catch (error) {
        return Promise.reject(error)
      }
    },
    async actionLogin(data = {}) {
      try {
        let res
        if (data.password) {
          // 账号密码登录
          res = await loginInAiPlatformApi(data)
        } else if (data.aiPlatformToken) {
          // AI平台token
          res = {
            access_token: data.aiPlatformToken,
          }
        } else if (data.assistantToken) {
          // paas token
          res = await loginInPaasApi({ assistantToken: data.assistantToken })
        } else if (data.isIdm) {
          // TGT token
          res = await loginInIdmApi({ client_id: 'ai-client' })
        } else if (data.TXinToken) {
          res = await loginInTxinApi({
            token: data.TXinToken,
            client_id: 'ai-client',
          })
        }
        if (!res || !res.access_token) {
          const defaultMsg = data.assistantToken || data.isIdm ? '免登录失败' : '登录失败'
          const errorMsg = res?.message || defaultMsg
          message.error(errorMsg)
          throw Error(errorMsg)
        }
        this.token = res.access_token
        await this.getUserInfo()
      } catch (error) {
        return Promise.reject(error)
      }
    },
  },
  persist: {
    enabled: true,
    strategies: [
      {
        key: 'ai-purchase-user-token-flag',
        storage: createExpiresStorage(EXPIRES_TIME),
        paths: ['token', 'userInfo', 'from'],
      },
    ],
  },
})
