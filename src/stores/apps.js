import { defineStore } from 'pinia'

export default defineStore('apps', {
  state: () => ({
    curAppCode: [],
    appTokenMap: {},
  }),
  actions: {
    setCurAppCode(appCode) {
      this.curAppCode = appCode
    },
    setAppToken(appCode, token){
      this.appTokenMap[appCode] = token
      localStorage.setItem(appCode, token)
    },
    clearApps () {
      this.curAppCode = ''
      this.appTokenMap = {}
    },
    getCurAppToken() {
      if(this.curAppCode)
      return this.appTokenMap[this.curAppCode]
    return ''
    },
    getAppToken(appCode) {
      if(appCode){
        const cacheToken = this.appTokenMap[appCode]
        const token = localStorage.getItem(appCode)
        if(token && !cacheToken) {
          this.appTokenMap[appCode] = token
        }
      return cacheToken || token || ''
      }
        
    return ''
    }
  }
})
