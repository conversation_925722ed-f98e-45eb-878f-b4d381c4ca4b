import { update } from 'lodash-es'
import { defineStore } from 'pinia'
import { userStore } from '@/stores'
import { Modal } from 'ant-design-vue'
import dayjs from 'dayjs'
import { estimateLocalStorageSize } from '@/utils/chart.js'
import { putDataDB, getDataDB } from '@/utils/indexDB.js'
import { cloneDeep } from 'lodash-es'

import { getUrlParamObj } from '@/utils/common.js'

async function updateData(list) {
  const uid = userStore()?.userInfo?.username || 'chat-bi-history'
  //按照时间倒序
  list.sort(
    (a, b) =>
      (b?.updateTime || b?.list[0]?.updateTime || b?.created_at || b?.list[0]?.created_at) -
      (a?.updateTime || a?.list[0]?.updateTime || a?.created_at || a?.list[0]?.created_at)
  )
  localStorage.setItem(uid, JSON.stringify(list))
  // putDataDB(cloneDeep(list));
  //todo,调接口保存
}

function initHistoryChatByUid() {
  const uid = userStore()?.userInfo?.username || 'chat-bi-history'
  // getDataDB(uid);
  return localStorage.getItem(uid) ? JSON.parse(localStorage.getItem(uid)) : []
}
function checkFailStatus(list) {
  if (list?.length) {
    let lastChat = list[list?.length - 1]
    if (lastChat && lastChat?.query && Number(lastChat?.sendStatus === 0)) {
      //最后一个chat有提问，lastChat.sendStatus!==1或者没有answer,则状态改为2存下来，下次进去可以重试
      lastChat.sendStatus = 2
      lastChat.errorMsg = '请重试'
    }
  }
}
function getRecentChat() {
  const uid = userStore()?.userInfo?.username || 'chat-bi-history'
  if (!localStorage.getItem(uid)) {
    return null
  } else {
    let arr = JSON.parse(localStorage.getItem(uid))
    const list = arr[0]?.list
    checkFailStatus(list)
    return arr[0]
  }
}

export default defineStore('history', {
  state: () => ({
    historyChatByUid: initHistoryChatByUid(),
    recentChat: getRecentChat(),

    talkChat: '',
    talkError: '',
    talkTimeout: '',
    talkNoAuth: '',
    qsModel: 0, //0：普通提问，1：追问模式，2：归因分析
    showGraphNum: false, //图上显示数据标签
    datasetId: '', //知识库id,提交点赞点踩的记录需要,在basic接口中返回
    shareImging: false, //分享图片中，勾选状态
    dimensionEnumMap: null, //当前主题域下，所有model，所有维度的下拉枚举值
    enableAnalysisReportData: false, //是否开启数据分析
    permission: {
      isFilter: 1, // 筛选条件是否隐藏 0 开启 1 不开启
      isAppend: 1, // 追问模式 0 开启  1 不开启
      enableDataInsight: false, //是否开启智能报告
    },
  }),
  actions: {
    setEnableAnalysisReportData(enable) {
      this.enableAnalysisReportData = enable
    },
    setEnableDataInsight(enable) {
      this.enableDataInsight = enable
    },
    setPermission(permission) {
      this.permission = permission
    },
    setDimensionEnumMap(dimensionEnum) {
      const map = new Map() //key:modelId&dimensionName (移除domainId依赖)
      Object.keys(dimensionEnum).forEach(modelId => {
        Object.keys(dimensionEnum[modelId]).forEach(dimensionName => {
          const list = dimensionEnum[modelId][dimensionName] || []
          const key = `${modelId}&${dimensionName}` // 移除domainId
          map.set(key, list)
        })
      })
      this.dimensionEnumMap = map
    },
    setAppendQsModel(model) {
      this.qsModel = model
    },

    setTalkScript(talkList, showNum) {
      //报错-1001,闲聊,1003,超时,1005,没有权限
      this.talkChat = talkList.find(i => i.talkType == 'chat')?.talkMessage || ''
      this.talkError = talkList.find(i => i.talkType == 'error')?.talkMessage || ''
      this.talkTimeout = talkList.find(i => i.talkType == 'timeout')?.talkMessage || ''
      this.talkNoAuth =
        talkList.find(i => i.talkType == 'noAuth')?.talkMessage ||
        '你好，你当前没有数据权限，请联系对接IT开通数据权限'
      this.showGraphNum = showNum || false
    },
    setDatasetId(id) {
      this.datasetId = id
    },

    setShareImging(share) {
      this.shareImging = share
    },
    //增删改查，
    updateHistoryChat(list, newChat) {
      //新增或修改历史会话
      const itemIndex = this.historyChatByUid?.findIndex(i => i.chatId == list[0]?.msgId)
      if (itemIndex > -1) {
        //更新list,保留title
        this.historyChatByUid[itemIndex].title = list[0]?.query
        this.historyChatByUid[itemIndex].list = list
        this.historyChatByUid[itemIndex].updateTime = dayjs().unix()
        this.historyChatByUid[itemIndex].deepThinking = list[0]?.deepThinking
        if (!newChat) {
          const totalSize = estimateLocalStorageSize()
          if (this.historyChatByUid?.length > 1 && totalSize > 4718592) {
            // // 1MB = 1024 × 1024 字节,4718592约为4.5M,489362
            //看数据条数，count=storage.length/2向下取整,如果>3则取3
            Modal.confirm({
              title: '提示',
              content: '会话超过限制，是否删除部分历史会话？',
              okText: '确定',
              cancelText: '取消',
              onOk: () => {
                var count = Math.floor(this.historyChatByUid.length / 2)
                if (count > 3) count = 3
                this.historyChatByUid.sort(
                  (a, b) =>
                    (b?.updateTime ||
                      b?.list[0]?.updateTime ||
                      b?.created_at ||
                      b?.list[0]?.created_at) -
                    (a?.updateTime ||
                      a?.list[0]?.updateTime ||
                      a?.created_at ||
                      a?.list[0]?.created_at)
                )
                //先排序再删除
                this.historyChatByUid.splice(-count)
              },
            })
          }
        }
      } else {
        //新增
        const item = {
          title: list[0]?.query,
          chatId: list[0]?.msgId,
          list: list,
          updateTime: dayjs().unix(),
          deepThinking: list[0]?.deepThinking || false,
        }
        this.historyChatByUid.push(item)
      }
      updateData(this.historyChatByUid)
      this.recentChat = getRecentChat()
    },
    queryAll() {
      //默认进去系统查询所有
      return this.historyChatByUid
    },
    queryAllTitle() {
      if (this.historyChatByUid?.length) {
        return this.historyChatByUid.map(i => {
          return {
            title: i?.title || i[0]?.query,
            chatId: i?.chatId || i[0]?.msgId,
          }
        })
      }
      return []
    },
    queryListByChatId(chatId) {
      //点击历史菜单，查询对应的历史详情
      const item = this.historyChatByUid.find(i => i?.chatId == chatId)
      const list = item?.list || []
      checkFailStatus(list)
      return list
    },
    reNameListByChatId(chatId, name) {
      //重新命名
      const item = this.historyChatByUid.find(i => i?.chatId == chatId)
      item.title = name
      updateData(this.historyChatByUid)
    },
    deleteHistoryByChatId(chatId) {
      //点击删除某个会话
      const idx = this.historyChatByUid.findIndex(i => i?.chatId == chatId)
      if (idx > -1) {
        this.historyChatByUid?.splice(idx, 1)
        updateData(this.historyChatByUid)
        this.recentChat = getRecentChat()
      }
    },
    deleteAllHistory() {
      //清空全部历史
      this.historyChatByUid = []
      updateData([])
      this.recentChat = getRecentChat()
    },
    updateHistoryChatLike(msgId, like, otherParams) {
      let obj = this.historyChatByUid?.find(ite => {
        return ite.list?.find(i => {
          return i?.msgId == msgId
        })
      })
      let item = obj?.list?.find(i => i?.msgId == msgId)
      item.like = like
      item.likeOtherParams = otherParams
      updateData(this.historyChatByUid)
      this.recentChat = getRecentChat()
    },
    updateHistoryChatShareTableTab(msgId, selectedName, index) {
      let obj = this.historyChatByUid?.find(ite => {
        return ite.list?.find(i => {
          return i?.msgId == msgId
        })
      })
      let item = obj?.list?.find(i => i?.msgId == msgId)
      let res = item?.answer?.analysisData[index]
      if (res?.key?.includes('Reach')) {
        item.selectReachChart = selectedName
      } else {
        item.selectYoyChart = selectedName
      }
      updateData(this.historyChatByUid)
      this.recentChat = getRecentChat()
    },
    addNewChat(chat) {
      this.historyChatByUid?.unshift(chat)
      updateData(this.historyChatByUid)
      this.recentChat = getRecentChat()
    },
  },
})
