// install pinia
import { createPinia } from "pinia";
import piniaPersist from "pinia-plugin-persist";
const pinia = createPinia();
const install = (app) => {
  pinia.use(piniaPersist);
  app.use(pinia);
};
export default { install };

// 引入stores
import useAppsStore from "./apps";
import userStore from "./user";
import historyStore from "./history";

export { useAppsStore, userStore, historyStore };
