// useWindowSize.ts - 增强版
import { ref } from "vue";

export default function useWindowSize() {
  const windowWidth = ref(window.innerWidth);
  const windowHeight = ref(window.innerHeight);
  
  // 使用更准确的移动设备检测方法
  const checkIsMobile = () => {
    const userAgent = navigator.userAgent.toLowerCase();
    const mobileKeywords = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini|mobile/;
    const isMobileUA = mobileKeywords.test(userAgent);
    
    return isMobileUA
  }

  const isMobile = ref(checkIsMobile())

  const update = () => {
    windowWidth.value = window.innerWidth
    windowHeight.value = window.innerHeight
    isMobile.value = checkIsMobile()
  }

  window.addEventListener('resize', update)
  window.addEventListener('orientationchange', update)


  return {
    windowWidth,
    windowHeight,
    isMobile,
  };
}
