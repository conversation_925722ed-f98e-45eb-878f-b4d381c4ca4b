/* Ant Design Modal 全局样式覆盖 */

/* Modal 整体样式 */
.ant-modal {
  .ant-modal-content {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12),
      0 9px 28px 8px rgba(0, 0, 0, 0.05);
  }

  .ant-modal-header {
    background: #fff;
    border-bottom: 1px solid #f0f0f0;
    padding: 16px 24px;
    border-radius: 8px 8px 0 0;

    .ant-modal-title {
      font-size: 16px;
      font-weight: 500;
      color: #262626;
      line-height: 22px;
    }
  }

  .ant-modal-body {
    padding: 24px;
    color: #262626;
    font-size: 14px;
    line-height: 1.5715;
  }

  .ant-modal-footer {
    padding: 10px 16px;
    text-align: right;
    background: transparent;
    border-top: 1px solid #f0f0f0;
    border-radius: 0 0 8px 8px;

    .ant-btn + .ant-btn:not(.ant-dropdown-trigger) {
      margin-left: 8px;
    }

    .ant-btn {
      border-radius: 6px;
      height: 32px;
      padding: 4px 15px;
      font-size: 14px;
      font-weight: 400;

      &.ant-btn-default {
        color: rgba(0, 0, 0, 0.88);
        border: 1px solid #d9d9d9;
        background: #fff;

        &:hover {
          color: #4096ff;
          border-color: #4096ff;
        }

        &:focus {
          color: #4096ff;
          border-color: #4096ff;
          outline: 0;
          box-shadow: 0 0 0 2px rgba(5, 145, 255, 0.1);
        }
      }

      &.ant-btn-primary {
        color: #fff;
        background: #1677ff;
        border-color: #1677ff;

        &:hover {
          color: #fff;
          background: #4096ff;
          border-color: #4096ff;
        }

        &:focus {
          color: #fff;
          background: #4096ff;
          border-color: #4096ff;
          outline: 0;
          box-shadow: 0 0 0 2px rgba(5, 145, 255, 0.1);
        }

        &.ant-btn-loading {
          opacity: 0.65;
        }
      }
    }
  }

  .ant-modal-close {
    position: absolute;
    top: 17px;
    right: 17px;
    z-index: 10;
    padding: 0;
    color: rgba(0, 0, 0, 0.45);
    font-weight: 700;
    line-height: 1;
    text-decoration: none;
    background: transparent;
    border: 0;
    outline: 0;
    cursor: pointer;
    transition: color 0.2s;

    &:hover {
      color: rgba(0, 0, 0, 0.75);
    }

    .ant-modal-close-x {
      display: block;
      width: 22px;
      height: 22px;
      font-size: 16px;
      font-style: normal;
      line-height: 22px;
      text-align: center;
      text-transform: none;
      text-rendering: auto;
    }
  }
}

/* Modal 确认框样式 */
.ant-modal-confirm {
  .ant-modal-header {
    display: none;
  }

  .ant-modal-body {
    padding: 32px 32px 24px;

    .ant-modal-confirm-body-wrapper {
      .ant-modal-confirm-body {
        .ant-modal-confirm-title {
          display: block;
          overflow: hidden;
          color: rgba(0, 0, 0, 0.88);
          font-weight: 600;
          font-size: 16px;
          line-height: 1.4;

          &::before {
            content: '';
            width: 0;
            height: 0;
            visibility: hidden;
          }
        }

        .ant-modal-confirm-content {
          margin-top: 8px;
          color: rgba(0, 0, 0, 0.65);
          font-size: 14px;
        }
      }

      .ant-modal-confirm-btns {
        margin-top: 32px;
        text-align: right;

        .ant-btn + .ant-btn {
          margin-left: 8px;
        }
      }
    }
  }

  &.ant-modal-confirm-error {
    .ant-modal-confirm-body {
      .anticon {
        color: #ff4d4f;
      }
    }
  }

  &.ant-modal-confirm-warning {
    .ant-modal-confirm-body {
      .anticon {
        color: #faad14;
      }
    }
  }

  &.ant-modal-confirm-info {
    .ant-modal-confirm-body {
      .anticon {
        color: #1677ff;
      }
    }
  }

  &.ant-modal-confirm-success {
    .ant-modal-confirm-body {
      .anticon {
        color: #52c41a;
      }
    }
  }
}

/* 响应式样式 */
@media (max-width: 768px) {
  .ant-modal {
    max-width: calc(100vw - 16px);
    margin: 8px auto;

    .ant-modal-content {
      border-radius: 0;
    }

    .ant-modal-header {
      padding: 16px;
    }

    .ant-modal-body {
      padding: 16px;
    }

    .ant-modal-footer {
      padding: 10px 16px;
    }
  }

  .ant-modal-confirm {
    .ant-modal-body {
      padding: 24px 16px 16px;
    }
  }
}

/* 加载状态优化 */
.ant-modal-body {
  .ant-spin-nested-loading {
    .ant-spin-container {
      transition: opacity 0.3s;
    }

    .ant-spin {
      .ant-spin-dot {
        font-size: 20px;
      }

      .ant-spin-text {
        color: rgba(0, 0, 0, 0.45);
        font-size: 14px;
        margin-top: 8px;
      }
    }
  }
}

/* Modal 遮罩层优化 */
.ant-modal-mask {
  background: rgba(0, 0, 0, 0.45);
  backdrop-filter: blur(4px);
}

/* Modal 动画优化 */
.ant-modal {
  &.zoom-enter-active,
  &.zoom-leave-active {
    transition: all 0.3s;
  }

  &.zoom-enter-from,
  &.zoom-leave-to {
    opacity: 0;
    transform: scale(0.2);
  }
}

/* 自定义宽度的 Modal */
.ant-modal.modal-sm {
  .ant-modal-content {
    width: 416px;
  }
}

.ant-modal.modal-md {
  .ant-modal-content {
    width: 632px;
  }
}

.ant-modal.modal-lg {
  .ant-modal-content {
    width: 880px;
  }
}

.ant-modal.modal-xl {
  .ant-modal-content {
    width: 1200px;
  }
}

/* Modal 内的 svg-icon 通用样式 */
.ant-modal {
  .svg-icon {
    cursor: pointer;
    transition: all 0.2s ease;
    border-radius: 4px;

    &:hover {
      background-color: rgba(0, 0, 0, 0.04);
      transform: scale(1.1);
    }

    &:active {
      background-color: rgba(24, 144, 255, 0.1);
      transform: scale(0.95);
    }

    /* 特定类型的图标样式 */
    &.reload-icon {
      padding: 4px;

      &:hover {
        background-color: #f5f5f5;
        transform: rotate(180deg);
      }
    }

    &.close-icon {
      &:hover {
        background-color: #ff4d4f;
        color: #fff;
      }
    }

    &.edit-icon {
      &:hover {
        background-color: #e6f7ff;
        color: #1890ff;
      }
    }
  }
}
