.ant-input-affix-wrapper {
  // background-color: #f6f7fb;
  // border-radius: 8px;
  // border: none;
  // padding: 6px 8px;
  // height: 32px;
  // line-height: 32px;
  border-radius: 4px;
}

.ant-input-affix-wrapper > input.ant-input {
  // background-color: #f6f7fb;
  // color: #333;
}

.ant-input {
  // background-color: #f6f7fb;
  // border-radius: 8px;
  // border: none;
  // padding: 6px 8px;
  // color: #333;
}
.ant-picker {
  // background-color: #f6f7fb;
  border-radius: 6px;
  // border: none;
  // padding: 6px 8px;
  // height: 32px;
  // line-height: 32px;
}
.ant-select:not(.ant-select-customize-input) .ant-select-selector {
  border-radius: 4px;
}

// .ant-input-affix-wrapper:focus, .ant-input-affix-wrapper-focused {
//     box-shadow: 0 0 0 1px rgba(99, 85, 255, 0.2);
// }
