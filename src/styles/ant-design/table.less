.ant-table {
  font-size: 12px;
  // background-color: #f6f7fb;
  border: 1px solid rgba(153, 153, 153, 0.1);
  border-bottom: none;
  border-radius: 6px;
  // 头部样式
  &-thead tr > th {
    height: 40px;
    padding: 6px 10px;
    background: #f1f4f9;
    // box-shadow: 0px 1px 0px 0px rgba(153, 153, 153, 0.1);
    color: #333;
    font-family: PingFang SC;
    font-size: 14px;
    font-weight: 500;
    border-bottom: none;

    &::before {
      display: none;
    }

    .ant-table-filter-column {
      justify-content: unset;
    }

    .ant-table-column-title {
      flex: unset;
    }

    .ant-table-column-sorters {
      justify-content: unset;
    }
  }

  // body样式
  &-tbody tr > td {
    // height: 38px;
    padding: 6px 10px;
    // color: #000;
    font-weight: 400;
    // border-bottom: 1px solid #e8e8e8;
    box-sizing: border-box;

    // 操作按钮统一样式，需添加特定class生效
    .operation {
      a {
        margin-right: 10px;
        color: #6355ff;

        &:last-child {
          margin-right: 0;
        }

        &:hover {
          color: #5244f7;
        }
      }
    }
  }

  &-tbody > tr.ant-table-row {
    // border-bottom-color: #ebecee;
  }

  &-tbody > tr.ant-table-row:hover > td,
  &-tbody > tr > td.ant-table-cell-row-hover {
    // background: rgba(99, 85, 255, 0.1);
    background-color: rgb(246, 247, 251);
  }

  .ant-table-cell-fix-right {
    background-color: rgb(246, 247, 251);
  }

  .ant-table-cell-fix-left {
    background-color: rgb(246, 247, 251);
  }
}
