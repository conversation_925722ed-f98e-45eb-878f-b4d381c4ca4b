@import './ant-design/index.less';
@import './vant-design/index.less';

// 菜单栏的共工样式
.ant-menu {
  // 菜单项边距为0
  li[role="menuitem"].ant-menu-item {
    margin: 0;

    // 选中项标识改为左侧
    &::after {
      right: auto;
      left: 0;
    }
  }

  // 选中菜单背景色
  &:not(.ant-menu-horizontal) .ant-menu-item-selected {
    background: #f0f1f3;
  }
}

// 页面公共样式
.common-page {
  display: flex;
  flex-direction: column;

  &-header {
    display: flex;
    border-bottom: 1px solid rgba(229, 230, 233, 1);
    height: 48px;
    padding: 0 20px;
    align-items: center;

    h2 {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 0;
    }

    & > * {
      flex: 1;
    }
  }

  &-body {
    padding: 20px;
    flex: 1;
  }
}

// 提示信息公共样式
.common-help-message {
  font-size: 13px;
  margin-top: 5px;
  color: rgba(4, 12, 44, 0.45);
}

html,
body {
  width: 100%;
  height: 100%;
  font-family: PingFang SC;
  // font-family: PingFangSC-Regular, -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
}

ul,
li {
  list-style: none;
}

pre {
  font-family: PingFang SC;
  // font-family: PingFangSC-Regular, -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
}

body {
  margin: 0;
  color: #040c2c;
  font-size: 13px;
  font-variant: tabular-nums;
  line-height: 1.5;
  background-color: #fff;
  font-feature-settings: "tnum";
}
/* 手机端，头部导航栏50px */
@media (max-width: 768px) {
  body {
    padding-top: 50px !important;
  }
}
*::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.3);
  border: solid transparent;
}

*::-webkit-scrollbar {
  cursor: pointer;
  height: 6px;
  background-color: #fff;
}

// 滚动条样式
&::-webkit-scrollbar {
  width: 5px;
}

&::-webkit-scrollbar-thumb {
  border-radius: 0;
}

.currentPath {
  padding-left: 20px;
  height: 48px;
  border-bottom: 1px solid rgba(229, 230, 233, 1);
  font-size: 16px;
  color: #040c2c;
  letter-spacing: 0;
  font-weight: bold;
  margin-bottom: 0;
  display: flex;
  align-items: center;
}

.detailsPath {
  padding: 0 20px;
  height: 48px;
  border-bottom: 1px solid rgba(229, 230, 233, 1);
  font-size: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}


.ant-popover {
  z-index: 1000 !important;
}
.ant-modal {
  z-index: 10000 !important;
}



   