import { createRouter, createWebHistory } from 'vue-router'
import { userStore } from '@/stores'
import Progress from 'nprogress'
import Login from '@/views/login/index.vue'
Progress.configure({ showSpinner: false })
import PermissionDenied from '@/views/login/PermissionDenied.vue'
import MainLayout from '@/components/Layout/MainLayout.vue'

// 导入子页面组件
import WeeklyAnalysisReport from '@/views/weekly-analysis-report/index.vue'
import WeeklyCalculationPlatform from '@/views/weekly-calculation-platform/index.vue'
import WeeklySupplierCapacity from '@/views/weekly-supplier-capacity/index.vue'
import WeeklyLineQuantity from '@/views/weekly-line-quantity/index.vue'
import WeeklyWorkingDays from '@/views/weekly-working-days/index.vue'
import WeeklySupplierQuota from '@/views/weekly-supplier-quota/index.vue'
import WeeklySupplierInventory from '@/views/weekly-supplier-inventory/index.vue'

import YearlyAnalysisReport from '@/views/yearly-analysis-report/index.vue'
import YearlyCalculationPlatform from '@/views/yearly-calculation-platform/index.vue'
import YearlySupplierCapacity from '@/views/yearly-supplier-capacity/index.vue'
import YearlyLineQuantity from '@/views/yearly-line-quantity/index.vue'
import YearlyWorkingDays from '@/views/yearly-working-days/index.vue'
import YearlySupplierQuota from '@/views/yearly-supplier-quota/index.vue'
import YearlySupplierInventory from '@/views/yearly-supplier-inventory/index.vue'
import YearlyCalenderConfig from '@/views/yearly-calender-config/index.vue'

const routes = [
  {
    path: '/',
    redirect: '/weekly-analysis-report',
  },
  // 产能分析系统路由 - 平铺结构
  {
    path: '/',
    component: MainLayout,
    children: [
      // Weekly routes - 平铺结构
      {
        path: 'weekly-analysis-report',
        name: 'WeeklyAnalysisReport',
        component: WeeklyAnalysisReport,
      },
      {
        path: 'weekly-calculation-platform',
        name: 'WeeklyCalculationPlatform',
        component: WeeklyCalculationPlatform,
      },
      {
        path: 'weekly-supplier-capacity',
        name: 'WeeklySupplierCapacity',
        component: WeeklySupplierCapacity,
      },
      {
        path: 'weekly-line-quantity',
        name: 'WeeklyLineQuantity',
        component: WeeklyLineQuantity,
      },
      {
        path: 'weekly-working-days',
        name: 'WeeklyWorkingDays',
        component: WeeklyWorkingDays,
      },
      {
        path: 'weekly-supplier-quota',
        name: 'WeeklySupplierQuota',
        component: WeeklySupplierQuota,
      },
      {
        path: 'weekly-supplier-inventory',
        name: 'WeeklySupplierInventory',
        component: WeeklySupplierInventory,
      },
      // Yearly routes - 平铺结构
      {
        path: 'yearly-analysis-report',
        name: 'YearlyAnalysisReport',
        component: YearlyAnalysisReport,
      },
      {
        path: 'yearly-calculation-platform',
        name: 'YearlyCalculationPlatform',
        component: YearlyCalculationPlatform,
      },
      {
        path: 'yearly-supplier-capacity',
        name: 'YearlySupplierCapacity',
        component: YearlySupplierCapacity,
      },
      {
        path: 'yearly-line-quantity',
        name: 'YearlyLineQuantity',
        component: YearlyLineQuantity,
      },
      {
        path: 'yearly-working-days',
        name: 'YearlyWorkingDays',
        component: YearlyWorkingDays,
      },
      {
        path: 'yearly-supplier-quota',
        name: 'YearlySupplierQuota',
        component: YearlySupplierQuota,
      },
      {
        path: 'yearly-supplier-inventory',
        name: 'YearlySupplierInventory',
        component: YearlySupplierInventory,
      },
      {
        path: 'yearly-calender-config',
        name: 'YearlyCalenderConfig',
        component: YearlyCalenderConfig,
      },
    ],
  },
  {
    path: '/login',
    name: 'Login',
    component: Login,
  },
  {
    path: '/permissiondenied',
    name: 'noPermission',
    component: PermissionDenied,
  },
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: routes,
})

router.beforeEach(async (to, from, next) => {
  const { token } = userStore()
  const path = to.path

  // 启动进度条
  Progress.start()

  // 允许访问的公共路径
  const publicPaths = ['/login']

  // 检查是否是公共路径
  const isPublicPath = publicPaths.some(item => path.startsWith(item))

  // 如果是公共路径，则允许访问
  if (isPublicPath) {
    next()
    return
  }

  // 其他情况需要登录
  if (!token) {
    next({
      name: 'Login',
      query: { redirect: path, ...to.query },
      replace: true,
    })
    return
  }

  // 已登录，允许访问
  next()
})

router.afterEach(() => {
  Progress.done()
})

export default router
