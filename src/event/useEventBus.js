// useeventBus2.js
import { reactive } from "vue";

export function useEventBus() {
  const state = reactive({
    events: {},
  });

  const emit = (event, data) => {
    if (state.events[event]) {
      state.events[event].forEach((callback) => callback(data));
    }
  };

  const on = (event, callback) => {
    if (!state.events[event]) {
      state.events[event] = [];
    }
    state.events[event].push(callback);
  };

  const off = (event, callback) => {
    if (state.events[event]) {
      state.events[event] = state.events[event].filter((cb) => cb !== callback);
    }
  };

  return { emit, on, off };
}
