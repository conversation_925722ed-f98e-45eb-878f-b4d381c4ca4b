import { createApp } from 'vue'
import Antd from 'ant-design-vue'
import Vant from 'vant'
import App from '@/App.vue'
import store from '@/stores'
import router from '@/router'
import SvgIcon from '@/components/SvgIcon/index.vue'
import 'ant-design-vue/dist/antd.less'
import 'nprogress/nprogress.css'
import 'virtual:svg-icons-register'
import 'dayjs/locale/zh-cn'
import 'vant/lib/index.css'
import '@/styles/index.less'
import './styles/tailwind.css'
import tlinkUtils from '@/utils/tlinkUtils'
import 'highlight.js/lib/common'
import hljsVuePlugin from '@highlightjs/vue-plugin' //插件
import 'highlight.js/styles/vs2015.css'
// import VConsole from 'vconsole';
import Watermark from 'ywl-watermark-vue'

// new VConsole();

const app = createApp(App)
app.config.globalProperties.$Tutile = tlinkUtils
app.component('SvgIcon', SvgIcon)

app.use(store).use(Watermark).use(router).use(Antd).use(hljsVuePlugin).use(Vant).mount('#app')
