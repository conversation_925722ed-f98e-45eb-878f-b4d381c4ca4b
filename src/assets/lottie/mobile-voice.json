{"v": "5.6.10", "fr": 25, "ip": 0, "op": 100, "w": 300, "h": 300, "nm": "话筒动画", "ddd": 0, "assets": [{"id": "image_0", "w": 110, "h": 114, "u": "", "p": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAG4AAAByCAYAAACyT+QbAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAgAElEQVR4nO2dbXBdx3nff8+eCxCg+AJKomRaVAi5tOyhbEsyU48jyyNIrl037lRxp+xbPJUsv7RJ3FipW2c69hSAp5066QfJ6YdMOu1QjtpmpuiMR/FL43ESQbJrN64pUfII1gtjQhIlSKZEggRIkMA95+mHs3vOs+eec+8FCFByqp05uHv27j1n9/nv/3n+u3vuhfBXIB08enKkTesGGWjtWUmzawS5QVW2K4yqChkyoqojipAp86rMK5CpzKu6w4rOo3I4TdNTy+cHpydulPnXuk+9krzWDVhLOnhs8QbgBkXGMuR6VW5QFXIwQBUUQRUyQFV8WXjfnpd5VX+OzGbKtKib1hUemrhRZl/D7tamnxvgPFh3Iu72nEkeBEogcpD8OQYkLCihDmQe3HoQpRgEmcosyH1ZxkNfvlGmXysb2PS6Bu7g3NKo0/RucLcrOloYkxKgrDafM66DfWsBusJGYBa4bxm+cu9r6FJfl8B5wCYQuUO1H7ZYkAxb6B+k6jWrbAzXCQaTPH8fGV/98l+/+Cx8XQF3/9zCGMi4ImNVw9fHq5rYxdqArl6/CrRo3kYBqAAITKsyeTEBfF0A90dzS6Nt0gnFM4w+gLDnXRnXjYHdgAeQ/E0aAauWT5Nw5797tzy70TZ7zYG7f+7MBDC+tvi0CsZF573jIdAPUHXl8yLc6xb5ysStGxcDXzPg7p9bGBPcwQxGLzQ+Zcbgq1GLTfcrQIG1ggcw2xLu/DfvkYc2wn6vCXD3z52ZUBjv6tqKfCcjLlQtVhno1eJq3WLXeiEP3DvxXvmtdTUgFxm4g3MnRxMGH1Q/D+sAiSojaoDQ7iKil1psEia1INnzmvJewIa8wLTLuPOL71u/2HfRgLv/xdO/kklyD8poFJ8sEBg2GAZGhl/zfK0+Hq7S/XUF1hqz4/PKvMCdX3yfPLAe9rwowP3hi4t3K3JPX/HpIqhFRS4MMK1nHl0+U+Qz7v7C++UrF2rTDQfu4AsLEyJufHUT5d6GX71aXJ34MDGq0S3aOquJiSgTX3i/TF6AWTcWuIMvLEyAG18dMP0DUVWLvWLehrCsaSD0GiAZE//6lrWDt2HAHXzhzATI+GrUYr20vzCgNR/hZWf7ZFy/TFzVdey5gigTv71G8DYEuIPPn5lQkfG1qcV6ttROqnvM1xrVYlN5j3q1LLsQFgMuZeJf3bp68NYduP9ybPFucPdExsQY3jBwfdTiBrhFe66xkdbT3YZyp3z2c2Pye/3a2LZzXdJ/fu7Mr+Dka3WM6H81vwpiBZRGl5lf18ydLgwwk6ePa13w/YTbP3eL/HGfpl4/4H7/6NLoQEsfzWCkWYjU7TbHhm8CuqjTxMCqUS7A0I2A9XHdtdzPl88ODjL2mT4n6a1+KvV1oRYHU5WR/kTE+u6nSf/G6Vm+qji2DvcrymF05TzTwDU9TA2A66dSr/QHz527J1XGMs+CTCU/MPnqex35+Fx7fE5VgjIrWGIBjGJUpbwu72oA7FZfmu7d5/0arjX6lT/T8X5sfsGu8vePLo2J48HI1REzojYedWFjPwysSuuiM6t0X1X2oCCyhhjVx/36LRdh7Ddv676rcEGu8p6jJ0dwHMy0Ii7oQxF2eS8HuzMeYjvrjRzSWg1dm8/yjNTUvxCx0/fnlfvo4TIvyFW2GLo7UxlNC3eWGzjN4nzxfiakGs5zcFMPcJaVbjE1+SyDVOOJdB7wTKeNW6wtx7gje5jPdHw+M/drum6X8m736yM/+h+/091lrtlV3nN0aXQA96iiI93UYnch0qdazEyDV8uyJndkr2Pr2fqVc9dQfqFTjaa1UHWMfuYD9SpzzYxrIQcz1ZGIHZqzIzAlU8nPs3o2ZpaNhoFBiKAGtLoRXckX9ZWcMb3y4Zr2WnReV/q4d0+B0uNadUx2KY2sWxPj7jm6NObUPVjHjl4T5eoKSd18zQLVczXf1rflTfV7ldOdWSKruO46tEuFsV//UKdQWZM4EU3GM9X6ibNVixaUDjfZRS2GRnfpHP4eTeVv2Qaj22DXJfmBwktnYfYUvLQIz56q9qm8jlpVWQHVCpeLIlZSJoBbqaRVM+6eZ5bGVNyDTYZvnBY0MLDpeQ/qDNdH+Y5N8InrYGRT937MnoI/fgpOnS+v06Ei11J+gWyvq5cMMPrJSqxbdYzLJLkjj08SqcU4PhHFuTj+xTFQq2CYo+rzq+UoqFF/H9gNn3t3b9AARrfDp98N73kzUexTKvex7aMsj9y0LaePeNdU3vB5Pd8Z61bFuHuO6ki7vXKyX7XYaz9ttWyygNlRi8JNu+CXu858mtNDs/DdZ2PDIrFhQ3saWSaeBfYz6+lKM0Y/+ZGSdati3PLy8t291SKFWkwrbMwVqNSPTnuEDoT5mmVZZozr6+wYhNuuXk1P4nTLKIwM+vsFg2Ux46osq2MGNfl+532Rwq2v91nb5lUBp8gdvdcW69Ya/doidAIGEYPqjGClvgUsfO7vvhWGLnC5/GM3+mtXJt61A6yhH1H5euTNPRx81La3b+D+/U9WxjKV0Y4F34KBzasnWWXkVt1cx+iqjG6rHi1gZHDNNrhme7+9aE4jQ3D19vjaRfuyMlsdNMaVdQAOq2Nft4GAMnr/1/X60N6+gWvDWPeV/j5X8KHrCO5gn3VZ1pj+2HVJvz3onfaMxNeO2Jd1Gje0Zb3Y1xH7NC5PhTtCW/sGTlTuaFKLBWBZd7VYHXUd6q2yBlkAVTFgONdsfdgW0pVbvEq1rG5qh3fdRd4c0eBrAnaVed+Owl32BdzEo0ujqdLpJqOYFtRizjJHfsOwz+UgHl3VEUyZj9xQQ1wL04BdW/oDpZ905VZ/bQuaZZxvh1V+hWFN27vVWy0rseVSusu+QroMDd2SpWnj/lq/O9FAx2jE1LejucrWqNwwYWSonx70l0aGKVZGVEFCB0Le9sm82qMATMr2r2pK0KtcuB54rC/GZWk2Vr9rXaMWu+Sd+qNSXitQoHNrxbhIMsp1zXVMWhkYVNti3SIxMFVl3NMVsvo8yhj0ybgsk7EMrTz/UQIGRKOiOnKK9ykN4Uy+VI2KGEGjoY45gmHtisl6prBw4D1+ybbQFiFaywRyN2oYBnHfO8qbWNVHPQc3QB/ATTyqI8v+Fw+CW4xuIGbUSVxe1zANLscwKeRV8y9jBCYFNxnccBW0jWAcmQEmjMJwn9BXLcu7AiCdrFw1gNWBoNx48Gs60hO486Rjmjk/+jq/NGFVT8G26nkBkmeU+Xy4XmBdxDIMWB4wC95GAJdl4DyrCuaJGTziB6VlWeiPAbNqkybAOuJmg9eyn9+kXN8TOCUpHrkz6iYHAanzwaYhJVAaLkKl0cEI3gUHia3QMdmmCtpGMM4MGBUz8HwHC+VYES7i297NZVoAO+r0w0p/7mBPV3GiqqJwQxGQzc0dglMtxIbzdZz4c/I6+QRBc3GCFPWiOU9oUJDclR1rrQMwlK9z0qb72bxvb7E4UDNRBwNyqG/K1zSf02KAj/ZknEAxxRUzuvDggRcaFX9eukhB0XKkhZhhDICa2GfAsLEuC2CZ1w0BzjuG4CY7KKNxUV2/60JI+PiapwGm3MENjcCpVyGi3BDikBUiRePqAKOsn4uLIlNev/hjC7xXsq7Qukh/ngV3uVGMM3Et8sgezIBpVbjYfoT8hYJUVy7KjlrgAmj+5iPVUSR41+hruQqA+Q3Vv4bnSKToaCFIpATJqkn118xMvOlwWRs1j8vIXYiCVgOJZVwl320ORq19WDOAIuzpb+WkjlnSLa8R8zTKGFdEOXqry0wFI2uUZPGIxAYwLsuMt/Dsw5VipRjSBe0qXsacY8qjWFdXTg+WVer0BM4pI/GIUr/yIcV6ZAA2L9eCXGEuUypARUUKWW0fJMqNoaVltBPMAF6xgrIBjIumIqEoPCBkBl/xmLp1lxCBGcU64vJGAPsDdkevJS8BRuxCcQGWj1wCONFcYQJOhCTU9exLfD6pXqPCZIdEajM0vO4IAJ4406MHq0gnF0vRY+eP9p5i20Hc1qJP9iDO01Qf81pXHud7T8CrdBWRgnXFDgCBcSWYRSPDh8PoDIELz7pMIg0SYp2NeR0ixTDuhZNw6Trtyb14gkJVkjXHuLIDdDAx8k42DhK7wr5WU6rnpl7PRWaBeesmyzlcySpHzCbLOHuIWpDDodEoo/pqGBitT3pV+cLJXj3oP/3lS5RTjepgUaJ4W53ORG215dS8Xy2nUl7T90q9+Q7GRYoScDAvMJK7NSkn24F5KjgRRGIWFjcxozFzofP53C4jJ6Dz1MoFXf6k81ACV2yHPTvgTdvg0LPwxIsVVwY88xJ8+J39AdMrBcZFEl/hF98Ge66AY8dh8Sy8+AqsrJSsqhVpEDGrQ8A0sayPcoGT3VylTOGtaD8sZV4ERPyqiJbsKxoaQDOfyUWJElQ34sED7M9b3HAVfOjt5b3ftA2eeIGOZ0GefglOLMKlF7ihemQO/vJFSMJUAAp3+c5RuHIHvPWqvO7Dh+HHR0w7fIeLiFDnUkszlPmK67TltUBS2r2rqzwAOHg2XrbSKMYV7jGID1ESKcVIUY7Wu1ByQSPe/YZ7Pfdq3JYdm+Gay/1J5dGCe7/drRe909Iy/NHDJr4F90jOtCt3xPV/+oLPWDYZV1YrKojzVYEC3UGLBE7GfC1wk2ZwiOYxLmx+omEvTYsLFSwL7lI0OhJREhfKKVRmKW5yVWqV6ssLyqmluF37f4FoKhDY+epp+OajTbD0Tv/rR3DitD+pTPTftjuue/oMLJyhQ/WKUjw+WKsuA1A1Dx31k8fYX+BwT3HihMfKkaAFgAVjtMqeOJ+Y88ScJwXIZXyoThUeOxa3Zd9VMDQYGyMY7huPwP/4Qc6e1aRv/gimf0zE4DAghgbhXW+J688cLYHCGhdj3KzMRwxrYN9q82gD48btiXJK/AZnuEAhUIgNXrBIvZukE8AkMNHUjwRN6Dzw+PNxu4YH4OZrKWKndWso/NmP4UtT8NSLdb2K06sL8MX/Ct/6v3QoxHB+y7ty8EI6vwyHn+pkQPhsoShNHyJ12eUzfanNAL5wuOc8bmCAx9rLkEchswcXjK/EirLKHHMthXjVXTRfKBHxr3kdJ7lQOb0Ejz0P15vHyz94HZw9Bw/9hGgnIRjo1QX4Dw/A23bB7svh7W+Gt10FZ8/D86/kx9MvwDMvlgOq+Dx5G0Thxr3w3n2xLR55KgcvqRFdYj4bBnQh4EydahyL4mQAqqZepbxzOlBNO+Hxl4obi9lEzZNzBgeC69TCBUaG9b1IzMhU35I8wikOKW2i8N2nYM9lMLK5/Mzt++HISzkIkcsKt1F46gV48hh85zCxesN4BjHGMSBc82b46M2xHU4twqNPVgwYDBrdvNn4VXVp7Raxqnr9Sp3l8zzcGOOmpqZkenpaPn4j8w4eKybKGgSE5gpTzdIXWoAWuUdCfMvfF6c4F1ymr48RNpTnp87Ctx/vbN+vfxAuuyTulDVKSB3zKmJDVmPIjq3wsb/Reb8/+T6cP28KtN6oHWWVONhxNNRpuj7K0Y9+XOpjHJDPBcbCB7PHcyOoMY6U7gEIijC8X7hNf4QlMSdaxr8ALiXY9lriB8bTc8r0TNy84UH4jQ/DtW+K3XKjgZrqmPw7RuHTfzuOawA/moFjL/u+GUNH53VHANIC01Cn6xQiBnYa+ljyAhgU97ANlh0NN0YpZb6f7wUx4hTnApB5vlCc0gl0ADPEiIdmlMOzcbsu3QKf+WX4zY/A5Vv7AM3GH1+GwKVb4R/eCnd8CHZUJvKPH4E/9wKmFgSI3VtVSFTv3dSmunzNe84D19d+*************************************************+ns5Nd+mD/beeM1cfv27oLJf5RL+z9/HM6cy4ONEC9gRF+8F7hsK9x2PdxyPbXpsSPwre96MSKxEW2f7I0i8KoNMHkhLrdE6LiOKR8QHur4PBDWKiOvN81scugHe54U/*******************************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", "e": 1}], "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "形状图层 4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [152.218, 150.386, 0], "ix": 2}, "a": {"a": 0, "k": [2.218, 601.386, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[5.869, 0], [0, -5.869], [0, 0], [-9.468, -0.251], [0, 5.869], [0, 0]], "o": [[-5.869, 0], [0, 0], [0, 5.04], [9.279, 0.246], [0, 0], [0, -5.869]], "v": [[0, -15.609], [-10.627, -4.982], [-10.627, -3.763], [0, -8.284], [10.627, -3.763], [10.627, -4.982]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 2.917, "s": [{"i": [[5.869, 0], [0, -5.869], [0, 0], [-9.468, -0.251], [0, 5.869], [0, 0]], "o": [[-5.869, 0], [0, 0], [0, 5.04], [9.279, 0.246], [0, 0], [0, -5.869]], "v": [[0, -15.609], [-10.627, -4.982], [-10.627, 9.979], [0, 5.458], [10.627, 9.979], [10.627, -4.982]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 6.417, "s": [{"i": [[5.869, 0], [0, -5.869], [0, 0], [-9.468, -0.251], [0, 5.869], [0, 0]], "o": [[-5.869, 0], [0, 0], [0, 5.04], [9.279, 0.246], [0, 0], [0, -5.869]], "v": [[0, -15.609], [-10.627, -4.982], [-10.627, -3.763], [0, -8.284], [10.627, -3.763], [10.627, -4.982]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 8.75, "s": [{"i": [[5.869, 0], [0, -5.869], [0, 0], [-9.468, -0.251], [0, 5.869], [0, 0]], "o": [[-5.869, 0], [0, 0], [0, 5.04], [9.279, 0.246], [0, 0], [0, -5.869]], "v": [[0, -15.609], [-10.627, -4.982], [-10.627, 3.108], [0, -1.413], [10.627, 3.108], [10.627, -4.982]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 11.667, "s": [{"i": [[5.869, 0], [0, -5.869], [0, 0], [-9.468, -0.251], [0, 5.869], [0, 0]], "o": [[-5.869, 0], [0, 0], [0, 5.04], [9.279, 0.246], [0, 0], [0, -5.869]], "v": [[0, -15.609], [-10.627, -4.982], [-10.627, -3.763], [0, -8.284], [10.627, -3.763], [10.627, -4.982]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 15.167, "s": [{"i": [[5.869, 0], [0, -5.869], [0, 0], [-9.468, -0.251], [0, 5.869], [0, 0]], "o": [[-5.869, 0], [0, 0], [0, 5.04], [9.279, 0.246], [0, 0], [0, -5.869]], "v": [[0, -15.609], [-10.627, -4.982], [-10.627, 3.108], [0, -1.413], [10.627, 3.108], [10.627, -4.982]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 17.5, "s": [{"i": [[5.869, 0], [0, -5.869], [0, 0], [-9.468, -0.251], [0, 5.869], [0, 0]], "o": [[-5.869, 0], [0, 0], [0, 5.04], [9.279, 0.246], [0, 0], [0, -5.869]], "v": [[0, -15.609], [-10.627, -4.982], [-10.627, 9.979], [0, 5.458], [10.627, 9.979], [10.627, -4.982]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [{"i": [[5.869, 0], [0, -5.869], [0, 0], [-9.468, -0.251], [0, 5.869], [0, 0]], "o": [[-5.869, 0], [0, 0], [0, 5.04], [9.279, 0.246], [0, 0], [0, -5.869]], "v": [[0, -15.609], [-10.627, -4.982], [-10.627, -3.763], [0, -8.284], [10.627, -3.763], [10.627, -4.982]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 25, "s": [{"i": [[5.869, 0], [0, -5.869], [0, 0], [-9.468, -0.251], [0, 5.869], [0, 0]], "o": [[-5.869, 0], [0, 0], [0, 5.04], [9.279, 0.246], [0, 0], [0, -5.869]], "v": [[0, -15.609], [-10.627, -4.982], [-10.627, -3.763], [0, -8.284], [10.627, -3.763], [10.627, -4.982]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 27.917, "s": [{"i": [[5.869, 0], [0, -5.869], [0, 0], [-9.468, -0.251], [0, 5.869], [0, 0]], "o": [[-5.869, 0], [0, 0], [0, 5.04], [9.279, 0.246], [0, 0], [0, -5.869]], "v": [[0, -15.609], [-10.627, -4.982], [-10.627, 9.979], [0, 5.458], [10.627, 9.979], [10.627, -4.982]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 31.417, "s": [{"i": [[5.869, 0], [0, -5.869], [0, 0], [-9.468, -0.251], [0, 5.869], [0, 0]], "o": [[-5.869, 0], [0, 0], [0, 5.04], [9.279, 0.246], [0, 0], [0, -5.869]], "v": [[0, -15.609], [-10.627, -4.982], [-10.627, -3.763], [0, -8.284], [10.627, -3.763], [10.627, -4.982]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 33.75, "s": [{"i": [[5.869, 0], [0, -5.869], [0, 0], [-9.468, -0.251], [0, 5.869], [0, 0]], "o": [[-5.869, 0], [0, 0], [0, 5.04], [9.279, 0.246], [0, 0], [0, -5.869]], "v": [[0, -15.609], [-10.627, -4.982], [-10.627, 3.108], [0, -1.413], [10.627, 3.108], [10.627, -4.982]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 36.667, "s": [{"i": [[5.869, 0], [0, -5.869], [0, 0], [-9.468, -0.251], [0, 5.869], [0, 0]], "o": [[-5.869, 0], [0, 0], [0, 5.04], [9.279, 0.246], [0, 0], [0, -5.869]], "v": [[0, -15.609], [-10.627, -4.982], [-10.627, -3.763], [0, -8.284], [10.627, -3.763], [10.627, -4.982]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 40.167, "s": [{"i": [[5.869, 0], [0, -5.869], [0, 0], [-9.468, -0.251], [0, 5.869], [0, 0]], "o": [[-5.869, 0], [0, 0], [0, 5.04], [9.279, 0.246], [0, 0], [0, -5.869]], "v": [[0, -15.609], [-10.627, -4.982], [-10.627, 3.108], [0, -1.413], [10.627, 3.108], [10.627, -4.982]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 42.5, "s": [{"i": [[5.869, 0], [0, -5.869], [0, 0], [-9.468, -0.251], [0, 5.869], [0, 0]], "o": [[-5.869, 0], [0, 0], [0, 5.04], [9.279, 0.246], [0, 0], [0, -5.869]], "v": [[0, -15.609], [-10.627, -4.982], [-10.627, 9.979], [0, 5.458], [10.627, 9.979], [10.627, -4.982]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 45, "s": [{"i": [[5.869, 0], [0, -5.869], [0, 0], [-9.468, -0.251], [0, 5.869], [0, 0]], "o": [[-5.869, 0], [0, 0], [0, 5.04], [9.279, 0.246], [0, 0], [0, -5.869]], "v": [[0, -15.609], [-10.627, -4.982], [-10.627, -3.763], [0, -8.284], [10.627, -3.763], [10.627, -4.982]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 52, "s": [{"i": [[5.869, 0], [0, -5.869], [0, 0], [-9.468, -0.251], [0, 5.869], [0, 0]], "o": [[-5.869, 0], [0, 0], [0, 5.04], [9.279, 0.246], [0, 0], [0, -5.869]], "v": [[0, -15.609], [-10.627, -4.982], [-10.627, -3.763], [0, -8.284], [10.627, -3.763], [10.627, -4.982]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 54.917, "s": [{"i": [[5.869, 0], [0, -5.869], [0, 0], [-9.468, -0.251], [0, 5.869], [0, 0]], "o": [[-5.869, 0], [0, 0], [0, 5.04], [9.279, 0.246], [0, 0], [0, -5.869]], "v": [[0, -15.609], [-10.627, -4.982], [-10.627, 9.979], [0, 5.458], [10.627, 9.979], [10.627, -4.982]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 58.417, "s": [{"i": [[5.869, 0], [0, -5.869], [0, 0], [-9.468, -0.251], [0, 5.869], [0, 0]], "o": [[-5.869, 0], [0, 0], [0, 5.04], [9.279, 0.246], [0, 0], [0, -5.869]], "v": [[0, -15.609], [-10.627, -4.982], [-10.627, -3.763], [0, -8.284], [10.627, -3.763], [10.627, -4.982]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 60.75, "s": [{"i": [[5.869, 0], [0, -5.869], [0, 0], [-9.468, -0.251], [0, 5.869], [0, 0]], "o": [[-5.869, 0], [0, 0], [0, 5.04], [9.279, 0.246], [0, 0], [0, -5.869]], "v": [[0, -15.609], [-10.627, -4.982], [-10.627, 3.108], [0, -1.413], [10.627, 3.108], [10.627, -4.982]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 63.667, "s": [{"i": [[5.869, 0], [0, -5.869], [0, 0], [-9.468, -0.251], [0, 5.869], [0, 0]], "o": [[-5.869, 0], [0, 0], [0, 5.04], [9.279, 0.246], [0, 0], [0, -5.869]], "v": [[0, -15.609], [-10.627, -4.982], [-10.627, -3.763], [0, -8.284], [10.627, -3.763], [10.627, -4.982]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 67.167, "s": [{"i": [[5.869, 0], [0, -5.869], [0, 0], [-9.468, -0.251], [0, 5.869], [0, 0]], "o": [[-5.869, 0], [0, 0], [0, 5.04], [9.279, 0.246], [0, 0], [0, -5.869]], "v": [[0, -15.609], [-10.627, -4.982], [-10.627, 3.108], [0, -1.413], [10.627, 3.108], [10.627, -4.982]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 69.5, "s": [{"i": [[5.869, 0], [0, -5.869], [0, 0], [-9.468, -0.251], [0, 5.869], [0, 0]], "o": [[-5.869, 0], [0, 0], [0, 5.04], [9.279, 0.246], [0, 0], [0, -5.869]], "v": [[0, -15.609], [-10.627, -4.982], [-10.627, 9.979], [0, 5.458], [10.627, 9.979], [10.627, -4.982]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 72, "s": [{"i": [[5.869, 0], [0, -5.869], [0, 0], [-9.468, -0.251], [0, 5.869], [0, 0]], "o": [[-5.869, 0], [0, 0], [0, 5.04], [9.279, 0.246], [0, 0], [0, -5.869]], "v": [[0, -15.609], [-10.627, -4.982], [-10.627, -3.763], [0, -8.284], [10.627, -3.763], [10.627, -4.982]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 78, "s": [{"i": [[5.869, 0], [0, -5.869], [0, 0], [-9.468, -0.251], [0, 5.869], [0, 0]], "o": [[-5.869, 0], [0, 0], [0, 5.04], [9.279, 0.246], [0, 0], [0, -5.869]], "v": [[0, -15.609], [-10.627, -4.982], [-10.627, -3.763], [0, -8.284], [10.627, -3.763], [10.627, -4.982]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 80.917, "s": [{"i": [[5.869, 0], [0, -5.869], [0, 0], [-9.468, -0.251], [0, 5.869], [0, 0]], "o": [[-5.869, 0], [0, 0], [0, 5.04], [9.279, 0.246], [0, 0], [0, -5.869]], "v": [[0, -15.609], [-10.627, -4.982], [-10.627, 9.979], [0, 5.458], [10.627, 9.979], [10.627, -4.982]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 84.417, "s": [{"i": [[5.869, 0], [0, -5.869], [0, 0], [-9.468, -0.251], [0, 5.869], [0, 0]], "o": [[-5.869, 0], [0, 0], [0, 5.04], [9.279, 0.246], [0, 0], [0, -5.869]], "v": [[0, -15.609], [-10.627, -4.982], [-10.627, -3.763], [0, -8.284], [10.627, -3.763], [10.627, -4.982]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 86.75, "s": [{"i": [[5.869, 0], [0, -5.869], [0, 0], [-9.468, -0.251], [0, 5.869], [0, 0]], "o": [[-5.869, 0], [0, 0], [0, 5.04], [9.279, 0.246], [0, 0], [0, -5.869]], "v": [[0, -15.609], [-10.627, -4.982], [-10.627, 3.108], [0, -1.413], [10.627, 3.108], [10.627, -4.982]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 89.667, "s": [{"i": [[5.869, 0], [0, -5.869], [0, 0], [-9.468, -0.251], [0, 5.869], [0, 0]], "o": [[-5.869, 0], [0, 0], [0, 5.04], [9.279, 0.246], [0, 0], [0, -5.869]], "v": [[0, -15.609], [-10.627, -4.982], [-10.627, -3.763], [0, -8.284], [10.627, -3.763], [10.627, -4.982]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 93.167, "s": [{"i": [[5.869, 0], [0, -5.869], [0, 0], [-9.468, -0.251], [0, 5.869], [0, 0]], "o": [[-5.869, 0], [0, 0], [0, 5.04], [9.279, 0.246], [0, 0], [0, -5.869]], "v": [[0, -15.609], [-10.627, -4.982], [-10.627, 3.108], [0, -1.413], [10.627, 3.108], [10.627, -4.982]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 95.5, "s": [{"i": [[5.869, 0], [0, -5.869], [0, 0], [-9.468, -0.251], [0, 5.869], [0, 0]], "o": [[-5.869, 0], [0, 0], [0, 5.04], [9.279, 0.246], [0, 0], [0, -5.869]], "v": [[0, -15.609], [-10.627, -4.982], [-10.627, 9.979], [0, 5.458], [10.627, 9.979], [10.627, -4.982]], "c": true}]}, {"t": 98, "s": [{"i": [[5.869, 0], [0, -5.869], [0, 0], [-9.468, -0.251], [0, 5.869], [0, 0]], "o": [[-5.869, 0], [0, 0], [0, 5.04], [9.279, 0.246], [0, 0], [0, -5.869]], "v": [[0, -15.609], [-10.627, -4.982], [-10.627, -3.763], [0, -8.284], [10.627, -3.763], [10.627, -4.982]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.513725490196, 0.764705942191, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [2.218, 610.164], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100.058], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "矩形 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 100, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 2, "nm": "话筒内@2x.png", "cl": "png", "refId": "image_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [148, 179, 0], "ix": 2}, "a": {"a": 0, "k": [55, 57, 0], "ix": 1}, "s": {"a": 0, "k": [136, 136, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 100, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "形状图层 2", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 11, "s": [28]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 19, "s": [16]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 38, "s": [28]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 46, "s": [16]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 68, "s": [28]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 76, "s": [16]}, {"t": 94, "s": [28]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [152.212, 173.594, 0], "ix": 2}, "a": {"a": 0, "k": [2.212, 606.594, 0], "ix": 1}, "s": {"a": 0, "k": [136, 136, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[26.775, 0], [0, -26.775], [-26.775, 0], [0, 26.775]], "o": [[-26.775, 0], [0, 26.775], [26.775, 0], [0, -26.775]], "v": [[0, -48.48], [-48.48, 0], [0, 48.48], [48.48, 0]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.780392216701, 0.780392216701, 0.996078491211, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [2.212, 606.594], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [139.435, 139.435], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "椭圆 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 100, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "形状图层 3", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 11, "s": [12]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 19, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 38, "s": [12]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 46, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 68, "s": [12]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 76, "s": [0]}, {"t": 94, "s": [12]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [152.212, 173.594, 0], "ix": 2}, "a": {"a": 0, "k": [2.212, 606.594, 0], "ix": 1}, "s": {"a": 0, "k": [136, 136, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[26.775, 0], [0, -26.775], [-26.775, 0], [0, 26.775]], "o": [[-26.775, 0], [0, 26.775], [26.775, 0], [0, -26.775]], "v": [[0, -48.48], [-48.48, 0], [0, 48.48], [48.48, 0]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.780392216701, 0.780392216701, 0.996078491211, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [2.212, 606.594], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [173.308, 173.308], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "椭圆 2", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[26.775, 0], [0, -26.775], [-26.775, 0], [0, 26.775]], "o": [[-26.775, 0], [0, 26.775], [26.775, 0], [0, -26.775]], "v": [[0, -48.48], [-48.48, 0], [0, 48.48], [48.48, 0]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.780392216701, 0.780392216701, 0.996078491211, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [2.212, 606.594], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [139.435, 139.435], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "椭圆 1", "np": 3, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 100, "st": 0, "bm": 0}], "markers": []}