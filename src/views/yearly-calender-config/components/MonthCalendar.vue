<template>
  <div class="month-calendar">
    <header>
      <div class="box"></div>
      <div>休息日</div>
      <div class="box valid"></div>
      <div>工作日</div>
      <div class="flex1">
        <a-button size="small" type="primary" @click="onSave" :disabled="isYearDisabled" :loading="saving">
          保存
        </a-button>
      </div>
    </header>
    <a-spin :spinning="loading">
      <div class="body">
        <div class="rows">
          <div class="row">
            <div class="header-cell" v-for="d in workList" :key="d">
              {{ d }}
            </div>
          </div>
          <div class="row" v-for="(week, index1) in weeks" :key="index1" v-if="weeks && weeks.length">
            <div
              class="cell"
              v-for="day in week"
              @click="onDay(day)"
              :key="day.id"
              :class="[
                day.date && day.date.format
                  ? day.date.format('YYYY-MM') == currentYearMonth
                    ? 'valid'
                    : 'invalid'
                  : 'invalid',
                day.customType || '',
              ]"
            >
              {{ day.date && day.date.format ? day.date.format('DD') : '' }}
            </div>
          </div>
        </div>
      </div>
    </a-spin>
  </div>
</template>

<script>
  import { defineComponent, reactive, toRefs, onMounted, computed, watch } from 'vue'
  import { message } from 'ant-design-vue'
  import dayjs from 'dayjs'
  import weekday from 'dayjs/plugin/weekday' // 引入插件
  // import 'dayjs/locale/zh-cn' // 引入中文语言包
  // 必须这一步：扩展功能
  // dayjs.extend(weekday)
  // dayjs.locale('zh-cn') // 设置语言
  import { getMonthDays, saveMonthDays } from '../api.js'
  export default defineComponent({
    name: 'MonthCalendar',
    props: {
      // 具体哪一年哪一月的日历：2025-07
      date: {
        type: Date,
        default: () => {
          return new Date()
        },
      },
      bu: {
        type: String,
        default: '',
      },
      supplier: {
        type: String,
        default: '',
      },
    },
    emits: ['success'],
    setup(props, { emit }) {
      // 获取某月日历
      function getCalendarDays(targetDate = null) {
        const date = dayjs(targetDate)
        const year = date.year()
        const month = date.month() // 0~11

        const firstDay = dayjs().year(year).month(month).date(1)
        const lastDay = dayjs().year(year).month(month).endOf('month')

        // 获取第一天和最后一天是星期几（0=周日，1=周一 ... 6=周六）
        const firstDayOfWeek = firstDay.weekday()
        const lastDayOfWeek = lastDay.weekday()

        // 调整为周一为一周第一天（周日变成 7）
        const adjustedFirstDayOfWeek = firstDayOfWeek === 0 ? 7 : firstDayOfWeek
        const adjustedLastDayOfWeek = lastDayOfWeek === 0 ? 7 : lastDayOfWeek

        // 补前缀：上个月的天数
        const prependCount = adjustedFirstDayOfWeek - 1

        // 补后缀：下个月的天数
        const appendCount = (7 - adjustedLastDayOfWeek) % 7

        const daysArray = []

        // 🔁 补上前一个月的天数
        const prevMonthLastDay = firstDay.subtract(1, 'day') // 上个月最后一天
        for (let i = prependCount - 1; i >= 0; i--) {
          const day = prevMonthLastDay.subtract(i, 'day')
          daysArray.push(day)
        }

        // 📅 添加当月的所有天
        const totalDays = lastDay.date()
        for (let i = 1; i <= totalDays; i++) {
          const day = dayjs().year(year).month(month).date(i)
          day.isCurrentMonth = true
          daysArray.push(day)
        }

        // 🔁 补上后一个月的天数
        const nextMonthFirstDay = lastDay.add(1, 'day')
        for (let i = 0; i < appendCount; i++) {
          const day = nextMonthFirstDay.add(i, 'day')
          daysArray.push(day)
        }

        // 可选：将一维数组转换为二维数组（每周一组，每组7天）
        const weeksArray = []
        for (let i = 0; i < daysArray.length; i += 7) {
          weeksArray.push(daysArray.slice(i, i + 7))
        }

        // return {
        //     daysArray, // 一维数组，含上月和下月日期
        //     weeksArray // 二维数组，每周一组
        // };
        return weeksArray // 二维数组，每周一组
      }
      // 日期列表，type:workday,holiday 从后台取数据
      const isYearDisabled = computed(() => {
        return dayjs(state.currentYearMonth).year() < dayjs().year()
      })
      const getCustomType = (day, dayList) => {
        const one = dayList.find(item => item.date == day.format('YYYY-MM-DD'))
        if (one) {
          return one.type
        } else {
          return day.isCurrentMonth ? '' : null // 本月的日期，即使库里没有存，也不要置为null
        }
      }

      const state = reactive({
        loading: false, // 获取哪些天是工作日
        saving: false, // 保存中
        workList: ['一', '二', '三', '四', '五', '六', '日'],
        currentYearMonth: dayjs(props.date).format('YYYY-MM'),
        weeks: [], // 按星期形成的二维数组
      })
      const getMonthData = targetDate => {
        if (!targetDate) return

        state.loading = true

        try {
          // 查询此月的工作日
          getMonthDays({
            buCode: props.bu,
            month: state.currentYearMonth,
            supplierCode: props.supplier,
            year: dayjs(state.currentYearMonth).year(),
          })
            .then(res => {
              state.loading = false
              const dayList = [] // 哪些是工作日？
              // { "dateDay": "2025-07-06", "isHoliday": "1" }
              ;(res || []).forEach(item => {
                if (item.dateDay) {
                  dayList.push({ date: item.dateDay, type: item.isHoliday == '1' ? 'holiday' : 'workday' })
                }
              })
              // 获取此月日历：每周一个数组
              const weeksArray = getCalendarDays(targetDate)
              state.weeks = weeksArray.map(group => {
                group = group.map(item => {
                  return {
                    id: state.currentYearMonth + '-' + item.format('YYYY-MM-DD'),
                    date: item,
                    customType: getCustomType(item, dayList),
                  }
                })
                return group
              })
            })
            .catch(err => {
              state.loading = false
              console.error('获取月度数据失败:', err)
              message.error(err.rstMsg || err.message || '获取数据失败')
            })
        } catch (error) {
          state.loading = false
          console.error('调用接口失败:', error)
          message.error('调用接口失败')
        }
      }
      // 加载当月数据
      onMounted(() => {
        const date = props.date || new Date()
        getMonthData(date)
      })
      // 点击天
      const onDay = day => {
        if (day.customType == null) {
          return // 非本月，不可点击
        }
        if (day.customType == 'workday') {
          day.customType = 'holiday'
        } else {
          day.customType = 'workday'
        }
      }
      // 保存
      const onSave = () => {
        state.saving = true
        // 收集哪些是工作日
        const days = []
        state.weeks.forEach(week => {
          week.forEach(day => {
            if (day.date.isCurrentMonth) {
              days.push({ dateDay: day.date.format('YYYY-MM-DD'), isHoliday: day.customType == 'workday' ? '0' : '1' })
            }
          })
        })
        // 调用接口保存
        saveMonthDays({
          buCode: props.bu,
          adjustDayList: days,
          month: state.currentYearMonth,
          supplierCode: props.supplier,
          year: dayjs(state.currentYearMonth).year(),
        })
          .then(res => {
            console.log('保存成功', res)
            state.saving = false
            message.success('保存成功')
            emit('success')
          })
          .catch(err => {
            state.saving = false
            message.error(err.rstMsg || err.message || err)
          })
      }
      // 监听参数变化，重新获取数据
      watch([() => props.date, () => props.bu, () => props.supplier], ([val]) => {
        // 年月变化，加载新数据
        console.log('月日历：', dayjs(val).format('YYYY-MM'))
        state.currentYearMonth = dayjs(val).format('YYYY-MM')
        getMonthData(val)
      })
      return {
        ...toRefs(state),
        onDay,
        onSave,
        isYearDisabled,
      }
    },
  })
</script>

<style lang="less" scoped>
  .month-calendar {
    header {
      height: 24px;
      line-height: 24px;
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      color: #666;
      .box {
        height: 12px;
        width: 12px;
        border: 1px solid #b0b0b0;
        margin-right: 4px;
        &.valid {
          background: #cdd9ff;
          border: none;
          margin-left: 16px;
        }
      }
      .flex1 {
        flex: 1;
        text-align: right;

        :deep(.ant-btn-primary) {
          background: #3562ff;
          border-color: #3562ff;

          &.ant-btn-primary[disabled] {
            background-color: #a0cfff;
            border-color: #a0cfff;
          }
        }
      }
    }
    @border: 1px solid #e9e9e9;
    .rows {
      min-height: 235px;

      .row {
        display: flex;
        border-top: @border;
        .header-cell {
          flex: 1;
          border-right: @border;
          text-align: center;
          height: 38px;
          line-height: 38px;
          color: #333;
          background: #e9eaeb;
          font-weight: 600;
          user-select: none;
        }
        .cell {
          flex: 1;
          border-right: @border;
          text-align: center;
          height: 38px;
          line-height: 38px;
          color: #333;
          cursor: pointer;
          &:first-child {
            border-left: @border;
          }
          &.invalid {
            color: #cecece;
            background: #f9f9f9;
            cursor: no-drop;
          }

          &.valid:hover {
            background: #eaefff;
            color: #3562ff;
            position: relative;
            &::after {
              content: ' ';
              position: absolute;
              left: 0;
              top: 0;
              right: 0;
              bottom: 0;
              border: 2px solid #3979f9;
            }
          }
        }
      }
      .row:last-of-type {
        border-bottom: @border;
      }
    }
    .workday {
      // background: #eaefff;
      background: #cdfee1;
    }
    .holiday {
      background: #fff;
    }
  }
</style>
