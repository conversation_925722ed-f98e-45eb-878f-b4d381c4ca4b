<template>
  <div class="year-calendar">
    <header>
      <div>选择月份</div>
    </header>
    <a-spin :spinning="loading">
      <div class="grid">
        <div class="cell" v-for="m in monthList" :key="m.id" @click="onMonth(m)" :class="{ active: m.id == month }">
          <div class="name">{{ m.month }}</div>
          <div class="workdays">({{ m.workdays || '-' }}天)</div>
        </div>
      </div>
    </a-spin>
  </div>
</template>

<script>
  import { defineComponent, reactive, toRefs, onMounted, watch } from 'vue'
  import { message } from 'ant-design-vue'
  import dayjs from 'dayjs'
  import weekday from 'dayjs/plugin/weekday' // 引入插件
  // import 'dayjs/locale/zh-cn' // 引入中文语言包
  // 必须这一步：扩展功能
  // dayjs.extend(weekday)
  // dayjs.locale('zh-cn') // 设置语言

  import { getYearMonths } from '../api.js'

  export default defineComponent({
    name: 'MonthCalendar',
    props: {
      // 具体哪一年
      year: {
        type: Date,
        default: () => {
          return new Date()
        },
      },
      // 具体哪一月
      month: {
        type: Number,
        default: 1,
      },
      bu: {
        type: String,
        default: '',
      },
      supplier: {
        type: String,
        default: '',
      },
    },
    emits: ['update:month', 'change'],
    setup(props, { emit }) {
      const state = reactive({
        loading: false,
        // 月份数据
        monthList: [
          { id: 1, month: '一月', workdays: null },
          { id: 2, month: '二月', workdays: null },
          { id: 3, month: '三月', workdays: null },
          { id: 4, month: '四月', workdays: null },
          { id: 5, month: '五月', workdays: null },
          { id: 6, month: '六月', workdays: null },
          { id: 7, month: '七月', workdays: null },
          { id: 8, month: '八月', workdays: null },
          { id: 9, month: '九月', workdays: null },
          { id: 10, month: '十月', workdays: null },
          { id: 11, month: '十一月', workdays: null },
          { id: 12, month: '十二月', workdays: null },
        ],
      })
      // 获取每年12个月工作日数
      const getYearData = () => {
        if (!props.year) return

        state.loading = true
        // 先置空
        state.monthList.forEach(item => {
          item.workdays = null
        })

        try {
          // 调用接口
          getYearMonths({
            buCode: props.bu,
            supplierCode: props.supplier,
            year: dayjs(props.year).year(),
          })
            .then(res => {
              state.loading = false
              // {"dateMonth": "",		"workDayNum": 0	}
              ;(res || []).map(item => {
                if (item.dateMonth) {
                  const month = dayjs(item.dateMonth).month() + 1
                  const one = state.monthList.find(m => m.id == month)
                  if (one) {
                    one.workdays = item.workDayNum // 每月的工作天数
                  }
                }
              })
            })
            .catch(err => {
              state.loading = false
              console.error('获取年度数据失败:', err)
              message.error(err.rstMsg || err.message || '获取数据失败')
            })
        } catch (error) {
          state.loading = false
          console.error('调用接口失败:', error)
          message.error('调用接口失败')
        }
      }
      // 加载当前月数据
      onMounted(() => {
        getYearData()
      })
      // 点击月份，更新month参数，发送change事件
      const onMonth = m => {
        emit('update:month', m.id)
        emit('change', m.id)
      }
      // 监听参数变化
      watch([() => dayjs(props.year).year(), () => props.bu, () => props.supplier], () => {
        // 年变化,bu变化，供应商变化，加载新数据
        getYearData()
      })
      return {
        ...toRefs(state),
        onMonth,
        getYearData,
      }
    },
  })
</script>

<style lang="less" scoped>
  .year-calendar {
    header {
      height: 24px;
      line-height: 24px;
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      color: #333;
      font-size: 16px;
      font-weight: 600;
    }
    .grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      @border:1px solid #E9E9E9;

      .cell {
        border-right: @border;
        border-top: @border;
        height: 58px;
        display: flex;
        justify-content: space-between;
        padding: 0 12px;
        align-items: center;
        cursor: pointer;
        &.active {
          background: #eaefff;
          position: relative;
          &::after {
            content: ' ';
            position: absolute;
            left: 0;
            top: 0;
            right: 0;
            bottom: 0;
            border: 1px solid #3979f9;
          }
        }
        &:nth-child(3n + 1) {
          border-left: @border;
        }
        &:nth-child(n + 10):nth-child(-n + 12) {
          border-bottom: @border;
        }

        .name {
          color: #333;
          font-weight: 600;
        }
        .workdays {
          color: #666;
        }
        &:hover {
          // position: relative;
          // &::after {
          //     content: ' ';
          //     position: absolute;
          //     left: 0;
          //     top: 0;
          //     right: 0;
          //     bottom: 0;
          //     border: 2px solid #3979f9;
          // }
          .name,
          .workdays {
            color: #3562ff;
          }
        }
      }
    }
  }
</style>
