<template>
  <div class="test-container">
    <h2>年度日历配置组件测试</h2>
    <div class="test-info">
      <p>当前时间: {{ currentTime }}</p>
      <p>dayjs版本测试: {{ dayjsTest }}</p>
      <p>组件状态: {{ componentStatus }}</p>
    </div>

    <div class="component-wrapper">
      <CalenderConfig />
    </div>
  </div>
</template>

<script>
  import { defineComponent, ref, onMounted } from 'vue'
  import dayjs from 'dayjs'
  // import 'dayjs/locale/zh-cn'
  import CalenderConfig from './index.vue'

  // dayjs.locale('zh-cn')

  export default defineComponent({
    name: 'TestComponent',
    components: {
      CalenderConfig,
    },
    setup() {
      const currentTime = ref('')
      const dayjsTest = ref('')
      const componentStatus = ref('初始化中...')

      onMounted(() => {
        try {
          currentTime.value = dayjs().format('YYYY-MM-DD HH:mm:ss')
          dayjsTest.value = dayjs().locale('zh-cn').format('YYYY年MM月DD日 dddd')
          componentStatus.value = '组件加载成功'
        } catch (error) {
          console.error('测试组件初始化失败:', error)
          componentStatus.value = '组件加载失败: ' + error.message
        }
      })

      return {
        currentTime,
        dayjsTest,
        componentStatus,
      }
    },
  })
</script>

<style scoped>
  .test-container {
    padding: 20px;
  }

  .test-info {
    background: #f5f5f5;
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 20px;
  }

  .test-info p {
    margin: 8px 0;
    font-family: monospace;
  }

  .component-wrapper {
    border: 2px dashed #ccc;
    padding: 20px;
    border-radius: 8px;
  }
</style>
