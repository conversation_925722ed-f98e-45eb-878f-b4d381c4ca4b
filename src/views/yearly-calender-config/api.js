import { CustomRequest } from '@/utils/CustomRequest'

// 根据年查询月份
export function getYearMonths(params) {
  return CustomRequest({
    url: '/purchase/v1/supplierAnnualDateCalendar/querySupplierWorkDayByYear',
    method: 'post',
    data: params,
  })
}

// 根据月查询右侧天数
export function getMonthDays(params) {
  return CustomRequest({
    url: '/purchase/v1/supplierAnnualDateCalendar/querySupplierWorkCalendarByMonth',
    method: 'post',
    data: params,
  })
}

// 保存接口
export function saveOrUpdateSupplierWorkCalendar(params) {
  return CustomRequest({
    url: '/purchase/v1/supplierAnnualDateCalendar/saveOrUpdateSupplierWorkCalendar',
    method: 'post',
    data: params,
  })
}
