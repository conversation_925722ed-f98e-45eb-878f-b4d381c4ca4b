<script setup>
  import { onMounted, ref } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  // import TCL from '@/assets/tcl-five-rings.svg'
  import { userStore, historyStore } from '@/stores'
  import useWindowSize from '@/hooks/useWindowSize'
  import LoginForm from './LoginForm.vue'
  import { getCookie } from '@/utils/cookie'
  import { URL_FROM, inTxinMobile, isInTxinPcBrowser } from '@/utils/index'

  defineOptions({
    name: 'LoginPage',
  })

  const { VITE_ROLE_CODE, VITE_LOGO_URL } = import.meta.env
  const logoUrl = ref(VITE_LOGO_URL)
  const route = useRoute()
  const router = useRouter()
  const { isMobile } = useWindowSize()
  const { actionLogin, setFrom } = userStore()

  const isAiAssistant = ref(false)
  const isIdm = ref(false)
  const isTaijiTxin = ref(false)
  const loading = ref(false)
  const permissionDenied = ref(false)

  const getOtherQuery = query => {
    return Object.keys(query).reduce((acc, cur) => {
      if (cur !== 'redirect') {
        acc[cur] = query[cur]
      } else if (query[cur].includes('?')) {
        let paramStr = query[cur].split('?')[1]
        const searchParams = new URLSearchParams(paramStr)
        for (const [key, value] of searchParams.entries()) {
          acc[key] = value
        }
      }
      return acc
    }, {})
  }

  const loginFn = async params => {
    loading.value = true
    const { from, ...data } = params
    await actionLogin(data)
    // reported(from);
    const { userInfo = {}, clearUserInfo } = userStore()
    const { roleCodes = [] } = userInfo
    console.log('roleCodes', roleCodes.value)
    console.log('VITE_ROLE_CODE', VITE_ROLE_CODE)
    if (!roleCodes.find(code => code === VITE_ROLE_CODE)) {
      permissionDenied.value = true
      clearUserInfo()
      return
    }
    const { query = {} } = router.currentRoute.value
    const redirect = query.redirect || '/',
      otherQuery = getOtherQuery(query)
    await router.replace({
      path: redirect,
      query: otherQuery,
    })
  }

  onMounted(async () => {
    const { from = '', token = '', isAiPlatform = '', accessToken } = route.query
    try {
      setFrom(from)
      if (from === URL_FROM.DATA_TXIN && (inTxinMobile() || isInTxinPcBrowser()) && accessToken) {
        isTaijiTxin.value = true
        await loginFn({ TXinToken: accessToken, from })
      } else if ([URL_FROM.AI_ASSISTANT, URL_FROM.LIGHT_APPLICATION].includes(from) && token) {
        isAiAssistant.value = true
        let params = {
          assistantToken: token,
          from,
        }
        if (!!isAiPlatform) {
          params = {
            aiPlatformToken: token,
            from,
          }
        }
        await loginFn(params)
      } else if (from === URL_FROM.DATA && getCookie('SIAMTGT')) {
        isIdm.value = true
        await loginFn({ isIdm: true, from })
      }
    } catch (error) {
      isAiAssistant.value = false
      isIdm.value = false
      isTaijiTxin.value = false
    } finally {
      loading.value = false
    }
  })
</script>
<template>
  <template v-if="isAiAssistant || isIdm || isTaijiTxin">
    <div v-if="loading" class="w-full h-full flex items-center justify-center">
      <svg-icon type="three-dots" style="color: rgba(99, 85, 255, 1)" />
    </div>
    <div v-if="permissionDenied" class="w-full h-full flex items-center justify-center text-[16px]">
      <div>你没有访问该系统的权限，请联系管理员分配权限。</div>
    </div>
  </template>
  <template v-else>
    <div
      v-if="isMobile"
      class="flex justify-center w-screen h-full bg-no-repeat bg-cover bg-[url('/images/bg-login.png')]"
    >
      <div class="w-5/6">
        <img class="h-[23px] my-[100px]" src="../../assets/tcl-five-rings.svg" alt="tcl" style="aspect-ratio" />
        <LoginForm class="bg-white" :loginFn="loginFn" :permissionDenied="permissionDenied" />
      </div>
    </div>
    <div
      v-else
      class="w-full h-full flex flex-col justify-center items-center bg-[url('/images/bg-login.png')] bg-no-repeat bg-cover"
    >
      <div class="flex flex-row items-center justify-between w-full">
        <div class="pt-[20px] pl-[22px] flex items-center text-[18px] font-semibold cursor-pointer">
          <img class="mr-4 h-[25px]" src="../../assets/tcl-five-rings.svg" alt="tcl" style="aspect-ratio" />
        </div>
      </div>
      <div class="flex flex-row m-auto w-[1160px] justify-between items-center">
        <img class="w-[720px] h-[507px]" src="@/assets/images/login.png" alt="login" />
        <LoginForm class="bg-white" :loginFn="loginFn" :permissionDenied="permissionDenied" />
      </div>
    </div>
  </template>
</template>

<style lang="less" scoped>
  :deep(.ant-input) {
    border-width: 0 0 1px 0;

    &:hover,
    &:focus {
      border-inline-end-width: 0 !important;
      box-shadow: none !important;
    }
  }

  :deep(.ant-input-affix-wrapper) {
    border-width: 0 0 1px 0;

    &:hover,
    &:focus {
      border-inline-end-width: 0 !important;
      box-shadow: none !important;
    }
  }
</style>
