<script setup>
import { reactive, ref } from "vue";
import encrypt from "@/common/encrypt.js";
import useWindowSize from "../../hooks/useWindowSize";

defineOptions({
  name: "LoginForm",
});

const props = defineProps({
  loginFn: {
    type: Function,
    default: () => {},
  },
  permissionDenied: {
    type: Boolean,
    default: false,
  },
});

const { isMobile } = useWindowSize();
const formState = reactive({
  username: "",
  password: "",
});
const loading = ref(false);

const onFinish = async (values) => {
  loading.value = true;
  try {
    loading.value = true;
    await props.loginFn({
      username: values.username,
      password: encrypt(formState.password),
    });
  } finally {
    loading.value = false;
  }
};
const onFinishFailed = (errorInfo) => {
  console.log("Failed:", errorInfo);
};
</script>
<template>
  <div class="px-12 rounded py-14">
    <div class="text-2xl font-semibold mb-7">登录</div>
    <div :class="isMobile ? 'w-full' : 'w-[284px]'">
      <a-form
        layout="vertical"
        :model="formState"
        @finish="onFinish"
        @finishFailed="onFinishFailed"
      >
        <div v-if="permissionDenied" class="py-2 text-[12px] text-[#ff4f55]">
          你没有访问该系统的权限，请联系管理员分配权限。<br />
          Sorry, the current system only supports Chinese.
        </div>
        <a-form-item
          label="账号"
          name="username"
          :rules="[{ required: true, message: '请输入账号' }]"
        >
          <a-input
            v-model:value="formState.username"
            placeholder="请输入账号"
          />
        </a-form-item>
        <a-form-item
          label="密码"
          name="password"
          :rules="[{ required: true, message: '请输入密码' }]"
        >
          <a-input-password
            v-model:value="formState.password"
            placeholder="请输入密码"
          />
        </a-form-item>
        <a-form-item>
          <a-button
            class="!text-base rounded mt-9"
            type="primary"
            :loading="loading"
            html-type="submit"
            block
          >
            登录
          </a-button>
        </a-form-item>
      </a-form>
    </div>
  </div>
</template>

<style lang="less" scoped>
:deep(.ant-input) {
  border-width: 0 0 1px 0;

  &:hover,
  &:focus {
    border-inline-end-width: 0 !important;
    box-shadow: none !important;
  }
}

:deep(.ant-input-affix-wrapper) {
  border-width: 0 0 1px 0;

  &:hover,
  &:focus {
    border-inline-end-width: 0 !important;
    box-shadow: none !important;
  }
}
.\!text-base {
  font-size: 1rem !important;
  line-height: 1rem !important;
}
</style>
