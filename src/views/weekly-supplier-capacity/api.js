import { CustomRequest } from '@/utils/CustomRequest'

// 获取产能管理-供应商日产能维护列表
export const getSupplierDailyCapacityList = data => {
  return CustomRequest({
    url: `/purchase/v1/tenant/supplierCapacityDataN3/pageQuery`,
    method: 'post',
    data,
  })
}

// N+3月供应商产能数据 - 分页查询
export const pageN3SupplierCapacityData = data => {
  return CustomRequest({
    url: `/purchase/v1/tenant/supplierCapacityDataN3/pageQuery`,
    method: 'post',
    data,
  })
}

// N+3月供应商产能数据 - 删除
export const deleteN3SupplierCapacityDataApi = data => {
  return CustomRequest({
    url: `/purchase/v1/tenant/supplierCapacityDataN3/del`,
    method: 'post',
    data,
  })
}

// N+3月供应商产能数据 - 失效
export const disableN3SupplierCapacityDataApi = data => {
  return CustomRequest({
    url: `/purchase/v1/tenant/supplierCapacityDataN3/changeStatus`,
    method: 'post',
    data,
  })
}

// N+3月供应商产能数据 - 确认
export const confirmN3SupplierCapacityDataApi = data => {
  return CustomRequest({
    url: `/purchase/v1/tenant/supplierCapacityDataN3/changeStatus`,
    method: 'post',
    data,
  })
}

// N+3月供应商产能数据 - 导入
export const importN3SupplierCapacityDataApi = data => {
  return CustomRequest({
    url: `/purchase/v1/tenant/supplierCapacityDataN3/excel/import`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
}

// N+3月供应商产能数据 - 导出
export const exportN3SupplierCapacityDataApi = data => {
  return CustomRequest({
    url: `/purchase/v1/tenant/supplierCapacityDataN3/excel/exportData`,
    method: 'post',
    data,
    responseType: 'blob',
  })
}

// N+3月供应商产能数据 - 模板下载
export const importDownloadN3SupplierCapacityDataApi = data => {
  return CustomRequest({
    url: `/purchase/v1/tenant/supplierCapacityDataN3/downloadImportTemplate`,
    method: 'post',
    data,
    responseType: 'blob',
  })
}
