<template>
  <div class="w-full h-full">
    <div class="w-full space-y-6">
      <!-- AI 总结 - 主要内容区域第一块 -->
      <AISummaryCard :request-params="summarizeAIParams" class="mb-[16px]" :id="'summarizeAIId'">
        <template #title>总结</template>
      </AISummaryCard>
      <!-- 年度供需Gap热力图 -->
      <!-- <HeatmapChart
        ref="heatmapRef"
        title="年度供需Gap热力图"
        subtitle="各产线平台12个月的供需缺口天数分布"
        chart-title="年度供需Gap热力图"
        :height="400"
        :time-axis="timeAxis"
        :dimension-axis="dimensionAxis"
        :data="heatmapData"
        :value-range="[0, 30]"
        :color-config="colorConfig"
        unit="天"
        :show-label="true"
        @cell-click="handleCellClick"
        @selection-change="handleSelectionChange"
      /> -->
      <div class="flex items-center seleted-item w-[800px]">
        <span class="text-[#f86d62]">*</span> <span class="mr-[4px]">物料编号</span
        ><MaterialSelete v-model:material-id="materialId" v-model:material-item="materialItem"></MaterialSelete>
      </div>
      <div class="collapse-card-container">
        <a-collapse ghost v-model:activeKey="activeKey">
          <template #expandIcon="{ isActive }">
            <caret-right-outlined :rotate="isActive ? 90 : 0" />
          </template>

          <a-collapse-panel key="1" :showArrow="false">
            <template #header>
              <div class="target-header-info">
                <div class="target-header-info-title">
                  <!-- <span class="colorful-block"></span> -->
                  <div>物料编号：{{ materialItem.label }}</div>
                </div>
                <div class="right-toggle-icon">
                  <svg-icon
                    type="down-arrow"
                    class="w-12 h-12 !mr-0 text-[#333] transition-transform"
                    :class="{ 'rotate-90': activeKey.includes('1') }"
                  />
                </div>
              </div>
            </template>
            <div class="target-main">
              <a-spin :spinning="lineChartLoading" size="large" tip="加载中...">
                <LineChart
                  :chartTitle="materialItem.label ? `${materialItem.label}产能供需分析趋势图` : ''"
                  :titleTooltip="trendTooltip"
                  :height="300"
                  :x-axis-data="timeAxis"
                  :series="lineChartSeries"
                  :extra="extra"
                  class="bg-white rounded-lg shadow-md px-[16px] py-[16px]"
                />
              </a-spin>
              <div class="table-wrapper bg-white rounded-lg shadow-md px-[16px] py-[16px] mt-[16px]">
                <!-- 表格标题 -->
                <TitleWithTooltip title="最优解策略" class="table-title" />
                <YearlySupplyAndDemand :materialId="materialId" />
              </div>
            </div>
            <AISummaryCard
              v-if="isStrategyAI"
              :request-params="strategyAIParams"
              class="mt-[24px]"
              :customStyle="'background: rgba(234, 238, 246, 0.6);'"
              :summaryHeight="200"
              :id="'strategyAIId'"
            >
              <template #title>解决方案</template>
            </AISummaryCard>
          </a-collapse-panel>
        </a-collapse>
      </div>
    </div>
  </div>
</template>

<script>
  import { ref, onMounted, watch, nextTick } from 'vue'
  import YearlySupplyAndDemand from './components/YearlySupplyAndDemand.vue'
  import AISummaryCard from '@/views/components/AISummaryCard.vue'
  import MaterialSelete from '@/views/components/MaterialSelete.vue'
  // import HeatmapChart from '@/components/HeatmapChart/index.vue'
  import LineChart from '@/components/LineChart/index.vue'
  import TitleWithTooltip from '@/components/TitleWithTooltip/index.vue'
  import { purchaseReportTrend } from '@/api/weekly.js'
  import { conmonConfig } from '@/api/common.js'

  export default {
    name: 'WeeklyAnalysisReport',
    components: {
      YearlySupplyAndDemand,
      AISummaryCard,
      MaterialSelete,
      LineChart,
      TitleWithTooltip,
    },
    setup() {
      const materialId = ref('') // 物料编号
      const materialItem = ref({}) // 物料选项
      // AI 摘要请求参数总结
      const summarizeAIParams = ref({
        inputs: {
          businessType: 'summarize',
          granularityType: '1',
        },
        query: '1',
        user: 'role',
        response_mode: 'streaming',
      })
      // 解决方案策略
      const strategyAIParams = ref({})

      const activeKey = ref(['1'])

      const lineChartLoading = ref(false)
      // 热力图配置数据
      const timeAxis = ref([])
      // 额外数据
      const extra = ref({ demandQty: [], capacityQty: [], gapQty: [], rate: [] })
      // 折线图系列配置
      const lineChartSeries = ref([])
      const isStrategyAI = ref(false)
      const trendTooltip = ref('')
      const init = async () => {
        const res = await conmonConfig('trend')
        if (res) {
          trendTooltip.value = res
        }
      }
      onMounted(() => {
        init()
      })
      const updateStrategyAI = val => {
        isStrategyAI.value = false
        console.log('===>val', val)
        strategyAIParams.value = {
          inputs: {
            businessType: 'strategy',
            granularityType: '1',
            materialCode: val,
          },
          query: '1',
          user: 'role',
          response_mode: 'streaming',
        }
        nextTick(() => {
          isStrategyAI.value = true
        })
      }
      watch(
        () => materialId.value,
        async val => {
          if (!val) return
          try {
            // 更新总结方案
            updateStrategyAI(val)
            lineChartLoading.value = true
            const res = await purchaseReportTrend(val)

            if (res?.length > 0) {
              timeAxis.value = res.map(item => item.xName)

              for (const key in res?.[0]) {
                extra.value[key] = res.map(item => item?.[key])
              }

              // 热力图系列数据
              lineChartSeries.value = [
                {
                  name: '需求',
                  data: extra.value.demandQty,
                  color: '#779BFC',
                  lineWidth: 2,
                  icon: 'line-chart-tooltip-2',
                },
                {
                  name: '供应+上周累缺',
                  data: extra.value.capacityQty,
                  color: '#6355FF',
                  lineWidth: 2,
                  icon: 'line-chart-tooltip-1',
                },
              ]
            }
          } finally {
            // console.log('finally')
            lineChartLoading.value = false
          }
        },
        { immediate: true, deep: true }
      )

      // 热力图组件引用
      // const heatmapRef = ref(null)
      // const dimensionAxis = ref(['1200线平台', '600线平台', '750线平台', 'N3平台', 'N4平台'])

      // // 热力图数据 (模拟年度供需Gap数据)
      // const heatmapData = ref([
      //   // [月份索引, 平台索引, gap天数]
      //   // 1200线平台
      //   [0, 0, 5],
      //   [1, 0, 8],
      //   [2, 0, 12],
      //   [3, 0, 3],
      //   [4, 0, 2],
      //   [5, 0, 15],
      //   [6, 0, 18],
      //   [7, 0, 22],
      //   [8, 0, 25],
      //   [9, 0, 20],
      //   [10, 0, 16],
      //   [11, 0, 10],

      //   // 600线平台
      //   [0, 1, 3],
      //   [1, 1, 6],
      //   [2, 1, 9],
      //   [3, 1, 1],
      //   [4, 1, 4],
      //   [5, 1, 12],
      //   [6, 1, 15],
      //   [7, 1, 19],
      //   [8, 1, 23],
      //   [9, 1, 18],
      //   [10, 1, 14],
      //   [11, 1, 8],

      //   // 750线平台
      //   [0, 2, 7],
      //   [1, 2, 10],
      //   [2, 2, 14],
      //   [3, 2, 5],
      //   [4, 2, 6],
      //   [5, 2, 17],
      //   [6, 2, 20],
      //   [7, 2, 24],
      //   [8, 2, 27],
      //   [9, 2, 22],
      //   [10, 2, 18],
      //   [11, 2, 12],

      //   // N3平台
      //   [0, 3, 2],
      //   [1, 3, 4],
      //   [2, 3, 7],
      //   [3, 3, 0],
      //   [4, 3, 1],
      //   [5, 3, 9],
      //   [6, 3, 12],
      //   [7, 3, 16],
      //   [8, 3, 19],
      //   [9, 3, 15],
      //   [10, 3, 11],
      //   [11, 3, 6],

      //   // N4平台
      //   [0, 4, 9],
      //   [1, 4, 12],
      //   [2, 4, 16],
      //   [3, 4, 7],
      //   [4, 4, 8],
      //   [5, 4, 19],
      //   [6, 4, 22],
      //   [7, 4, 26],
      //   [8, 4, 29],
      //   [9, 4, 24],
      //   [10, 4, 20],
      //   [11, 4, 14],
      // ])

      // 热力图颜色配置
      // const colorConfig = ref({
      //   inRange: {
      //     color: [
      //       '#313695',
      //       '#4575b4',
      //       '#74add1',
      //       '#abd9e9',
      //       '#e0f3f8',
      //       '#ffffbf',
      //       '#fee090',
      //       '#fdae61',
      //       '#f46d43',
      //       '#d73027',
      //       '#a50026',
      //     ],
      //   },
      //   textStyle: {
      //     color: '#999',
      //   },
      // })
      // 处理热力图单元格点击事件
      // const handleCellClick = cellData => {
      //   console.log('热力图单元格点击:', cellData)
      //   // 这里可以根据点击的数据进行其他模块的查询
      //   // cellData 包含: { x, y, value, timeLabel, dimensionLabel }
      // }

      // // 处理热力图选中状态变化事件
      // const handleSelectionChange = cellData => {
      //   console.log('热力图选中状态变化:', cellData)
      //   // 这里可以根据选中的数据更新其他组件
      // }

      return {
        materialId,
        materialItem,
        summarizeAIParams,
        strategyAIParams,
        activeKey,
        lineChartLoading,
        timeAxis,
        lineChartSeries,
        extra,
        trendTooltip,
        isStrategyAI,
      }
    },
  }
</script>

<style lang="less" scoped>
  :deep(.ant-collapse > .ant-collapse-item > .ant-collapse-header) {
    padding-bottom: 12px;
    padding-left: 16px;
  }
  :deep(.ant-collapse-content > .ant-collapse-content-box) {
    padding-top: 12px;
  }
  :deep(.ant-collapse-content-box) {
    padding-top: 0px !important;
  }
  .collapse-card-container {
    border-radius: 16px;
    background-color: #f9fbfd;
    // padding: 16px;
    margin-top: 16px !important;
    .target-header-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      .colorful-block {
        width: 12px;
        height: 12px;
        display: inline-block;
        background: #6355ff;
        margin-right: 8px;
      }
      &-title {
        display: flex;
        flex-direction: row;
        align-items: center;
        font-family: PingFang SC;
        font-size: 16px;
        font-weight: 500;
        line-height: 16px;
        letter-spacing: 0px;

        color: #333333;
      }
    }
    .right-toggle-icon {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 24px;
      height: 24px;
      border-radius: 6px;
      opacity: 1;
      border: 1.33px solid #dddddd;

      /* 图标旋转动画优化 */
      svg {
        transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        transform-origin: center;
      }
    }
    .target-main {
      // background-color: #fff;
      border-radius: 16px;
      // padding: 16px;
    }
  }
  .seleted-item {
    margin-top: 0 !important;
    font-family: PingFang SC;
    font-size: 12px;
    font-weight: normal;
    line-height: 20px;

    letter-spacing: 0px;

    font-variation-settings: 'opsz' auto;
    /* * */

    :deep(.ant-select-selector) {
      height: 24px !important;
      .ant-select-selection-item {
        line-height: 24px;
      }
      .ant-select-selection-search-input {
        height: 24px !important;
      }
    }
  }
</style>
