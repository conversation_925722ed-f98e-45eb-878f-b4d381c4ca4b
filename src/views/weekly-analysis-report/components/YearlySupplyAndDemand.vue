<template>
  <div class="pt-[16px] pb-[16px] rounded-lg">
    <a-table
      v-if="isTotal"
      :columns="columns"
      :data-source="dataSource"
      :pagination="false"
      :bordered="true"
      size="small"
      :loading="loading"
      class="rounded-md overflow-hidden"
      :scroll="{ x: 'max-content' }"
      :rowClassName="getRowClassName"
    >
      <template #bodyCell="{ column, record, index }">
        <!-- 序号列的处理 -->
        <template v-if="column.key === 'index'">
          <span class="font-medium text-gray-800">{{ index + 1 }}</span>
        </template>

        <!-- 料号的处理 -->
        <template v-else-if="column.key === 'materialCode'">
          <span class="font-medium text-gray-800" v-if="colSpanList.includes(record?.type)">
            {{ record?.type }}
          </span>
          <span class="font-medium text-gray-800" v-else>
            {{ record?.materialCode }}
          </span>
        </template>

        <!-- 线体列处理 -->
        <template v-else-if="column.key === 'productLineType'">
          <span v-if="!colSpanList.includes(record?.type)">
            {{ record?.productLineType }}
          </span>
        </template>

        <!-- 类型列处理 -->
        <template v-else-if="column.key === 'type'">
          <span class="font-normal" :class="getTypeClass(record.type)" v-if="!colSpanList.includes(record?.type)">
            {{ record.type }}
          </span>
        </template>

        <!-- 动态月份数据列处理 -->
        <template v-else>
          <span>{{ record[column.key] }}</span>
        </template>
      </template>
    </a-table>
  </div>
</template>

<script setup>
  import { toRefs } from 'vue'
  import { useYearlySupplyAndDemand } from './useYearlySupplyAndDemand.js'

  const props = defineProps({
    materialId: {
      type: String,
      default: '',
    },
  })

  const { materialId } = toRefs(props)

  // 使用封装的业务逻辑
  const { loading, columns, dataSource, colSpanList, isTotal, getTypeClass, getRowClassName } =
    useYearlySupplyAndDemand(materialId)
</script>

<style lang="less" scoped>
  /* 表格样式覆盖 - 使用Tailwind颜色值 */
  :deep(.ant-table) {
    font-size: 0.75rem; /* text-xs */
  }

  :deep(.ant-table-thead > tr > th) {
    background-color: #f1f4f9 !important;
    font-weight: 500 !important;
    color: rgba(51, 51, 51, 0.6) !important;
    font-size: 14px !important;
  }

  :deep(.ant-table-tbody > tr > td) {
    background-color: #ffffff;
    border-bottom: 1px solid #f3f4f6; /* border-b border-gray-60 */
    color: #333333;
  }

  :deep(.ant-table-tbody > tr:hover > td) {
    background: #e6efff !important; /* bg-gray-50 */
  }

  :deep(.ant-table-tbody) {
    .d8edff {
      td {
        background-color: #d8edff !important;
      }
    }
  }

  :deep(.ant-table-cell) {
    padding: 0 !important;

    span {
      display: block;
      padding: 8px;
    }
  }
</style>
