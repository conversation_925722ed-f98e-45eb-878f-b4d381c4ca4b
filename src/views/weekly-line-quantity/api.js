import { CustomRequest } from '@/utils/CustomRequest'

// 供应商产线数据 - 分页查询
export const pageSupplierLineMaterialRelationApi = data => {
  return CustomRequest({
    url: `/purchase/v1/tenant/relation/pageQuery`,
    method: 'post',
    data,
  })
}

// 供应商产线数据 - 保存
export const saveSupplierLineMaterialRelationApi = data => {
  return CustomRequest({
    url: `/purchase/v1/tenant/relation/save`,
    method: 'post',
    data,
  })
}

// 供应商产线数据 - 删除
export const deleteSupplierLineMaterialRelationApi = data => {
  return CustomRequest({
    url: `/purchase/v1/tenant/relation/del`,
    method: 'post',
    data,
  })
}

// 供应商产线数据 - 启用
export const enableSupplierLineMaterialRelationApi = data => {
  return CustomRequest({
    url: `/purchase/v1/tenant/relation/changeStatus`,
    method: 'post',
    data,
  })
}

// 供应商产线数据 - 停用
export const disableSupplierLineMaterialRelationApi = data => {
  return CustomRequest({
    url: `/purchase/v1/tenant/relation/changeStatus`,
    method: 'post',
    data,
  })
}

// 供应商产线数据 - 确认
export const confirmSupplierLineMaterialRelationApi = data => {
  return CustomRequest({
    url: `/purchase/v1/tenant/relation/changeStatus`,
    method: 'post',
    data,
  })
}

// 供应商产线数据 - 导入
export const importSupplierLineMaterialRelationApi = data => {
  return CustomRequest({
    url: `/purchase/v1/tenant/relation/excel/import`,
    method: 'post',
    data,
    responseType: 'blob',
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
}

// 供应商产线数据 - 导入模板下载
export const importDownloadSupplierLineMaterialRelationApi = data => {
  return CustomRequest({
    url: `/purchase/v1/tenant/relation/downloadImportTemplate`,
    method: 'post',
    data,
    responseType: 'blob',
  })
}

// 供应商产线数据 - 导出
export const exportSupplierLineMaterialRelationApi = data => {
  return CustomRequest({
    url: `/purchase/v1/tenant/relation/excel/exportData`,
    method: 'post',
    data,
    responseType: 'blob',
  })
}
