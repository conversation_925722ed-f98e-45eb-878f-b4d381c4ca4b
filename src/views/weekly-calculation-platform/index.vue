<template>
  <div class="calculation-platform">
    <!-- 搜索条件区域 -->
    <div class="search-container">
      <div class="search-header">
        <span class="search-title">查询条件</span>
        <div class="search-actions">
          <a-button @click="handleReset">重置</a-button>
          <a-button type="primary" @click="handleSearch">查询</a-button>
        </div>
      </div>

      <div class="search-form">
        <a-row :gutter="16" class="search-row">
          <a-col :span="8">
            <a-form-item label="创建人">
              <a-input size="small" v-model:value="searchForm.name" placeholder="请输入" allowClear />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="配置时间">
              <!-- <a-date-picker
                size="small"
                v-model:value="searchForm.configTime"
                placeholder="请选择时间"
                style="width: 100%"
              /> -->
              <a-range-picker
                size="small"
                v-model:value="searchForm.createTimeRange"
                @change="e => handleDateChange(e, 'createTimeRange')"
              />
            </a-form-item>
          </a-col>
          <!-- <a-col :span="8">
            <a-form-item label="状态">
              <a-select size="small" v-model:value="searchForm.status" placeholder="请选择状态" allowClear>
                <a-select-option value="running">进行中</a-select-option>
                <a-select-option value="pending">待运行</a-select-option>
                <a-select-option value="completed">已完成</a-select-option>
              </a-select>
            </a-form-item>
          </a-col> -->
          <!-- <a-col :span="8">
            <a-form-item label="版本号">
              <a-input size="small" v-model:value="searchForm.version" placeholder="请输入版本号" allowClear />
            </a-form-item>
          </a-col> -->
        </a-row>
      </div>
    </div>

    <!-- 任务列表区域 -->
    <div class="table-container">
      <div class="table-header">
        <span class="table-title">任务列表</span>
        <div class="table-actions">
          <a-button @click="handleDelete" :disabled="!hasSelected">删除</a-button>
          <a-button type="primary" @click="handleAdd">新增</a-button>
        </div>
      </div>

      <a-table
        :columns="columns"
        :data-source="tableData"
        :row-selection="rowSelection"
        :pagination="pagination"
        :loading="loading"
        row-key="id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="text" size="small" @click="handleView(record)" :disabled="record.status !== 2"
                >查看</a-button
              >
              <a-button type="text" size="small" @click="handleCopy(record)"> 复制 </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>

    <!-- 配置弹框 -->
    <ConfigModal :open="configModalVisible" @update:open="configModalVisible = $event" @success="handleConfigSuccess" />

    <!-- 报表查看弹框 -->
    <ReportModal
      :open="reportModalVisible"
      @update:open="reportModalVisible = $event"
      :version-no="selectedVersionNo"
    />
  </div>
</template>

<script>
  import { ref, reactive, computed, onMounted } from 'vue'
  import { getCalculateTaskList, delCalculateTask, copyTask } from '@/api/calculate'
  import { message, Modal } from 'ant-design-vue'
  import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
  import { createVNode } from 'vue'
  import dayjs from 'dayjs'
  import ConfigModal from './components/ConfigModal.vue'
  import ReportModal from './components/ReportModal.vue'
  export default {
    name: 'WeeklyCalculationPlatform',
    components: {
      ConfigModal,
      ReportModal,
    },
    setup() {
      // 响应式数据
      const activeTab = ref('platform')
      const loading = ref(false)
      const selectedRowKeys = ref([])
      const configModalVisible = ref(false)
      const reportModalVisible = ref(false)
      const selectedVersionNo = ref('')

      // 搜索表单
      const searchForm = reactive({
        creator: undefined,
        configTime: undefined,
        status: undefined,
        version: undefined,
        createTimeRange: undefined,
        createStartTime: undefined,
        createEndTime: undefined,
      })

      // 表格列定义
      const columns = [
        {
          title: '序号',
          dataIndex: 'id',
          key: 'id',
          width: 80,
        },
        {
          title: '版本号',
          dataIndex: 'versionNo',
          key: 'versionNo',
          width: 120,
        },
        {
          title: '创建人',
          dataIndex: 'creator',
          key: 'creator',
          width: 100,
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          key: 'createTime',
          width: 180,
          sorter: (a, b) => new Date(a.createTime) - new Date(b.createTime),
          sortDirections: ['descend', 'ascend'],
          // defaultSortOrder: 'descend',
        },
        {
          title: '状态',
          dataIndex: 'status',
          key: 'status',
          width: 100,
        },
        {
          title: '操作',
          key: 'action',
          width: 150,
        },
      ]

      // 表格数据
      const tableData = ref([])

      // 分页配置
      const pagination = reactive({
        current: 1,
        pageSize: 10,
        total: 0,
        showSizeChanger: false,
        showQuickJumper: true,
        showTotal: total => `共 ${total} 条`,
        onChange: (page, pageSize) => {
          pagination.current = page
          pagination.pageSize = pageSize
          fetchTaskList()
        },
      })

      // 行选择配置
      const rowSelection = {
        selectedRowKeys: selectedRowKeys,
        onChange: keys => {
          selectedRowKeys.value = keys
        },
      }

      // 计算属性
      const hasSelected = computed(() => selectedRowKeys.value.length > 0)

      // 获取任务列表数据
      const fetchTaskList = async () => {
        try {
          loading.value = true
          const params = {
            page: pagination.current,
            pageSize: pagination.pageSize,
            ...searchForm,
          }

          const response = await getCalculateTaskList(params)
          console.log('response', response)
          if (response) {
            tableData.value = response.records || []
            pagination.total = response.total || 0
          }
        } catch (error) {
          console.error('获取任务列表失败:', error)
          message.error('获取任务列表失败')
        } finally {
          loading.value = false
        }
      }
      const handleDateChange = (e, field) => {
        console.log(e, field)
        if (!e || e.length !== 2) {
          // 如果没有选择日期或日期不完整，清空相关字段
          if (field === 'createTimeRange') {
            searchForm.createStartTime = null
            searchForm.createEndTime = null
          }
          return
        }

        const [startDate, endDate] = e
        console.log('startDate', startDate)
        console.log('endDate', endDate)

        if (field === 'createTimeRange') {
          searchForm.createStartTime = startDate ? dayjs(startDate).startOf('day').format('YYYY-MM-DD HH:mm:ss') : null
          searchForm.createEndTime = endDate ? dayjs(endDate).endOf('day').format('YYYY-MM-DD HH:mm:ss') : null
        }
      }
      // 方法
      const handleReset = () => {
        Object.keys(searchForm).forEach(key => {
          searchForm[key] = undefined
        })
        pagination.current = 1
        fetchTaskList()
      }

      const handleSearch = () => {
        pagination.current = 1
        fetchTaskList()
      }

      const handleImport = () => {
        console.log('导入')
      }

      const handleExport = () => {
        console.log('导出')
      }

      const handleDelete = async () => {
        // 批量删除
        if (selectedRowKeys.value.length === 0) {
          message.warning('请先选择要删除的任务')
          return
        }

        Modal.confirm({
          title: `是否确认删除选中的 ${selectedRowKeys.value.length} 条任务?`,
          icon: createVNode(ExclamationCircleOutlined),
          content: createVNode('div', { style: 'color:red;' }, '删除后无法恢复，请谨慎操作！'),
          async onOk() {
            try {
              const res = await delCalculateTask(selectedRowKeys.value)
              message.success(`成功删除 ${selectedRowKeys.value.length} 条任务`)
              // 清空选择
              selectedRowKeys.value = []
              // 刷新列表
              fetchTaskList()
            } catch (error) {
              console.error('批量删除失败:', error)
              // message.error('批量删除失败')
            }
          },
          onCancel() {
            console.log('取消批量删除')
          },
        })
      }

      const handleAdd = () => {
        configModalVisible.value = true
      }

      const handleConfigSuccess = () => {
        // 配置成功后刷新列表
        fetchTaskList()
      }

      const handleView = record => {
        if (record.status !== 2) {
          message.warning('只有已完成的任务才能查看报表')
          return
        }

        if (!record.versionNo) {
          message.warning('该任务缺少版本号，无法查看报表')
          return
        }

        selectedVersionNo.value = record.versionNo
        reportModalVisible.value = true
      }

      const handleCopy = async record => {
        try {
          Modal.confirm({
            title: '确认复制任务',
            async onOk() {
              try {
                await copyTask(record.id)
                message.success('任务复制成功')
                // 刷新任务列表
                fetchTaskList()
              } catch (error) {
                console.error('复制任务失败:', error)
                message.error('复制任务失败，请重试')
              }
            },
          })
        } catch (error) {
          console.error('复制任务失败:', error)
          message.error('复制任务失败，请重试')
        }
      }

      const getStatusColor = status => {
        const colorMap = {
          0: 'rgba(253, 239, 167, 1)',
          1: 'rgba(226, 240, 253, 1)',
          2: 'rgba(214, 252, 227, 1)',
        }
        return colorMap[status] || 'default'
      }

      const getStatusText = status => {
        // 0：待运行；1：进行中；2：已完成；3：失败
        const textMap = {
          0: '待运行',
          1: '进行中',
          2: '已完成',
          3: '已失败',
        }
        return textMap[status] || status
      }

      // 初始化数据
      onMounted(() => {
        fetchTaskList()
      })

      return {
        activeTab,
        loading,
        selectedRowKeys,
        configModalVisible,
        reportModalVisible,
        selectedVersionNo,
        searchForm,
        columns,
        tableData,
        pagination,
        rowSelection,
        hasSelected,
        fetchTaskList,
        handleReset,
        handleSearch,
        handleImport,
        handleExport,
        handleDelete,
        handleAdd,
        handleConfigSuccess,
        handleView,
        handleCopy,
        getStatusColor,
        getStatusText,
        handleDateChange,
      }
    },
  }
</script>

<style scoped lang="less">
  .tabs-container {
    background: white;
    border-radius: 8px;
    margin-bottom: 16px;
    padding: 0 16px;
  }

  .search-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }

  .search-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
  }

  .search-actions {
    display: flex;
    gap: 8px;
  }

  .search-form {
    /* 搜索表单容器 */
    position: relative;
  }

  .search-row {
    width: 100%;
    margin-bottom: 16px;
  }

  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }

  .table-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
  }

  .table-actions {
    display: flex;
    gap: 8px;
  }

  :deep(.ant-table) {
    &-thead tr > th {
      font-variation-settings: 'opsz' auto;
      color: rgba(51, 51, 51, 0.6);
    }
    &-tbody > tr > td.ant-table-cell-row-hover {
      background: rgba(99, 85, 255, 0.1);
    }
  }

  // 复制按钮置灰样式
  :deep(.ant-btn[disabled]) {
    color: rgba(0, 0, 0, 0.25) !important;
    cursor: not-allowed;

    &:hover {
      color: rgba(0, 0, 0, 0.25) !important;
    }
  }
  :deep(.ant-tag) {
    color: #000;
    border-radius: 8px;
    font-size: 12px;
    padding: 2px 8px;
  }
</style>
