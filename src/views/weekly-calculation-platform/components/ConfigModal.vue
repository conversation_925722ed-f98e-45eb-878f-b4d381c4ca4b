<template>
  <a-modal
    v-model:visible="visible"
    title="测算参数配置"
    width="632px"
    :footer="null"
    @cancel="handleCancel"
    :get-container="getContainer"
  >
    <a-spin size="small" tip="加载品类中..." :spinning="categoryLoading">
      <div class="config-modal">
        <!-- 品类和物料选择 -->
        <div class="category-select">
          <a-row :gutter="24">
            <a-col :span="24">
              <a-form-item label="品类">
                <a-select
                  v-model:value="selectedCategory"
                  :options="categoryList"
                  placeholder="请选择品类"
                  size="small"
                  :loading="categoryLoading"
                  @change="handleCategoryChange"
                  show-search
                  :filter-option="filterOption"
                />
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="物料">
                <a-select
                  v-model:value="selectedMaterial"
                  :options="materialList"
                  placeholder="请选择物料"
                  size="small"
                  :max-tag-count="2"
                  :loading="materialLoading"
                  :disabled="!selectedCategory"
                  @change="handleMaterialChange"
                  show-search
                  :filter-option="filterOption"
                  mode="multiple"
                  allowClear
                />
              </a-form-item>
            </a-col>
          </a-row>
        </div>

        <!-- 配置项详情 -->
        <div class="config-details">
          <div class="section-header">
            <span class="section-title">配置项详情</span>
            <div class="reload-icon-wrapper" @click="refreshConfig">
              <svg-icon type="reload" class="reload-icon reload-icon-normal" />
              <svg-icon type="reload-hover" class="reload-icon reload-icon-hover" />
            </div>
          </div>

          <a-table
            :columns="configColumns"
            :data-source="configList"
            :pagination="false"
            :loading="configLoading"
            row-key="id"
            size="small"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'status'">
                <a-tag :color="getStatusColor(record.status)" size="small" class="config-tag">
                  {{ getStatusText(record.status) }}
                </a-tag>
              </template>
              <template v-if="column.key === 'action'">
                <a-button type="link" size="small" @click="handleConfig(record)"> 配置 </a-button>
              </template>
            </template>
          </a-table>
        </div>

        <!-- 底部按钮 -->
        <div class="modal-footer">
          <a-button @click="handleCancel">取消</a-button>
          <a-button type="primary" @click="handleCalculate" :loading="calculating"> 测算 </a-button>
        </div>
      </div>
    </a-spin>
  </a-modal>
</template>

<script>
  import { ref, watch } from 'vue'
  import { useRouter } from 'vue-router'
  import { ReloadOutlined } from '@ant-design/icons-vue'
  import { getConfigDetails, saveCalculateConfig, getCategoryList, getMaterialList } from '@/api/calculate'
  import { message } from 'ant-design-vue'

  export default {
    name: 'ConfigModal',
    components: {
      ReloadOutlined,
    },
    props: {
      open: {
        type: Boolean,
        default: false,
      },
    },
    emits: ['update:open', 'success'],
    setup(props, { emit }) {
      const router = useRouter()
      const visible = ref(false)
      const selectedCategory = ref(undefined)
      const selectedMaterial = ref([])
      const categoryList = ref([])
      const materialList = ref([])
      const configList = ref([])
      const categoryLoading = ref(false)
      const materialLoading = ref(false)
      const configLoading = ref(false)
      const calculating = ref(false)

      // 表格列定义
      const configColumns = [
        {
          title: '序号',
          dataIndex: 'id',
          key: 'id',
          align: 'center',
          width: 180,
        },
        {
          title: '配置项',
          dataIndex: 'configName',
          align: 'center',
          key: 'configName',
        },
        {
          title: '状态',
          dataIndex: 'status',
          key: 'status',
          align: 'center',
        },
        {
          title: '操作',
          key: 'action',
          align: 'center',
        },
      ]

      // 监听 props.open 变化
      watch(
        () => props.open,
        newVal => {
          visible.value = newVal
        },
        { immediate: true }
      )

      // 监听 visible 变化，同步到父组件
      watch(visible, newVal => {
        emit('update:open', newVal)
        if (newVal) {
          fetchCategoryList()
        }
      })

      // 获取品类列表
      const fetchCategoryList = async () => {
        try {
          categoryLoading.value = true
          const response = await getCategoryList({})
          if (response && Array.isArray(response)) {
            categoryList.value = response.map(item => ({
              label: String(item.name || item.label || ''),
              value: String(item.code || item.value || item.id || ''),
            }))
          }
        } catch (error) {
          console.error('获取品类列表失败:', error)
          categoryList.value = []
        } finally {
          categoryLoading.value = false
        }
      }

      // 获取物料列表
      const fetchMaterialList = async categoryCode => {
        try {
          materialLoading.value = true
          const response = await getMaterialList({ categoryCode })
          if (response && Array.isArray(response)) {
            materialList.value = response.map(item => ({
              label: String(item.name || item.label || ''),
              value: String(item.code || item.value || item.id || ''),
            }))
          }
        } catch (error) {
          console.error('获取物料列表失败:', error)
          materialList.value = []
        } finally {
          materialLoading.value = false
        }
      }

      // 下拉框搜索过滤函数
      const filterOption = (input, option) => {
        if (!input) return true
        // 处理不同的 option 结构
        const label = option.label || option.children || option.title || ''
        const value = option.value || ''
        const result =
          label.toString().toLowerCase().includes(input.toLowerCase()) ||
          value.toString().toLowerCase().includes(input.toLowerCase())
        return result
      }

      // 获取配置详情
      const fetchConfigDetails = async params => {
        try {
          configLoading.value = true
          const response = await getConfigDetails(params)
          if (response && response.configItemList) {
            console.log('配置详情响应:', response)
            // 处理新的数据结构
            configList.value = response.configItemList.map((item, index) => ({
              ...item,
              id: item.id || `config_${index}`,
              status: item.configReady ? 'configured' : 'unconfigured', // 转换状态字段
            }))
          }
        } catch (error) {
          console.error('获取配置详情失败:', error)
        } finally {
          configLoading.value = false
        }
      }

      // 处理品类选择变化
      const handleCategoryChange = value => {
        selectedCategory.value = value
        selectedMaterial.value = [] // 清空物料选择（改为数组）
        materialList.value = [] // 清空物料列表
        configList.value = [] // 清空配置列表

        console.log('选择的品类:', value)

        if (value) {
          // 根据品类获取物料列表
          fetchMaterialList(value)
        }
      }

      // 处理物料选择变化
      const handleMaterialChange = value => {
        selectedMaterial.value = value
        console.log('选择的物料:', value)

        if (selectedCategory.value && value && value.length > 0) {
          // 当品类和物料都有值时，查询配置信息
          const materialCodeList = value // value 已经是数组
          fetchConfigDetails({ materialCodeList })
        } else {
          configList.value = []
        }
      }

      // 刷新配置
      const refreshConfig = () => {
        if (selectedCategory.value && selectedMaterial.value && selectedMaterial.value.length > 0) {
          const materialCodeList = selectedMaterial.value // 已经是数组
          fetchConfigDetails({ materialCodeList })
        }
      }

      // 处理配置操作
      const handleConfig = record => {
        console.log('配置项:', record)

        // 根据配置项名称跳转到对应页面
        const configRouteMap = {
          供应商日产能维护: '/weekly-supplier-capacity',
          供应商库存管理: '/weekly-supplier-inventory',
          供应商产线数据: '/weekly-line-quantity',
          供应商配额自定义: '/weekly-supplier-quota',
        }

        const targetRoute = configRouteMap[record.configName]
        if (targetRoute) {
          // 关闭当前弹框
          // visible.value = false
          // 跳转到对应页面
          router.push(targetRoute)
          message.success(`正在跳转到${record.configName}页面`)
        } else {
          message.warning(`未找到${record.configName}对应的页面`)
        }
      }

      // 处理取消
      const handleCancel = () => {
        visible.value = false
        selectedCategory.value = undefined
        selectedMaterial.value = [] // 改为数组
        materialList.value = []
        configList.value = []
      }

      // 处理测算
      const handleCalculate = async () => {
        if (!selectedCategory.value) {
          message.warning('请先选择品类')
          return
        }

        if (!selectedMaterial.value || selectedMaterial.value.length === 0) {
          message.warning('请先选择物料')
          return
        }

        // // 检查是否所有配置项都已配置
        // const unConfiguredItems = configList.value.filter(item => item.status !== 'configured')
        // if (unConfiguredItems.length > 0) {
        //   message.warning('请先完成所有配置项的配置')
        //   return
        // }

        try {
          calculating.value = true

          const data = {
            materialCodeList: selectedMaterial.value, // 已经是数组
          }

          await saveCalculateConfig(data)
          message.success('测算任务创建成功')
          emit('success')
          handleCancel()
        } catch (error) {
          console.error('测算失败:', error)
        } finally {
          calculating.value = false
        }
      }

      // 获取状态颜色
      const getStatusColor = status => {
        const colorMap = {
          configured: '#D6FCE3',
          unconfigured: '#F9DBDA',
        }
        return colorMap[status] || 'default'
      }

      // 获取状态文本
      const getStatusText = status => {
        const textMap = {
          configured: '已配置',
          unconfigured: '未配置',
        }
        return textMap[status] || status
      }

      // 获取弹框挂载容器 - 挂载到测算平台页面
      const getContainer = () => {
        // 查找测算平台页面的容器
        const platformContainer = document.querySelector('.calculation-platform')
        return platformContainer
      }

      return {
        visible,
        selectedCategory,
        selectedMaterial,
        categoryList,
        materialList,
        configList,
        categoryLoading,
        materialLoading,
        configLoading,
        calculating,
        configColumns,
        handleCategoryChange,
        handleMaterialChange,
        refreshConfig,
        handleConfig,
        handleCancel,
        handleCalculate,
        getStatusColor,
        getStatusText,
        filterOption,
        getContainer,
      }
    },
  }
</script>

<style scoped lang="less">
  :deep(.ant-modal) {
    top: 160px !important;
  }
  .config-modal {
    padding: 0;
  }

  .category-select {
    margin-bottom: 32px;
  }

  .config-details {
    margin-bottom: 24px;
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }

  .section-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
  }

  .modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    padding-top: 16px;
  }

  /* 刷新图标容器样式 */
  .reload-icon-wrapper {
    position: relative;
    width: 28px;
    height: 28px;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;

    .reload-icon {
      width: 20px;
      height: 20px;
      font-size: 20px;
      transition: opacity 0.2s ease;
    }

    /* 默认状态：显示普通图标，隐藏 hover 图标 */
    .reload-icon-normal {
      opacity: 1;
    }

    .reload-icon-hover {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      opacity: 0;
    }

    /* hover 状态：隐藏普通图标，显示 hover 图标 */
    &:hover {
      .reload-icon-normal {
        opacity: 0;
      }

      .reload-icon-hover {
        opacity: 1;
      }
    }

    &:active {
      transform: scale(0.95);
    }
  }
  .config-tag {
    color: #333;
    border-radius: 8px;
  }
  :deep(.ant-select-multiple.ant-select-sm .ant-select-selection-item) {
    max-width: 270px;
  }
</style>
