<template>
  <a-modal
    v-model:visible="visible"
    title="多维报表"
    :width="800"
    :footer="null"
    :destroyOnClose="true"
    @cancel="handleCancel"
  >
    <div class="report-container">
      <div v-if="loading" class="loading-container">
        <a-spin size="large" />
        <div class="loading-text">报表加载中...</div>
      </div>
      <div v-if="!iframeUrl" class="error-container">
        <a-result status="warning" title="无法加载报表" sub-title="缺少版本号参数，无法生成报表链接" />
      </div>
      <iframe
        v-show="!loading && iframeUrl"
        :src="iframeUrl"
        class="report-iframe"
        frameborder="0"
        @load="handleIframeLoad"
        @error="handleIframeError"
      />
    </div>
  </a-modal>
</template>

<script>
  import { ref, computed, watch } from 'vue'
  import { buildReportUrl } from '@/utils/reportUtils'

  export default {
    name: 'ReportModal',
    props: {
      open: {
        type: Boolean,
        default: false,
      },
      versionNo: {
        type: String,
        default: '',
      },
    },
    emits: ['update:open'],
    setup(props, { emit }) {
      const visible = ref(false)
      const loading = ref(true)

      // 监听 props.open 变化
      watch(
        () => props.open,
        newVal => {
          console.log('ReportModal props.open changed to:', newVal)
          visible.value = newVal
        },
        { immediate: true }
      )

      // 监听 visible 变化，同步到父组件
      watch(visible, newVal => {
        console.log('ReportModal visible changed to:', newVal)
        emit('update:open', newVal)
        if (newVal) {
          loading.value = true
        }
      })

      // 构建 iframe URL
      const iframeUrl = computed(() => {
        return buildReportUrl(props.versionNo)
      })

      // iframe 加载完成
      const handleIframeLoad = () => {
        loading.value = false
      }

      // iframe 加载错误
      const handleIframeError = () => {
        loading.value = false
        console.error('报表加载失败')
      }

      // 关闭弹框
      const handleCancel = () => {
        visible.value = false
        loading.value = true
      }

      return {
        visible,
        loading,
        iframeUrl,
        handleIframeLoad,
        handleIframeError,
        handleCancel,
      }
    },
  }
</script>

<style scoped lang="less">
  .report-container {
    position: relative;
    width: 100%;
    height: 600px;
    background: #f5f5f5;
    border-radius: 6px;
    overflow: hidden;
  }

  .loading-container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #fff;
    z-index: 10;
  }

  .loading-text {
    margin-top: 16px;
    color: #666;
    font-size: 14px;
  }

  .error-container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fff;
    z-index: 10;
  }

  .report-iframe {
    width: 100%;
    height: 100%;
    border: none;
    background: #fff;
  }

  // Modal 样式覆盖
  :deep(.ant-modal-body) {
    padding: 16px;
  }

  :deep(.ant-modal-header) {
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: 0;
  }
</style>
