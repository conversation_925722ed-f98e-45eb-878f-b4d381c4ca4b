import { CustomRequest } from '@/utils/CustomRequest'

// 获取年度供应商配额&日产能维护列表
export const getYearlySupplierCapacityList = data => {
  return CustomRequest({
    url: `/purchase/v1/year/supplierCapacityData/getSupplierCapacityDataYearList`,
    method: 'post',
    data,
  })
}

// 年度供应商配额&日产能数据 - 删除
export const deleteYearlySupplierCapacityApi = data => {
  return CustomRequest({
    url: `/purchase/v1/year/supplierCapacityData/batch`,
    method: 'delete',
    data,
  })
}

// 年度供应商配额&日产能数据 - 失效
export const disableYearlySupplierCapacityApi = data => {
  return CustomRequest({
    url: `/purchase/v1/tenant/yearlySupplierCapacity/changeStatus`,
    method: 'post',
    data,
  })
}

// 年度供应商配额&日产能数据 - 确认
export const confirmYearlySupplierCapacityApi = data => {
  return CustomRequest({
    url: `/purchase/v1/tenant/yearlySupplierCapacity/changeStatus`,
    method: 'post',
    data,
  })
}

// 年度供应商配额&日产能数据 - 导入
export const importYearlySupplierCapacityApi = data => {
  return CustomRequest({
    url: `/purchase/v1/year/supplierCapacityData/excel/import`,
    method: 'post',
    data,
  })
}

// 年度供应商配额&日产能数据 - 导出
export const exportYearlySupplierCapacityApi = data => {
  return CustomRequest({
    url: `/purchase/v1/tenant/yearlySupplierCapacity/export`,
    method: 'post',
    data,
    responseType: 'blob',
  })
}

// 年度供应商配额&日产能数据 - 下载导入模板
export const downloadYearlySupplierCapacityTemplateApi = data => {
  return CustomRequest({
    url: `/purchase/v1/year/supplierCapacityData/downloadImportTemplate`,
    method: 'post',
    data,
    responseType: 'blob',
  })
}

export const getProductType = data => {
  return CustomRequest({
    url: `/purchase/v1/year/supplierCapacityData/lov/productType`,
    method: 'get',
    data,
  })
}

export const getSizeSegment = data => {
  return CustomRequest({
    url: `/purchase/v1/year/supplierCapacityData/lov/sizeSegment`,
    method: 'get',
    data,
  })
}

export const getYear = data => {
  return CustomRequest({
    url: `/purchase/v1/year/supplierCapacityData/lov/year`,
    method: 'get',
    data,
  })
}
