<template>
  <UiTable
    title="供应商配额&日产能维护"
    :columns="tableColumns"
    :form-columns="formColumns"
    :data="tableData"
    :tag-columns="tagColumns"
    :page="filterData.page.current"
    :size="filterData.page.size"
    :total="tableDataCount"
    :loading="loading"
    :show-confirm="false"
    :show-invalid="false"
    @search="onTableSearch"
    @tablePageChange="onTablePageChange"
    @refresh="handleRefresh"
    @delete="handleDelete"
    @import="handleImport"
    @export="handleExport"
    @downloadTemplate="handleDownloadTemplate"
  />
</template>

<script setup>
  import { ref, onMounted } from 'vue'
  import { message, Modal } from 'ant-design-vue'
  import { getSuppliers } from '@/api/weekly'
  import {
    getYearlySupplierCapacityList,
    deleteYearlySupplierCapacityApi,
    importYearlySupplierCapacityApi,
    exportYearlySupplierCapacityApi,
    downloadYearlySupplierCapacityTemplateApi,
    getYear,
    getProductType,
    getSizeSegment,
  } from './api.js'
  import UiTable from '@/views/shared-components/UiTable/UiTable.vue'
  import { getHeadersFileName, download } from '@/utils/download'

  defineOptions({
    name: 'YearlySupplierCapacity',
  })

  // 查询条件配置
  const formColumns = [
    {
      key: 'yearList',
      title: '年份',
      type: 'multipleSelect',
      options: getYear()
        .then(records => {
          return (records || []).map(item => ({
            label: item,
            value: item,
          }))
        })
        .catch(err => {
          console.error('获取年份列表失败:', err)
          return Promise.resolve([])
        }),
    },
    {
      key: 'productTypeList',
      title: '产品类型',
      type: 'multipleSelect',
      options: getProductType()
        .then(records => {
          return (records || []).map(item => ({
            label: item,
            value: item,
          }))
        })
        .catch(err => {
          console.error('获取产品类型列表失败:', err)
          return Promise.resolve([])
        }),
    },
    {
      key: 'sizeSegmentList',
      title: '尺寸分档',
      type: 'multipleSelect',
      options: getSizeSegment()
        .then(records => {
          return (records || []).map(item => ({
            label: item,
            value: item,
          }))
        })
        .catch(err => {
          console.error('获取尺寸分档列表失败:', err)
          return Promise.resolve([])
        }),
    },
    {
      key: 'supplierCodeList',
      title: '供应商',
      type: 'multipleSelect',
      options: getSuppliers()
        .then(records => {
          return records
            .map(item => ({
              label: item.supplierName,
              value: item.supplierCode,
            }))
            .filter((item, index, oriArr) => {
              const firstIndex = oriArr.findIndex(subItem => subItem.value === item.value)
              return firstIndex === index
            })
        })
        .catch(err => {
          console.error('获取供应商列表失败:', err)
          return Promise.resolve([])
        }),
    },
  ]

  // 表格列配置
  const tableColumns = [
    {
      title: '年份',
      dataIndex: 'year',
      key: 'year',
      width: 80,
      align: 'center',
    },
    {
      title: '产品类型',
      dataIndex: 'productType',
      key: 'productType',
      width: 100,
      align: 'center',
    },
    {
      title: '尺寸分档',
      dataIndex: 'sizeSegment',
      key: 'sizeSegment',
      width: 100,
      align: 'center',
    },
    {
      title: '供应商',
      dataIndex: 'supplierCode',
      key: 'supplierCode',
      width: 100,
      align: 'center',
    },
    {
      title: '比例',
      dataIndex: 'ratio',
      key: 'ratio',
      width: 80,
      align: 'center',
      customRender: ({ text }) => {
        return text ? `${text}%` : '-'
      },
    },
    {
      title: '单机用量',
      dataIndex: 'usageQty',
      key: 'usageQty',
      width: 100,
      align: 'center',
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 80,
      align: 'center',
    },
    {
      title: '型号',
      dataIndex: 'model',
      key: 'model',
      width: 100,
      align: 'center',
    },
    {
      title: '日产能',
      dataIndex: 'dailyCapacity',
      key: 'dailyCapacity',
      width: 100,
      align: 'center',
    },
    {
      title: '创建人',
      dataIndex: 'creatorName',
      key: 'creatorName',
      width: 100,
      align: 'center',
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 160,
      align: 'center',
    },
    {
      title: '更新人',
      dataIndex: 'modifier',
      key: 'modifier',
      width: 100,
      align: 'center',
    },
    {
      title: '更新时间',
      dataIndex: 'modifiedTime',
      key: 'modifiedTime',
      width: 160,
      align: 'center',
    },
  ]

  // 标签列配置（此页面无状态字段，暂时为空）
  const tagColumns = []

  // 数据状态
  const tableData = ref([])
  const tableDataCount = ref(0)
  const loading = ref(false)
  const filterData = ref({
    page: { current: 1, size: 10 },
    yearList: [],
    productTypeList: [],
    sizeSegmentList: [],
    supplierCodeList: [],
  })

  // 数据获取函数
  const fetchData = async () => {
    try {
      loading.value = true
      console.log('filterData.value', filterData.value)
      const response = await getYearlySupplierCapacityList(filterData.value)
      // 确保每条记录都有唯一的 id
      tableData.value = (response.records || []).map((item, index) => ({
        ...item,
        id: item.id || `row_${index}_${Date.now()}`, // 如果没有 id，生成一个唯一的 id
      }))
      tableDataCount.value = Number(response.total) || 0
    } catch (error) {
      console.error('获取年度供应商配额&日产能数据失败:', error)
      tableData.value = []
      tableDataCount.value = 0
    } finally {
      loading.value = false
    }
  }

  // 搜索事件
  const onTableSearch = async value => {
    filterData.value = {
      ...value,
      page: { current: 1, size: filterData.value.page.size },
    }
    await fetchData()
  }

  // 分页事件
  const onTablePageChange = async ({ current, pageSize }) => {
    Object.assign(filterData.value, { page: { current: current, size: pageSize } })
    await fetchData()
  }

  // 刷新事件
  const handleRefresh = async () => {
    await fetchData()
  }

  // 删除操作
  const handleDelete = async selectedRows => {
    console.log('selectedRows', selectedRows)
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除选中的 ${selectedRows.length} 条记录吗？`,
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          const ids = selectedRows.map(row => row.id)
          await deleteYearlySupplierCapacityApi(ids)
          message.success('删除成功')
          await fetchData()
        } catch (error) {
          console.error('删除失败:', error)
          message.error('删除失败')
        }
      },
    })
  }

  // 导入操作
  const handleImport = async file => {
    try {
      const formData = new FormData()
      formData.append('excel', file)
      await importYearlySupplierCapacityApi(formData)
      message.success('导入成功')
      await fetchData()
    } catch (error) {
      console.error('导入失败:', error)
      message.error('导入失败')
    }
  }

  // 导出操作
  const handleExport = async () => {
    try {
      const response = await exportYearlySupplierCapacityApi(filterData.value)
      const fileName = getHeadersFileName(response) || '年度供应商配额日产能数据.xlsx'
      download(response.data, fileName)
      message.success('导出成功')
    } catch (error) {
      console.error('导出失败:', error)
      message.error('导出失败')
    }
  }

  // 下载模板操作
  const handleDownloadTemplate = async () => {
    try {
      const response = await downloadYearlySupplierCapacityTemplateApi()
      const fileName = getHeadersFileName(response) || '年度供应商配额日产能导入模板.xlsx'
      download({ fileName: `${fileName}`, blob: response.data })
      message.success('模板下载成功')
    } catch (error) {
      console.error('模板下载失败:', error)
      message.error('模板下载失败')
    }
  }

  // 组件初始化
  onMounted(() => {
    fetchData()
  })
</script>
