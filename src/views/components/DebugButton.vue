<script lang="ts">
export default {
  name: "ComponentDebugButton",
};
</script>
<script lang="ts" setup>
import { ref, reactive, watch } from "vue";
import { Button, message } from "ant-design-vue";

import { format } from "sql-formatter";
import eventBus from "@/utils/eventBus2.js";
import { cloneDeep } from "lodash-es";
import { inTxinMobile } from "@/utils/index";
import { tXinMobileDownload } from "@/utils/tXinMobileDownload";

const props = defineProps({
  data: {
    type: Object,
    required: true,
  },
  query: {
    type: String,
    required: true,
  },
  inShare: {
    type: Boolean,
    default: false,
  },
});
const myFormat = (params) => {
  //接口返回params可能不符合sql格式，格式化会报错，catch返回原数据
  try {
    return format(params, {
      language: "sql",
    });
  } catch {
    return params;
  }
};
let sqlVisible = ref(false);
var mySql = null;
const copySql = () => {
  const formattedCode = myFormat(mySql);
  const textarea = document.createElement("textarea");
  textarea.value = formattedCode;
  document.body.appendChild(textarea);
  textarea.select();
  document.execCommand("copy");
  document.body.removeChild(textarea);
  message.success("复制成功");
  setTimeout(() => {
    sqlVisible.value = false;
  }, 0);
};

const debugInfoVisible = ref(false);
const showDebugModal = () => {
  if (props.inShare) return;
  debugInfoVisible.value = true;
};

const activeKey = ref(0);
let chatQueryDebugVo = ref(cloneDeep(props.data.chatQueryDebugVo));

watch(
  () => props.data,
  (v) => {
    chatQueryDebugVo = ref(cloneDeep(props.data.chatQueryDebugVo || []));
    if (chatQueryDebugVo?.value?.forEach) {
      chatQueryDebugVo?.value?.forEach((item) => (item.checked = false));
    }

    const RealSqlFixedLLM = chatQueryDebugVo.value.find(
      (i) => i.step == "RealSqlFixedLLM"
    );
    const ConvertRealSqlLLM = chatQueryDebugVo.value.find(
      (i) => i.step == "ConvertRealSqlLLM"
    );
    mySql =
      RealSqlFixedLLM?.answer ||
      ConvertRealSqlLLM?.answer ||
      props?.data?.realSql;
    if (mySql) {
      chatQueryDebugVo?.value?.push({
        step: "真实SQL",
        sql: myFormat(mySql),
      });
    }

    if (props?.data?.errorMsg) {
      chatQueryDebugVo?.value?.push({
        step: "异常信息",
        errorMsg: props.data.errorMsg,
      });
    }
    if (props?.data) {
      const jsonCode = JSON.stringify(props?.data, null, 2);
      chatQueryDebugVo?.value?.push({
        step: "响应信息",
        allData: jsonCode,
      });
    }
    if (props?.data?.realSql) {
      const idx = chatQueryDebugVo?.value?.findIndex((i) => i?.sql);
      activeKey.value = idx;
    }
  },
  { immediate: true, deep: true }
);

const formattingSql = (info) => {
  if (
    [
      "ConvertRealSqlLLM",
      "RealSqlFixedLLM",
      "MapperSemanticSqlWithLLM",
    ].includes(info.step)
  ) {
    return myFormat(info.answer);
  }
  if (info.step === "PickFieldWithLLM") {
    try {
      return JSON.stringify(JSON.parse(info.answer), null, 2);
    } catch {
      return info.answer;
    }
  }
  return info.answer;
};
const stepMap = {
  FixQuestionWithLLM: "fixQuestionPrompt",
  PickFieldWithLLM: "pickFieldPrompt",
  MapperSemanticSqlWithLLM: "semanticSqlPrompt",
  ConvertRealSqlLLM: "realSqlPrompt",
  RealSqlFixedLLM: "realSqlPromptFixed",
  ChatUnderstandWithLLM: "chatUnderstandPrompt",
  AnalysisReportDataWithLLM: "analysisReportDataPrompt",
};
const checkChangeData = () => {
  const obj = {};
  chatQueryDebugVo.value.forEach((item, index) => {
    if (item.checked) {
      obj[stepMap[item.step]] = item.prompt;
    }
  });
  console.log("修改后的数据", obj);
  return obj;
};
const debugRetry = () => {
  const data = checkChangeData();
  eventBus.emit("debugRetry", {
    data,
    quetions: props.query,
  });
  debugInfoVisible.value = false;
};
const downloadJsonAsTxt = async (jsonData, fileName = "answer.txt") => {
  const jsonString = JSON.stringify(jsonData, null, 2); // 第二个参数用于格式化 JSON
  // 使用TextEncoder将字符串编码为Uint8Array
  const encoder = new TextEncoder();
  const uint8Array = encoder.encode(jsonString);
  const buffer = new Uint8Array(uint8Array);
  const blob = new Blob([jsonString], { type: "text/plain" });
  // 移动端T信
  if (inTxinMobile()) {
    await tXinMobileDownload(blob, buffer, fileName);
    return;
  }
  const url = URL.createObjectURL(blob);
  const a = document.createElement("a");
  a.href = url;
  a.download = fileName; // 设置下载文件的名称
  document.body.appendChild(a);
  a.click();
  URL.revokeObjectURL(url);
  document.body.removeChild(a);
};
const downLoading = ref(false);
const downloadRes = () => {
  downLoading.value = true;
  try {
    downloadJsonAsTxt(props.data, "响应数据.txt");
  } finally {
    downLoading.value = false;
  }
};
</script>
<template>
  <div>
    <Button
      type="primary"
      style="height: 30px; margin-top: 20px"
      @click="showDebugModal"
      :class="{ 'no-click': inShare }"
      >DEBUG信息</Button
    >
    <a-modal
      :visible="debugInfoVisible"
      title="DEBUG信息"
      @ok="debugRetry"
      ok-text="重试"
      cancel-text="关闭"
      centered
      width="95%"
      height="95%"
      @cancel="debugInfoVisible = false"
    >
      <a-collapse v-model:activeKey="activeKey" accordion>
        <a-collapse-panel
          :key="index"
          :header="info.step"
          v-for="(info, index) in chatQueryDebugVo"
        >
          <a-checkbox v-model:checked="info.checked" v-if="info.prompt"
            >重写提示词</a-checkbox
          >
          <textarea
            v-model="info.prompt"
            style="width: 100%; min-height: 460px"
            v-if="info.prompt"
          ></textarea>
          <span v-if="info.answer">
            回答：<br />
            <highlightjs
              autodetect
              :code="formattingSql(info)"
              class="language-json"
            />
            <!-- <span>{{ info.answer }}</span> -->
          </span>
          <div v-if="info.sql">
            <highlightjs autodetect :code="info.sql" class="language-json" />
            <Button type="primary" @click="copySql">复制SQL</Button>
          </div>

          <div v-if="info.errorMsg">
            <highlightjs
              autodetect
              :code="info.errorMsg"
              class="language-json"
            />
          </div>
          <div v-if="info.allData">
            <highlightjs
              autodetect
              :code="info.allData"
              class="language-json"
            />
            <Button type="primary" @click="downloadRes" :loading="downLoading"
              >下载响应信息</Button
            >
          </div>
        </a-collapse-panel>
      </a-collapse>
    </a-modal>
  </div>
</template>

<style scoped>
.collapse-layout {
  font-size: 14px;
}
.no-click {
  cursor: not-allowed;
}
</style>
