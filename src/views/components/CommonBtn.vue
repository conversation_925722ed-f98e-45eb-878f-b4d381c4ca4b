<template>
  <span
    :class="[
      'custom-button',
      type,
      {
        hover: isHovered && !isMobile,
        disabled,
        loading,
        'mobile-custom-button': isMobile,
      },
    ]"
    @click="handleClick"
  >
    <a-spin
      v-if="loading"
      :spinning="loading"
      :indicator="indicator"
      style="margin-right: 4px"
    />
    <span>{{ label }}</span>
  </span>
</template>

<script>
import { LoadingOutlined } from "@ant-design/icons-vue";
import { defineComponent, h } from "vue";
import useWindowSize from "@/hooks/useWindowSize";
const { isMobile } = useWindowSize();

export default {
  name: "CustomButton",
  props: {
    type: {
      type: String,
      default: "primary", // primary or secondary
    },
    label: {
      type: String,
      required: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },
  emits: ["click"],
  data() {
    return {
      isHovered: false,
      indicator: h(LoadingOutlined, {
        style: {
          fontSize: "12px",
          color: "#ffffff",
        },
        spin: true,
        isMobile: isMobile.value,
      }),
    };
  },
  methods: {
    handleClick() {
      if (!this.disabled && !this.loading) {
        this.$emit("click");
      }
    },
  },
};
</script>

<style scoped lang="less">
.custom-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 82px;
  height: 26px;
  border-radius: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  user-select: none;
  font-size: 12px;
}

/* Primary button styles */
.custom-button.primary {
  color: white;
  background: linear-gradient(270deg, #be99fd 20%, #779bfc 100%);

  &:hover {
    background: linear-gradient(270deg, #a774ff 8%, #4f7dfc 99%);
  }
}
.custom-button.primary.disabled {
  background: linear-gradient(to right, #e0e0e0, #f0f0f0);
  cursor: not-allowed;
}

/* Secondary button styles */
.custom-button.secondary {
  color: #6355ff;
  background: white;
  border: 1px solid #6355ff;
  &:hover {
    color: #1500fa;
    border: 1px solid #1500fa;
  }
}
.custom-button.secondary.disabled {
  color: #b0c4de;
  border-color: #b0c4de;
  cursor: not-allowed;
}

/* Loading styles */
.custom-button.loading {
  pointer-events: none;
}
.loader {
  border: 2px solid #f3f3f3; /* Light grey */
  border-top: 2px solid #3498db; /* Blue */
  border-radius: 50%;
  width: 12px;
  height: 12px;
  animation: spin 1s linear infinite;
}
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
