<template>
  <div class="markdown-content" v-html="renderMarkdown()"></div>
</template>
<script setup>
import MarkdownIt from "markdown-it";
import MarkdownItAbbr from "markdown-it-abbr";
import MarkdownItAnchor from "markdown-it-anchor";
import MarkdownItFootnote from "markdown-it-footnote";
import MarkdownItHighlightjs from "markdown-it-highlightjs";
import MarkdownItSub from "markdown-it-sub";
import MarkdownItSup from "markdown-it-sup";
import MarkdownItTasklists from "markdown-it-task-lists";
import MarkdownItTOC from "markdown-it-toc-done-right";
import namedCodeBlocks from "markdown-it-named-code-blocks";
import "highlight.js/styles/github.css";

import { ref } from "vue";

defineOptions({
  name: "markdowndiv",
});

const props = defineProps({
  content: {
    type: String,
  },
});

const renderMarkdown = () => {
  const md = new MarkdownIt({
    html: true,
    linkify: true,
    typographer: true,
  })
    .use(MarkdownItAbbr)
    .use(MarkdownItAnchor)
    .use(MarkdownItFootnote)
    .use(MarkdownItHighlightjs)
    .use(MarkdownItSub)
    .use(MarkdownItSup)
    .use(MarkdownItTasklists)
    .use(MarkdownItTOC)
    .use(namedCodeBlocks, { isEnableInlineCss: true });
  return md.render(props.content);
};
</script>
<style scoped lang="less">
.markdown-content {
  font-size: 12px !important;
  line-height: 18px !important;
  :deep(p) {
    font-size: 12px !important;
    line-height: 18px !important;
  }
  :deep(h1) {
    font-size: 14px !important;
    line-height: 18px !important;
    color: #000;
    font-weight: 500;
  }
}
</style>