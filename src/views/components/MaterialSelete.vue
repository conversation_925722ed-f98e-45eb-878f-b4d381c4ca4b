<template>
  <a-select
    v-model:value="materialId"
    style="width: 198px; height: 24px"
    :options="materialOptions"
    show-search
    
    @change="handleMaterialChange"
    @search="fetchOption"
    :filter-option="false"
    ><template v-if="materialIdLoading" #notFoundContent> <a-spin size="small" /> </template
  ></a-select>
</template>

<script lang="ts" setup>
  import { ref, onMounted, watch, nextTick } from 'vue'
  import { debounce } from 'lodash-es'
  import { materialCode } from '@/api/weekly.js'

  const materialId = defineModel('materialId')
  const materialItem = defineModel('materialItem')

  const materialOptions = ref([]) // 物料选项
  const materialIdLoading = ref(false) // 物料编号加载中
  const fetchOption = debounce(async val => {
    materialIdLoading.value = true
    try {
      const res = await materialCode(val)

      materialOptions.value = res.map(item => ({
        label: item,
        value: item,
      }))
    } catch (error) {
      console.error('获取物料编号失败:', error)
      // 可以添加错误提示给用户
    } finally {
      materialIdLoading.value = false
    }
  }, 300)
  // handleMaterialChange
  const handleMaterialChange = (value, data) => {
    materialId.value = value
    materialItem.value = data
  }
  const init = async () => {
    try {
      const res = await materialCode()
      if (res?.length > 0) {
        materialOptions.value = res.map(item => ({
          label: item,
          value: item,
        }))
        materialItem.value = materialOptions.value[0]
        materialId.value = materialOptions.value[0].value
      }
    } catch (error) {
      console.error('获取物料编号失败:', error)
      // 可以添加错误提示给用户
    }
  }
  onMounted(() => {
    init()
  })
</script>

<style scoped lang="less"></style>
