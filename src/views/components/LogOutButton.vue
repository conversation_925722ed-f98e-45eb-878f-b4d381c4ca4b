<template>
  <div v-if="showLogout" class="menu-item-pc text-[14px]">
    <a-popconfirm title="确定退出登录吗?" @confirm="onLogout">
      <LogoutOutlined class="mr-[11px]" />退出登录
    </a-popconfirm>
  </div>
</template>

<script setup>
import { onMounted, ref } from "vue";
import { LogoutOutlined } from "@ant-design/icons-vue";
import { useRoute, useRouter } from "vue-router";
import { userStore } from "@/stores";

defineOptions({
  name: "LogOutButton",
});

const route = useRoute();
const router = useRouter();
const showLogout = ref(false);

onMounted(() => {
  const { from = "" } = route.query;
  showLogout.value = !from;
});

const onLogout = () => {
  const { clearUserInfo } = userStore();
  clearUserInfo();
  router.replace("/login");
};
</script>
