// CustomRenderer.js
import { Renderer } from "marked"
const renderer = new Renderer()

//  将链接<a></a>打开方式为_blank
renderer.link = (href, title, text) => {
  const urlRegex = /(https?:\/\/[^\s，。,!！）)(（[\]]+)/g
  // 使用正则表达式验证邮箱地址的格式
const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

  const u = href.match(urlRegex)
  const target = 'target="_blank"'
  const attributes = []
  if (!u) {
    if (href.startsWith('mailto:')) {
      const email = href.replace('mailto:', '');
      if (emailRegex.test(email)) {
        return `<a href="${href}" title="${title || ''}">${email}</a>`;
      }
    }
    return href
  }

  if (/\.(png|PNG|jpg)$/.test(href)) {
    return `<br/><img class="defalut-html-img" src="${href}" alt="${
      title || "PNG"
    }" onclick="window.handleImageClick('${href}')"><br/>`
  }

  if (u && u.length) {
    attributes.push(`href="${u[0]}"`)
  }
  if (title) {
    attributes.push(`title="${title}"`)
  }

  if (u && u.length) {
    const targeturl = u[0]
    const subIndex = href.indexOf(targeturl) + targeturl.length
    const extText = href.substring(subIndex)
    // 简单处理 如果text中包含targetUrl，则认为，直接显示url即可，否则显示text原文
    if(text.indexOf(targeturl) > -1) {
      return `<a ${attributes.join(" ")} onclick="window.handleLinkClick(event, '${targeturl}', '${title}', '${text}')">${targeturl}</a>${extText}`
    }
    return `<a ${attributes.join(" ")} onclick="window.handleLinkClick(event, '${targeturl}', '${title}', '${text}')">${text}</a>${extText}`
  }
  return `<a ${attributes.join(" ")} onclick="window.handleLinkClick(event, '${targeturl}', '${title}', '${text}')">${text}</a>`
}

renderer.image = (href, title, text) => {
  return `<img src="${href}" alt="${text}" title="${title || text}" class="defalut-html-img" onclick="window.handleImageClick('${href}')" />`
}


// 重写段落处理方法，将换行符替换成 <br> 标签
renderer.paragraph = (text) => {
  const tempText = text.replace(/\r?\n/g, '<br>')
  return `<p>${tempText.replace('↵', '<br>')}</p>`
}

renderer.code = (code, language) => {
  const ref = new Date().getTime() + Math.floor(Math.random() * 1000) + ""
  if(!language) {
    return code
  }

  return `<div class="code-container">
         <div class="code-copy-views">
         <span>${language}</span>
         <div class="copy-btn" onclick="doCodeCopy(${ref})">
         <svg style="width:20px"
         t="1609826359524"
         viewBox="0 0 1024 1024"
         version="1.1"
         p-id="2955"
         >
         <path
           d="M770.63802083 933.875H216.92708332c-44.82421875 0-79.1015625-34.27734375-79.10156249-79.1015625V195.59374999h553.7109375c44.82421875 0 79.1015625 34.27734375 79.1015625 79.10156251v659.1796875zM190.55989583 248.328125v606.4453125c0 15.8203125 10.546875 26.3671875 26.36718751 26.3671875h500.97656249V274.6953125c0-15.8203125-10.546875-26.3671875-26.3671875-26.3671875H190.55989583z"
           p-id="2956"
           fill="currentColor"
         />
         <path
           d="M612.43489583 424.98828125H296.02864583c-13.18359375 0-26.3671875-10.546875-26.3671875-26.3671875 0-13.18359375 10.546875-26.3671875 26.3671875-26.3671875h316.40625c13.18359375 0 26.3671875 10.546875 26.36718751 26.3671875 0 13.18359375-13.18359375 26.3671875-26.36718751 26.3671875z m0 131.8359375H296.02864583c-13.18359375 0-26.3671875-10.546875-26.3671875-26.3671875 0-13.18359375 10.546875-26.3671875 26.3671875-26.3671875h316.40625c13.18359375 0 26.3671875 10.546875 26.36718751 26.3671875 0 13.18359375-13.18359375 26.3671875-26.36718751 26.3671875z m0 131.8359375H296.02864583c-13.18359375 0-26.3671875-10.546875-26.3671875-26.3671875 0-13.18359375 10.546875-26.3671875 26.3671875-26.3671875h316.40625c13.18359375 0 26.3671875 10.546875 26.36718751 26.3671875 0 13.18359375-13.18359375 26.3671875-26.36718751 26.3671875z"
           p-id="2957"
           fill="currentColor"
         />
         <path
           d="M828.64583333 90.125h-527.34375001c-15.8203125 0-26.3671875 10.546875-26.36718749 26.3671875s10.546875 26.3671875 26.36718751 26.3671875h527.34374999c15.8203125 0 26.3671875 10.546875 26.3671875 26.3671875v606.4453125H823.37239583v52.73437499h84.375V169.2265625c0-44.82421875-36.9140625-79.1015625-79.1015625-79.1015625z"
           p-id="2958"
           fill="currentColor"
         />
         <path
           d="M797.00520833 802.0390625a26.3671875 26.3671875 0 1 0 52.73437501 0 26.3671875 26.3671875 0 1 0-52.73437501 0z"
           p-id="2959"
           fill="currentColor"
         />
       </svg>
       <span style="margin-left:6px;display:inline-block;">Copy Code</span>
       </div>
         </div>
         <pre><code class="hljs language-${language}" id="${ref}">${code}</code></pre>
       </div>`
}

export default renderer
