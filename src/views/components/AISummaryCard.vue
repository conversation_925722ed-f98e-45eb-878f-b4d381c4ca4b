<template>
  <div :class="`ai-summary-card ${customClass}`" :style="`${customStyle}`">
    <!-- 左上角 SVG 图标 -->
    <div class="summary-icon">
      <slot name="title"> <SvgIcon type="summary" class="icon" /></slot>
    </div>

    <!-- 右下角标识 -->
    <div class="ai-badge">数据总结由AI生成</div>

    <!-- 重新分析按钮 -->
    <div class="refresh-button">
      <a-button
        type="text"
        size="small"
        :loading="loading"
        @click="generateSummary"
        :disabled="loading || streaming"
        class="refresh-btn"
      >
        <template #icon>
          <sync-outlined :spin="loading || streaming" />
        </template>
      </a-button>
    </div>

    <!-- 内容区域 -->
    <div class="card-content">
      <div v-if="loading" class="loading-state">
        <a-spin size="small" />
        <span class="loading-text">正在连接AI服务，请稍候...</span>
      </div>

      <div v-else-if="error" class="error-state">
        <exclamation-circle-outlined class="error-icon" />
        <span class="error-text">{{ error }}</span>
        <a-button type="link" size="small" @click="generateSummary"> 重试 </a-button>
      </div>

      <div v-else-if="summary || streaming" class="summary-content" :style="`max-height: ${summaryHeight}px`" :id="id">
        <div class="summary-text" v-html="formattedSummary"></div>

        <!-- 流式输出指示器 -->
        <div v-if="streaming" class="streaming-indicator">
          <span class="streaming-dots">
            <span class="dot"></span>
            <span class="dot"></span>
            <span class="dot"></span>
          </span>
          <span class="streaming-text">AI正在思考中...</span>
        </div>
      </div>

      <!-- 请求完成但内容为空的提示 -->
      <div v-else-if="requestCompleted && !summary && !loading && !streaming" class="empty-response-state">
        <span class="empty-text">AI 暂时没有生成相关分析内容</span>
        <a-button type="link" size="small" @click="generateSummary" class="retry-btn"> 重新生成 </a-button>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, computed, onMounted, onBeforeUnmount } from 'vue'
  import { SyncOutlined, ExclamationCircleOutlined } from '@ant-design/icons-vue'
  import { message } from 'ant-design-vue'
  import { aiSummaryService } from '@/api/aiSummary'

  // 定义组件 props
  const props = defineProps({
    requestParams: {
      type: Object,
      required: true,
      default: () => ({
        inputs: {},
        query: '1',
        user: 'role',
        response_mode: 'streaming',
      }),
    },
    customClass: {
      type: String,
      default: '',
    },
    customStyle: {
      type: String,
      default: '',
    },
    summaryHeight: {
      type: [String, Number],
      default: 135,
    },
    // 用户滚动的ID
    id: {
      type: String,
      default: '',
    },
  })

  const loading = ref(false)
  const streaming = ref(false) // 新增：标识是否正在流式输出
  const summary = ref('')
  const error = ref('')
  const requestCompleted = ref(false) // 新增：标识请求是否已完成
  const abortController = ref(null) // 用于中断请求

  // 格式化摘要内容，支持换行和简单的markdown
  const formattedSummary = computed(() => {
    if (!summary.value) return ''

    return summary.value
      .replace(/\n/g, '<br>')
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
  })

  // 生成AI摘要
  const generateSummary = async () => {
    if (loading.value || streaming.value) return

    // 如果有正在进行的请求，先中断它
    if (abortController.value) {
      abortController.value.abort()
    }

    // 创建新的 AbortController
    abortController.value = new AbortController()

    loading.value = true
    streaming.value = false
    error.value = ''
    summary.value = ''
    requestCompleted.value = false // 重置请求完成状态

    try {
      // 直接使用传入的请求参数
      const requestParams = { ...props.requestParams }
      // 标记是否已接收到第一个数据块
      let firstChunkReceived = false

      // 使用AI摘要服务
      await aiSummaryService.generateSummaryWithParams(
        requestParams,
        content => {
          // 检查是否已被中断
          if (abortController.value?.signal.aborted) {
            return
          }

          // 进度回调：接收到新的内容块
          // console.log('summary.value', content)

          // 接收到第一个数据块时关闭loading，开始流式输出状态
          if (!firstChunkReceived) {
            firstChunkReceived = true
            loading.value = false
            streaming.value = true
          }

          summary.value += content
          scrollToBottom()
        },
        err => {
          // 错误回调
          throw err
        },
        () => {
          // 完成回调：流式输出结束
          console.log('AI摘要生成完成')
          streaming.value = false
          requestCompleted.value = true // 标记请求已完成
        },
        abortController.value // 传递 AbortController
      )
    } catch (err) {
      console.error('AI摘要生成失败:', err)
      error.value = '生成AI摘要时发生错误，请稍后重试'
      message.error('AI分析失败，请检查网络连接后重试')
    } finally {
      loading.value = false
      streaming.value = false
      requestCompleted.value = true // 无论成功还是失败，都标记为已完成
    }
  }
  // 滚动到底部
  const scrollToBottom = () => {
    if (props.id) {
      const element = document.getElementById(props.id)
      if (element) {
        element.scrollTop = element.scrollHeight
      }
    }
  }
  // 组件挂载时自动生成摘要
  onMounted(() => {
    generateSummary()
  })

  // 组件卸载时清理资源
  onBeforeUnmount(() => {
    // 中断正在进行的请求
    if (abortController.value) {
      abortController.value.abort()
      abortController.value = null
    }

    // 重置状态
    loading.value = false
    streaming.value = false
    requestCompleted.value = false
  })
</script>

<style scoped>
  .ai-summary-card {
    position: relative;
    background: linear-gradient(270deg, rgba(190, 153, 253, 0.16) 8%, rgba(119, 155, 252, 0.16) 100%);

    border-radius: 10px;
    padding: 12px 16px;
    /* margin-bottom: 24px; */
    min-height: 120px;
  }

  /* 左上角 SVG 图标 */
  .summary-icon {
    position: absolute;
    top: 12px;
    left: 16px;
    z-index: 2;
    font-family: REEJI-PinboGB-Flash;
    font-size: 16px;
    font-weight: normal;
    line-height: 16px;
    letter-spacing: 0.04em;

    font-variation-settings: 'opsz' auto;
    color: #333333;
    font-weight: 600;
  }

  .summary-icon .icon {
    width: 32px !important;
    height: 16px !important;
    color: #6355ff !important;
    font-size: 20px !important;
    margin-right: 0 !important;
  }

  /* 右下角标识 */
  .ai-badge {
    position: absolute;
    bottom: 12px;
    right: 16px;
    padding: 2px 8px;
    background: rgba(255, 255, 255, 0.75);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-size: 10px;
    color: #666;
    z-index: 2;
    border-radius: 8px;
  }

  /* 重新分析按钮 */
  .refresh-button {
    position: absolute;
    top: 12px;
    right: 16px;
    z-index: 2;
  }

  .refresh-btn {
    color: #6355ff !important;
    padding: 4px !important;
  }

  .refresh-btn:hover {
    background: rgba(99, 85, 255, 0.1) !important;
  }

  .card-content {
    padding-top: 40px;
    padding-bottom: 30px;
    min-height: 80px;
  }

  .loading-state {
    display: flex;
    align-items: center;
    gap: 12px;
    color: #666;
    justify-content: center;
    padding: 10px 0;
  }

  .loading-text {
    font-size: 14px;
  }

  .error-state {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #ff4d4f;
    justify-content: center;
    padding: 10px 0;
  }

  .error-icon {
    font-size: 16px;
  }

  .error-text {
    font-size: 14px;
  }

  /* 空响应状态样式 */
  .empty-response-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    color: #999;
    justify-content: center;
    padding: 20px 0;
  }

  .empty-response-state .empty-icon {
    font-size: 24px;
    margin-bottom: 4px;
  }

  .empty-response-state .empty-text {
    font-size: 14px;
    color: #666;
    text-align: center;
  }

  .empty-response-state .retry-btn {
    color: #6355ff !important;
    padding: 0 !important;
    font-size: 12px;
  }

  .empty-state {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #999;
    justify-content: center;
    padding: 20px 0;
  }

  .empty-icon {
    font-size: 16px;
  }

  .empty-text {
    font-size: 14px;
  }

  .summary-content {
    line-height: 1.6;
    overflow-y: auto;
  }

  .summary-text {
    color: #333;
    font-size: 14px;
    white-space: pre-wrap;
  }

  .summary-text :deep(strong) {
    font-weight: 600;
    color: #1890ff;
  }

  .summary-text :deep(em) {
    font-style: italic;
    color: #666;
  }

  /* 流式输出指示器 */
  .streaming-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 12px;
    padding: 8px 0;
    color: #1890ff;
    font-size: 12px;
  }

  .streaming-dots {
    display: flex;
    gap: 4px;
  }

  .dot {
    width: 4px;
    height: 4px;
    background-color: #1890ff;
    border-radius: 50%;
    animation: streaming-pulse 1.4s infinite ease-in-out;
  }

  .dot:nth-child(1) {
    animation-delay: -0.32s;
  }

  .dot:nth-child(2) {
    animation-delay: -0.16s;
  }

  .streaming-text {
    font-style: italic;
  }

  @keyframes streaming-pulse {
    0%,
    80%,
    100% {
      opacity: 0.3;
      transform: scale(0.8);
    }
    40% {
      opacity: 1;
      transform: scale(1);
    }
  }

  /* 移动端适配 */
  @media (max-width: 768px) {
    .ai-summary-card {
      padding: 10px 12px;
      min-height: 100px;
    }

    .summary-icon {
      top: 10px;
      left: 12px;
    }

    .summary-icon .icon {
      width: 18px;
      height: 18px;
    }

    .ai-badge {
      bottom: 10px;
      right: 12px;
      font-size: 11px;
      padding: 1px 6px;
    }

    .refresh-button {
      top: 10px;
      right: 12px;
    }

    .card-content {
      padding-top: 35px;
      padding-bottom: 25px;
    }
  }
</style>
