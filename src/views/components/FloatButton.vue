<template>
  <div
    class="draggable-circle"
    :style="{ top: `${top}px`, right: '24px' }"
    @mousedown="startDrag"
    @touchstart="startDrag"
    @click="handleClick"
  >
    <svg-icon type="add-file" style="font-size: 24px; margin: 4px 0 0 0" />
    <span style="color: #ffffff; font-size: 10px; font-weight: 500">人工提单</span>
  </div>
</template>

<script>
export default {
  data() {
    return {
      top: 200, // 初始位置
      isDragging: false,
      startY: 0,
      startX: 0,
      isMoved: false, // 用于区分是否是移动
    };
  },
  mounted() {
    this.top = window.innerHeight - 300;
  },
  methods: {
    startDrag(event) {
      this.isDragging = true;
      this.startY = event.clientY || event.touches[0].clientY;
      this.startX = event.clientX || event.touches[0].clientX;
      this.isMoved = false;
      document.addEventListener("mousemove", this.drag);
      document.addEventListener("mouseup", this.stopDrag);
      document.addEventListener("touchmove", this.drag);
      document.addEventListener("touchend", this.stopDrag);
    },
    drag(event) {
      if (!this.isDragging) return;

      const clientY = event.clientY || event.touches[0].clientY;
      const clientX = event.clientX || event.touches[0].clientX;
      const diffY = clientY - this.startY;
      const diffX = clientX - this.startX;

      // 如果移动距离大于5px，则认为是拖动
      if (Math.abs(diffX) > 5 || Math.abs(diffY) > 5) {
        this.isMoved = true;
      }

      const newTop = this.top + diffY;

      // 边界检测
      const minTop = 60;
      const maxTop = window.innerHeight - 120;

      if (newTop >= minTop && newTop <= maxTop) {
        this.top = newTop;
      }

      this.startY = clientY;
    },
    stopDrag() {
      this.isDragging = false;
      document.removeEventListener("mousemove", this.drag);
      document.removeEventListener("mouseup", this.stopDrag);
      document.removeEventListener("touchmove", this.drag);
      document.removeEventListener("touchend", this.stopDrag);
    },
    handleClick() {
      // 如果没有发生拖动，则触发点击事件
      if (!this.isMoved) {
        this.$Tutile.openUrl(
          "https://its.tcl.com/o/bk_itsm/weixin/#/?source=Txin",
          "人工报障"
        );
      }
    },
  },
};
</script>

<style scoped lang="less">
.draggable-circle {
  position: fixed;
  z-index: 1000;
  cursor: pointer;
  touch-action: none;
  user-select: none;
  width: 54px;
  height: 54px;
  border-radius: 27px;
  background: linear-gradient(270deg, #be99fd 8%, #779bfc 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
}
</style>
