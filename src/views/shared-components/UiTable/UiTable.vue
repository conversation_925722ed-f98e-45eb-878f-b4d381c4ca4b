<script setup>
  import { computed, ref, onMounted, toRaw } from 'vue'
  import { notification } from 'ant-design-vue'
  import { tagColorMap, TAG_STATUS } from './enum'
  import ToolBtnGroup from '@/views/shared-components/ToolBtnGroup/ToolBtnGroup.vue'
  import ImportDialog from '@/views/shared-components/ImportDialog/ImportDialog.vue'

  // 表单展开状态控制
  const isExpand = ref(false)
  const toggleExpand = () => {
    isExpand.value = !isExpand.value
  }

  const props = defineProps({
    title: {
      type: String,
      default: '',
    },
    data: {
      type: Array,
      default: () => [],
    },
    columns: {
      type: Array,
      default: () => [],
    },
    rowKey: {
      type: String,
      default: 'id',
    },
    rowClassName: {
      type: String,
    },
    tagColumns: {
      type: Array,
      default: () => [],
    },
    formColumns: {
      type: Array,
      default: () => [],
    },
    total: {
      type: Number,
      default: 0,
    },
    page: {
      type: Number,
      default: 0,
    },
    size: {
      type: Number,
      default: 0,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    showConfirm: {
      type: Boolean,
      default: true,
    },
    showInvalid: {
      type: Boolean,
      default: true,
    },
  })

  const emits = defineEmits([
    'search',
    'tablePageChange',
    'refresh',
    'delete',
    'confirm',
    'invalid',
    'import',
    'export',
    'downloadTemplate',
  ])
  const pagination = computed(() => ({
    showSizeChanger: false,
    total: props.total,
    current: props.page,
    pageSize: props.size,
    showTotal: total => `共 ${total} 条数据`,
  }))
  // 表单数据和选项映射
  const formData = ref({})
  const optionMap = ref({})

  // 初始化表单数据
  const initFormData = () => {
    props.formColumns.forEach(item => {
      formData.value[item.key] = undefined
    })
  }

  // 加载选项数据
  const loadOptions = async () => {
    for (const item of props.formColumns) {
      if (item.type === 'multipleSelect' && item.options) {
        if (item.options instanceof Promise) {
          try {
            const options = await item.options
            optionMap.value[item.key] = options
          } catch (error) {
            console.error(`Failed to load options for ${item.key}:`, error)
            optionMap.value[item.key] = []
          }
        } else {
          optionMap.value[item.key] = item.options
        }
      }
    }
  }

  const timestampToDate = timestamp => {
    // 统一转换为毫秒时间戳
    const ts = String(timestamp).length === 10 ? timestamp * 1000 : timestamp

    const date = new Date(Number(ts))

    // 获取年月日
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0') // 月份从0开始需+1
    const day = String(date.getDate()).padStart(2, '0')

    return `${year}-${month}-${day}`
  }

  const removeUndefinedShallow = obj => {
    return Object.fromEntries(Object.entries(obj).filter(([_, value]) => value !== undefined))
  }

  // 处理查询
  const handleSearch = () => {
    // 格式化输出
    const formattedData = { ...toRaw(formData.value) }
    // 处理日期范围
    props.formColumns.forEach(item => {
      if (item.type === 'dateRange' && formattedData[item.key] && formattedData[item.key].length > 0) {
        formattedData[item.key] = formattedData[item.key].map(timestampToDate)
      }
      if (typeof item.formatOutput === 'function' && formattedData[item.key]) {
        Object.assign(formattedData, { [item.key]: item.formatOutput(formattedData[item.key]) })
      }
    })

    emits('search', formattedData)
  }

  // 处理重置
  const handleReset = () => {
    initFormData()
    handleSearch()
  }

  const tagColumnKeys = computed(() => props.tagColumns.map(item => item.key))
  const getTagInfo = (columnKey, value) => {
    const currentItem = props.tagColumns.find(item => item.key === columnKey)
    if (!currentItem) {
      return { content: '' }
    }
    return currentItem.relations[String(value)] || { content: '' }
  }

  // 导入弹框控制
  const importDialogVisible = ref(false)
  const handleImport = () => {
    importDialogVisible.value = true
  }

  const handleImportConfirm = () => {
    // 导入确认已经在ImportDialog中处理
    importDialogVisible.value = false
  }

  const handleImportCancel = () => {
    importDialogVisible.value = false
  }

  const handleExport = () => {
    // 导出应该传递查询参数和分页参数
    const formattedData = { ...toRaw(formData.value) }
    // 处理日期范围
    props.formColumns.forEach(item => {
      if (item.type === 'dateRange' && formattedData[item.key] && formattedData[item.key].length > 0) {
        formattedData[item.key] = formattedData[item.key].map(timestampToDate)
      }
      if (typeof item.formatOutput === 'function' && formattedData[item.key]) {
        Object.assign(formattedData, { [item.key]: item.formatOutput(formattedData[item.key]) })
      }
    })

    // 添加分页参数
    const exportData = {
      ...formattedData,
      page: {
        current: props.page,
        size: props.size,
      },
    }

    emits('export', exportData)
  }

  const handleDownloadTemplate = () => {
    emits('downloadTemplate')
  }

  const handleDelete = () => {
    if (tableSelectValue.value.length < 1) {
      notification.error({
        message: '请选择需要操作的行',
      })
      return
    }

    emits('delete', tableSelectValue.value)
  }

  const handleConfirm = () => {
    if (tableSelectValue.value.length < 1) {
      notification.error({
        message: '请选择需要操作的行',
      })
      return
    }

    emits('confirm', tableSelectValue.value)
  }

  const handleInvalid = () => {
    if (tableSelectValue.value.length < 1) {
      notification.error({
        message: '请选择需要操作的行',
      })
      return
    }

    emits('invalid', tableSelectValue.value)
  }

  const handleRefresh = () => {
    emits('refresh')
  }

  const settingValue = ref(props.columns.map(item => item.key))
  const filterColumns = computed(() => {
    return props.columns.filter(item => settingValue.value.includes(item.key))
  })
  const onSettingChange = value => {
    settingValue.value = value
  }

  const tableSelectValue = ref([])

  const rowSelection = {
    onChange: (selectedRowKeys, selectedRows) => {
      console.log('selectedRowKeys:', selectedRowKeys)
      console.log('selectedRows:', selectedRows)
      console.log(
        'selectedRows.map(item => item.id):',
        selectedRows.map(item => item.id)
      )
      tableSelectValue.value = selectedRows
    },
    type: 'checkbox',
  }

  onMounted(() => {
    initFormData()
    loadOptions()
  })
</script>

<template>
  <!-- 表格筛选表单 -->
  <div class="filter-form-container mb-3 bg-white rounded-lg">
    <a-form layout="horizontal" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }" v-model:model="formData">
      <!-- 操作按钮区域 -->
      <div class="flex justify-between items-center mb-4">
        <div class="text-base font-medium">查询条件</div>
        <div class="flex space-x-2">
          <a-button type="primary" @click="handleSearch" :loading="loading">查询</a-button>
          <a-button @click="handleReset" :disabled="loading">重置</a-button>
          <a-button @click="toggleExpand" type="dashed" :disabled="loading">
            {{ isExpand ? '收起' : '展开' }}
          </a-button>
        </div>
      </div>

      <a-row :gutter="[16, 8]" style="width: 100%">
        <template v-for="(item, index) in formColumns" :key="item.key">
          <a-col :span="8" v-show="index < 3 || isExpand">
            <a-form-item :name="item.key">
              <template #label>
                <span class="form-label" :title="item.title">{{ item.title }}</span>
              </template>
              <template v-if="item.type === 'multipleSelect'">
                <a-select
                  size="small"
                  mode="multiple"
                  :max-tag-count="1"
                  :options="optionMap[item.key] || []"
                  placeholder="请选择"
                  v-model:value="formData[item.key]"
                />
              </template>
              <template v-else-if="item.type === 'dateRange'">
                <a-range-picker size="small" v-model:value="formData[item.key]" />
              </template>
              <template v-else>
                <a-input size="small" :placeholder="`请输入${item.title}`" v-model:value="formData[item.key]" />
              </template>
            </a-form-item>
          </a-col>
        </template>
      </a-row>
    </a-form>
  </div>

  <div class="flex justify-between mb-2">
    <div class="text-base font-medium">{{ title }}</div>
    <ToolBtnGroup
      :table-columns="props.columns"
      :loading="loading"
      :show-confirm="showConfirm"
      :show-invalid="showInvalid"
      @importClick="handleImport"
      @exportClick="handleExport"
      @downloadTemplateClick="handleDownloadTemplate"
      @deleteClick="handleDelete"
      @confirmClick="handleConfirm"
      @invalidClick="handleInvalid"
      @refreshClick="handleRefresh"
      @settingChange="onSettingChange"
    />
  </div>
  <a-table
    :columns="filterColumns"
    size="small"
    :data-source="data"
    :row-selection="rowSelection"
    :pagination="pagination"
    :showSizeChanger="false"
    :scroll="{ x: 'max-content' }"
    :loading="loading"
    row-key="id"
    @change="value => emits('tablePageChange', value)"
  >
    <template #bodyCell="{ column, record }">
      <template v-if="tagColumnKeys.includes(column.key)">
        <span
          class="rounded-lg py-0.5 px-2 text-xs"
          :style="{
            color: tagColorMap.get(getTagInfo(column.key, record[column.key]).status)?.tColor || '',
            backgroundColor: tagColorMap.get(getTagInfo(column.key, record[column.key]).status)?.bgColor || '',
          }"
        >
          {{ getTagInfo(column.key, record[column.key]).content }}
        </span>
      </template>
    </template>
  </a-table>
  <!-- 导入弹框 -->
  <ImportDialog
    v-model:visible="importDialogVisible"
    :onImport="file => emits('import', file)"
    :onDownloadTemplate="() => emits('downloadTemplate')"
    :multiple="false"
    :maxCount="1"
    @confirm="handleImportConfirm"
    @cancel="handleImportCancel"
  />
</template>

<style scoped>
  /* 筛选表单容器样式 */
  .filter-form-container {
    margin-bottom: 16px;
  }

  /* 自定义表单label样式 */
  .form-label {
    max-width: 48px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block;
    text-align: left;
  }

  /* 确保tooltip正常显示 */
  :deep(.ant-tooltip) {
    max-width: 200px;
  }
</style>
