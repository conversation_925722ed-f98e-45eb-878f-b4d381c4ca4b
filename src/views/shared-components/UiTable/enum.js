export const TAG_STATUS = Object.freeze({
    PRIMARY: 'primary',
    SUCCESS: 'success',
    ERROR: 'error',
    WARNING: 'warning',
    DEFAULT: 'default'
})

export const tagColorMap = new Map([
    [TAG_STATUS.PRIMARY, { tColor: '#333333', bgColor: '#E2F0FD' }],
    [TAG_STATUS.SUCCESS, { tColor: '#333333', bgColor: '#D6FCE3' }],
    [TAG_STATUS.ERROR, { tColor: '#333333', bgColor: '#F9DBDA' }],
    [TAG_STATUS.WARNING, { tColor: '#333333', bgColor: '#FDEFA7' }],
    [TAG_STATUS.DEFAULT, { tColor: '#333333', bgColor: '#DDDDDD' }],
])