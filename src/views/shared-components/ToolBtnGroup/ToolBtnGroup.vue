<script setup>
  import { defineProps, defineEmits, ref } from 'vue'

  // 定义props，控制按钮显隐
  const props = defineProps({
    showImport: {
      type: Boolean,
      default: true,
    },
    showExport: {
      type: Boolean,
      default: true,
    },
    showDelete: {
      type: Boolean,
      default: true,
    },
    showConfirm: {
      type: Boolean,
      default: true,
    },
    showInvalid: {
      type: Boolean,
      default: true,
    },
    showRefresh: {
      type: Boolean,
      default: true,
    },
    showSetting: {
      type: Boolean,
      default: true,
    },
    showDownloadTemplate: {
      type: Boolean,
      default: true,
    },
    tableColumns: {
      type: Array,
      default: () => [],
    },
    loading: {
      type: Boolean,
      default: false,
    },
  })

  // 定义emits，传递点击事件
  const emits = defineEmits([
    'importClick',
    'exportClick',
    'deleteClick',
    'confirmClick',
    'invalidClick',
    'refreshClick',
    'settingChange',
    'downloadTemplateClick',
  ])

  const settingDropdownVisible = ref(false)
  const theadValue = ref(props.tableColumns.map(item => item.key))
  const onCheckboxChange = value => {
    emits('settingChange', value)
  }
</script>

<template>
  <div class="tool-btn-group">
    <a-button v-if="showImport" type="primary" :disabled="loading" @click="emits('importClick')"> 导入 </a-button>
    <a-button v-if="showExport" type="primary" :disabled="loading" @click="emits('exportClick')"> 导出 </a-button>
    <!-- <a-button v-if="showDownloadTemplate" type="default" :disabled="loading" @click="emits('downloadTemplateClick')">
      下载模板
    </a-button> -->
    <a-button v-if="showDelete" type="danger" :disabled="loading" @click="emits('deleteClick')"> 删除 </a-button>
    <a-button v-if="showConfirm" type="success" :disabled="loading" @click="emits('confirmClick')"> 确认 </a-button>
    <a-button v-if="showInvalid" type="default" :disabled="loading" @click="emits('invalidClick')"> 失效 </a-button>
    <a-dropdown v-if="showSetting" v-model:visible="settingDropdownVisible" :trigger="['click']">
      <a-button type="default" :disabled="loading"> 设置 </a-button>
      <template #overlay>
        <div class="bg-white w-52 p-2">
          <a-checkbox-group v-model:value="theadValue" @change="onCheckboxChange">
            <a-row :gutter="[0, 4]">
              <a-col v-for="item in tableColumns" :key="item.key" :span="24">
                <a-checkbox :value="item.key">{{ item.title }}</a-checkbox>
              </a-col>
            </a-row>
          </a-checkbox-group>
        </div>
      </template>
    </a-dropdown>
  </div>
</template>

<style scoped>
  .tool-btn-group {
    display: flex;
    gap: 8px;
  }
</style>
