<template>
  <a-modal v-model:visible="visibleModel" title="导入数据" :maskClosable="false" width="632px">
    <div class="import-dialog-container">
      <a-upload-dragger
        v-model:fileList="fileList"
        name="file"
        :multiple="props.multiple"
        :maxCount="props.maxCount"
        :customRequest="customRequest"
        :beforeUpload="beforeUpload"
        @change="handleChange"
        @drop="handleDrop"
      >
        <div class="ant-upload-drag-icon">
          <img src="@/assets/image/import-icon.png" class="import-icon" alt="导入图标" />
          <div class="ant-upload-text">请拖拽文件或点击上传</div>
          <div class="ant-upload-hint">文件最大不可超过50M，文件格式仅支持.xls、.xlsx</div>
        </div>
      </a-upload-dragger>
      <div class="w-full mb-2 flex">
        <a-button class="p-0" @click="downloadTemplate" type="link"> 下载模板 </a-button>
      </div>

      <div class="text-gray-500 text-xs">
        <ul>
          <li v-if="!props.multiple">• 导入模式：覆盖导入，新数据将替换原有数据</li>
          <li v-else>• 导入模式：批量导入，支持同时导入多个文件</li>
          <li>• 如果导入的数据中有不合规数据，将直接自动下载一个excel文件到本地供您参考</li>
          <li>• 上传的 Excel 表符合以下规范:</li>
          <li>• 文件大小不超过50MB</li>
          <li>• 仅支持 （*.xls 和 *.xlsx）文件</li>
        </ul>
      </div>
    </div>
    <template #footer>
      <div class="flex justify-end space-x-4">
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" @click="handleConfirm" :loading="confirmLoading">保存</a-button>
      </div>
    </template>
  </a-modal>
</template>

<script setup>
  import { ref, defineProps, defineEmits } from 'vue'
  import { message } from 'ant-design-vue'

  const visibleModel = defineModel('visible', {
    type: Boolean,
    default: false,
  })
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    onImport: {
      type: Function,
      required: true,
    },
    onDownloadTemplate: {
      type: Function,
      required: true,
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    maxCount: {
      type: Number,
      default: 1,
    },
  })

  const emits = defineEmits(['update:visible', 'confirm', 'cancel'])

  const fileList = ref([])
  const confirmLoading = ref(false)
  const selectedFiles = ref([])

  // 文件上传前的验证
  const beforeUpload = file => {
    const isExcel =
      file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
      file.type === 'application/vnd.ms-excel' ||
      file.name.endsWith('.xlsx') ||
      file.name.endsWith('.xls')

    if (!isExcel) {
      message.error('只能上传 Excel 文件（.xlsx 或 .xls）')
      return false
    }

    const isLt50M = file.size / 1024 / 1024 < 50
    if (!isLt50M) {
      message.error('文件大小不能超过 50MB')
      return false
    }

    // 根据是否支持多文件来处理
    if (props.multiple) {
      if (selectedFiles.value.length >= props.maxCount) {
        message.error(`最多只能上传 ${props.maxCount} 个文件`)
        return false
      }
      selectedFiles.value.push(file)
    } else {
      selectedFiles.value = [file]
    }

    // 显示上传成功的toast提示
    message.success('上传成功，请点击保存导入文件中的数据')

    return false // 阻止自动上传，我们手动处理
  }

  // 自定义上传请求
  const customRequest = ({ onSuccess }) => {
    // 文件已在beforeUpload中处理，这里只需要标记成功
    onSuccess()
  }

  // 下载模板
  const downloadTemplate = async () => {
    try {
      await props.onDownloadTemplate()
    } catch (error) {
      console.error('模板下载失败:', error)
      message.error('模板下载失败')
    }
  }

  // 取消
  const handleCancel = () => {
    emits('update:visible', false)
    emits('cancel')
    // 重置选择的文件
    selectedFiles.value = []
    fileList.value = []
  }

  // 确认导入
  const handleConfirm = async () => {
    if (selectedFiles.value.length === 0) {
      message.error('请先选择要导入的文件')
      return
    }

    confirmLoading.value = true
    try {
      // 根据是否支持多文件来传递不同的参数
      const filesToImport = props.multiple ? selectedFiles.value : selectedFiles.value[0]
      await props.onImport(filesToImport)
      emits('update:visible', false)
      emits('confirm', filesToImport)
      // 重置选择的文件
      selectedFiles.value = []
      fileList.value = []
      message.success('导入成功')
    } catch (error) {
      console.error('导入失败:', error)
      message.error('导入失败')
    } finally {
      confirmLoading.value = false
    }
  }

  const handleChange = info => {
    const { status } = info.file
    if (status === 'done') {
      message.success(`${info.file.name} 文件选择成功`)
    } else if (status === 'error') {
      message.error(`${info.file.name} 文件选择失败`)
    }
  }

  const handleDrop = e => {
    console.log('Dropped files', e.dataTransfer.files)
  }
</script>

<style scoped lang="less">
  .import-dialog-container {
    width: 100%;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
  }

  .import-dialog-container .ant-upload-dragger {
    width: 100% !important;
    margin-bottom: 16px;
    box-sizing: border-box;
  }
  .ant-upload-drag-icon {
    .import-icon {
      width: 96px;
      height: 96px;
    }
  }
</style>
