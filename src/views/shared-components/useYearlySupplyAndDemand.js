import { ref, computed, watch, nextTick } from 'vue'
import { reportForm } from '@/api/weekly.js'

export const useYearlySupplyAndDemand = materialId => {
  const loading = ref(false)
  // 需要合并的行类型
  const colSpanList = ['合计累缺', '合计供应', '合计需求']

  // 表格列定义
  const columns = ref([
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      width: 50,
      fixed: 'left',
      align: 'center',
    },
    {
      title: '物料编号',
      dataIndex: 'materialCode',
      key: 'materialCode',
      width: 100,
      fixed: 'left',
      align: 'center',
      customCell: (row, index) => {
        const span = mergeInfo.value.materialCode?.[index]?.rowSpan || 0
        return {
          rowSpan: span,
          colSpan: colSpanList.includes(row?.type) ? 4 : 1,
        }
      },
    },
    {
      title: '供应商',
      dataIndex: 'supplierName',
      key: 'supplierName',
      width: 80,
      fixed: 'left',
      align: 'center',
      customCell: (row, index) => {
        const span = mergeInfo.value.supplierName?.[index]?.rowSpan || 0
        return {
          rowSpan: span,
          colSpan: colSpanList.includes(row?.type) ? 0 : 1,
        }
      },
    },
    {
      title: '线体',
      dataIndex: 'productLineType',
      key: 'productLineType',
      width: 100,
      fixed: 'left',
      align: 'center',
      customCell: (row, index) => {
        const span = mergeInfo.value.productLineType?.[index]?.rowSpan || 0
        return {
          rowSpan: span,
          colSpan: colSpanList.includes(row?.type) ? 0 : 1,
        }
      },
    },
    {
      title: '',
      dataIndex: 'type',
      key: 'type',
      width: 70,
      fixed: 'left',
      align: 'center',
      customCell: (row, index) => {
        return {
          colSpan: colSpanList.includes(row?.type) ? 0 : 1,
        }
      },
    },
  ])

  // 表格数据源
  const dataSource = ref([])

  // 计算行合并信息
  const mergeInfo = computed(() => {
    const data = dataSource.value || []
    const mergeMap = {}
    // 需要合并的字段
    const mergeFields = ['supplierName', 'materialCode', 'productLineType']

    mergeFields.forEach(field => {
      mergeMap[field] = []

      data.forEach((item, index) => {
        // 需要合并行时特殊处理
        if (colSpanList.includes(item?.type)) {
          mergeMap[field].push({ rowSpan: 1 })
        } else if (index === 0) {
          // 第一行总是显示
          mergeMap[field].push({ rowSpan: 1 })
        } else {
          const prevItem = data[index - 1]
          if (item[field] === prevItem[field]) {
            // productLineType合并  需要 productLineType + supplierName
            if (field === 'productLineType') {
              // 腺体合并
              if (
                `${item[field]}${item.supplierName}` !==
                `${prevItem[field]}${prevItem.supplierName}`
              ) {
                mergeMap[field].push({ rowSpan: 1 })
                return mergeMap
              }
            }
            // 与上一行相同，不显示（rowSpan = 0）
            mergeMap[field].push({ rowSpan: 0 })
            // 增加上一个显示行的 rowSpan
            let displayIndex = index - 1
            while (displayIndex >= 0 && mergeMap[field][displayIndex].rowSpan === 0) {
              displayIndex--
            }
            if (displayIndex >= 0) {
              mergeMap[field][displayIndex].rowSpan++
            }
          } else {
            // 与上一行不同，显示新值
            mergeMap[field].push({ rowSpan: 1 })
          }
        }
      })
    })

    return mergeMap
  })

  // 根据数据值返回对应的样式类
  const getTypeClass = text => {
    if (text === '供应') {
      return 'bg-[#ebf8ff]'
    } else if (text === '需求') {
      return 'bg-[#e4e9ff]'
    } else if (text === '累缺') {
      return 'bg-[#fff5da]'
    } else {
      return ''
    }
  }

  // 行样式
  const getRowClassName = record => {
    return colSpanList.includes(record?.type) ? 'd8edff' : ''
  }

  // 添加动态列
  const addDynamicColumns = () => {
    // 检查数据源是否为空
    if (!dataSource.value || dataSource.value.length === 0) return

    // 获取第一条数据的所有字段
    const firstItem = dataSource.value[0]?.data || {}

    // 创建动态列
    const dynamicColumns = Object.keys(firstItem).map(key => ({
      title: key,
      dataIndex: key,
      key: key,
      width: 80,
      align: 'center',
    }))

    // 保留固定列并合并动态列
    const fixedFieldKeys = ['index', 'materialCode', 'supplierName', 'type', 'productLineType']
    columns.value = [
      ...columns.value.filter(col => fixedFieldKeys.includes(col.key)),
      ...dynamicColumns,
    ]
  }

  const isTotal = ref(true)

  const init = async id => {
    if (!id) return

    try {
      loading.value = true
      const res = await reportForm(id)
      dataSource.value = (res || []).map((item, index) => ({
        ...item,
        index: index + 1,
        ...item.data,
      }))

      addDynamicColumns()

      // 触发重新计算
      isTotal.value = false
      await nextTick()
      isTotal.value = true
    } catch (error) {
      console.error('获取年度供需数据失败:', error)
      dataSource.value = []
    } finally {
      loading.value = false
    }
  }

  // 监听物料ID变化
  watch(
    materialId,
    val => {
      if (!val) return
      init(val)
    },
    {
      immediate: true,
    }
  )

  return {
    loading,
    columns,
    dataSource,
    mergeInfo,
    getTypeClass,
    getRowClassName,
    isTotal,
    colSpanList,
  }
}
