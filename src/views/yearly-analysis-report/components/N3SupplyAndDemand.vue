<template>
  <div class="p-5 bg-white rounded-lg shadow-md">
    <a-table
      :columns="columns"
      :data-source="dataSource"
      :pagination="false"
      :bordered="true"
      size="small"
      class="rounded-md overflow-hidden"
      :scroll="{ x: 'max-content' }"
    >
    </a-table>
  </div>
</template>

<script setup>
  import { ref, computed } from 'vue'

  // 检测是否为移动设备
  const isMobile = computed(() => {
    if (typeof window !== 'undefined') {
      return window.innerWidth <= 768
    }
    return false
  })

  // 响应式滚动配置
  const scrollConfig = computed(() => {
    const baseConfig = {
      x: 'max-content',
      scrollToFirstRowOnChange: false,
    }

    // 移动端优化配置
    if (isMobile.value) {
      return {
        ...baseConfig,
        // 移动端可以设置固定高度避免页面过长
        // y: 400, // 可选：限制表格高度
      }
    }

    return { y: 220, x: 200 }
  })

  // 表格列定义
  const columns = ref([
    {
      title: '供应商',
      dataIndex: 'supplier',
      key: 'supplier',
      width: 80,
      fixed: 'left',
      align: 'center',
      customCell: record => {
        return {
          rowSpan: record.supplierRowSpan || 0,
        }
      },
    },
    {
      title: '行标签',
      dataIndex: 'rowLabel',
      key: 'rowLabel',
      width: 80,
      fixed: 'left',
      align: 'center',
    },
    {
      title: '线体数',
      dataIndex: 'lineCount',
      key: 'lineCount',
      width: 60,
      fixed: 'left',
      align: 'center',
    },
    {
      title: '1周',
      dataIndex: 'week1',
      key: 'week1',
      width: 60,
      align: 'center',
    },
    {
      title: '2周',
      dataIndex: 'week2',
      key: 'week2',
      width: 60,
      align: 'center',
    },
    {
      title: '3周',
      dataIndex: 'week3',
      key: 'week3',
      width: 60,
      align: 'center',
    },
    {
      title: '4周',
      dataIndex: 'week4',
      key: 'week4',
      width: 60,
      align: 'center',
    },
    {
      title: '5周',
      dataIndex: 'week5',
      key: 'week5',
      width: 60,
      align: 'center',
    },
    {
      title: '6周',
      dataIndex: 'week6',
      key: 'week6',
      width: 60,
      align: 'center',
    },
    {
      title: '7周',
      dataIndex: 'week7',
      key: 'week7',
      width: 60,
      align: 'center',
    },
    {
      title: '8周',
      dataIndex: 'week8',
      key: 'week8',
      width: 60,
      align: 'center',
    },
    {
      title: '9周',
      dataIndex: 'week9',
      key: 'week9',
      width: 60,
      align: 'center',
    },
    {
      title: '10周',
      dataIndex: 'week10',
      key: 'week10',
      width: 60,
      align: 'center',
    },
    {
      title: '11周',
      dataIndex: 'week11',
      key: 'week11',
      width: 60,
      align: 'center',
    },
    {
      title: '12周',
      dataIndex: 'week12',
      key: 'week12',
      width: 60,
      align: 'center',
    },
    {
      title: '13周',
      dataIndex: 'week13',
      key: 'week13',
      width: 60,
      align: 'center',
    },
  ])

  // 表格数据源
  const dataSource = ref([
    // AAA 供应商
    {
      key: '1',
      supplier: 'AAA',
      supplierRowSpan: 3, // AAA供应商占3行
      rowLabel: '1200线',
      lineCount: 3,
      week1: 20,
      week2: 23,
      week3: 25,
      week4: 7,
      week5: 6,
      week6: 20,
      week7: 19,
      week8: 21,
      week9: 24,
      week10: 26,
      week11: 28,
      week12: 30,
      week13: 32,
    },
    {
      key: '2',
      supplier: 'AAA',
      supplierRowSpan: 0, // 合并到上一行
      rowLabel: '600线',
      lineCount: 1,
      week1: 5,
      week2: 5,
      week3: 5,
      week4: 5,
      week5: 7,
      week6: 7,
      week7: 8,
      week8: 13,
      week9: 22,
      week10: 18,
      week11: 15,
      week12: 20,
      week13: 25,
    },
    {
      key: '3',
      supplier: 'AAA',
      supplierRowSpan: 0, // 合并到上一行
      rowLabel: '750线',
      lineCount: 3,
      week1: 5,
      week2: 5,
      week3: 5,
      week4: 8,
      week5: 16,
      week6: 16,
      week7: 18,
      week8: 25,
      week9: 25,
      week10: 22,
      week11: 24,
      week12: 27,
      week13: 29,
    },
    // BBB 供应商
    {
      key: '4',
      supplier: 'BBB',
      supplierRowSpan: 3, // BBB供应商占3行
      rowLabel: '1200线',
      lineCount: 5,
      week1: 5,
      week2: 5,
      week3: 5,
      week4: 28,
      week5: 6,
      week6: 20,
      week7: 19,
      week8: 21,
      week9: 24,
      week10: 26,
      week11: 28,
      week12: 30,
      week13: 32,
    },
    {
      key: '5',
      supplier: 'BBB',
      supplierRowSpan: 0, // 合并到上一行
      rowLabel: '600线',
      lineCount: 4,
      week1: 5,
      week2: 5,
      week3: 5,
      week4: 1,
      week5: 7,
      week6: 5,
      week7: 5,
      week8: 5,
      week9: 5,
      week10: 8,
      week11: 10,
      week12: 12,
      week13: 15,
    },
    {
      key: '6',
      supplier: 'BBB',
      supplierRowSpan: 0, // 合并到上一行
      rowLabel: '750线',
      lineCount: 2,
      week1: 5,
      week2: 5,
      week3: 5,
      week4: 8,
      week5: 16,
      week6: 5,
      week7: 5,
      week8: 5,
      week9: 5,
      week10: 8,
      week11: 10,
      week12: 12,
      week13: 15,
    },
    // CCC 供应商
    {
      key: '7',
      supplier: 'CCC',
      supplierRowSpan: 3, // CCC供应商占3行
      rowLabel: '1200线',
      lineCount: 2,
      week1: 20,
      week2: 23,
      week3: 7,
      week4: 7,
      week5: 6,
      week6: 20,
      week7: 19,
      week8: 21,
      week9: 24,
      week10: 26,
      week11: 28,
      week12: 30,
      week13: 32,
    },
    {
      key: '8',
      supplier: 'CCC',
      supplierRowSpan: 0, // 合并到上一行
      rowLabel: '600线',
      lineCount: 2,
      week1: 5,
      week2: 5,
      week3: 5,
      week4: 5,
      week5: 7,
      week6: 7,
      week7: 8,
      week8: 13,
      week9: 22,
      week10: 18,
      week11: 15,
      week12: 20,
      week13: 25,
    },
    {
      key: '9',
      supplier: 'CCC',
      supplierRowSpan: 0, // 合并到上一行
      rowLabel: '750线',
      lineCount: 1,
      week1: 5,
      week2: 5,
      week3: 5,
      week4: 8,
      week5: 16,
      week6: 16,
      week7: 18,
      week8: 21,
      week9: 21,
      week10: 22,
      week11: 24,
      week12: 27,
      week13: 29,
    },
  ])
</script>
