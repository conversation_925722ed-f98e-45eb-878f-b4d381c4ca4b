<template>
  <div class="w-full h-full">
    <div class="w-full space-y-6">
      <!-- <AISummaryCard :request-params="weeklyAIParams" /> -->
      <N3SupplyAndDemand />
    </div>
  </div>
</template>

<script>
  import N3SupplyAndDemand from './components/N3SupplyAndDemand.vue'
  import AISummaryCard from '@/views/components/AISummaryCard.vue'

  export default {
    name: 'YearlyAnalysisReport',
    components: {
      N3SupplyAndDemand,
      AISummaryCard,
    },
  }
</script>
