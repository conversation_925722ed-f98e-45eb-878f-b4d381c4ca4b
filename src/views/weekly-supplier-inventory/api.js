import { CustomRequest } from '@/utils/CustomRequest'

// 供应商库存数据 - 分页查询
export const pagSupplierInventoryApi = data => {
  return CustomRequest({
    url: `/purchase/v1/tenant/supplierInventory/pageQuery`,
    method: 'post',
    data,
  })
}

// 供应商库存数据 - 保存
export const savSupplierInventoryApi = data => {
  return CustomRequest({
    url: `/purchase/v1/tenant/supplierInventory/save`,
    method: 'post',
    data,
  })
}

// 供应商库存数据 - 删除
export const deletSupplierInventoryApi = data => {
  return CustomRequest({
    url: `/purchase/v1/tenant/supplierInventory/del`,
    method: 'post',
    data,
  })
}

// 供应商库存数据 - 启用
export const enablSupplierInventoryApi = data => {
  return CustomRequest({
    url: `/purchase/v1/tenant/supplierInventory/changeStatus`,
    method: 'post',
    data,
  })
}

// 供应商库存数据 - 停用
export const disablSupplierInventoryApi = data => {
  return CustomRequest({
    url: `/purchase/v1/tenant/supplierInventory/changeStatus`,
    method: 'post',
    data,
  })
}

// 供应商库存数据 - 确认
export const confirmSupplierLineMaterialsupplierInventoryApi = data => {
  return CustomRequest({
    url: `/purchase/v1/tenant/supplierInventory/changeStatus`,
    method: 'post',
    data,
  })
}

// 供应商库存数据 - 导入
export const importSupplierLineMaterialsupplierInventoryApi = data => {
  return CustomRequest({
    url: `/purchase/v1/tenant/supplierInventory/excel/import`,
    method: 'post',
    data,
    responseType: 'blob',
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
}

// 供应商库存数据 - 导入模板下载
export const importDownloadSupplierLineMaterialsupplierInventoryApi = data => {
  return CustomRequest({
    url: `/purchase/v1/tenant/supplierInventory/downloadImportTemplate`,
    method: 'post',
    data,
    responseType: 'blob',
  })
}

// 供应商库存数据 - 导出
export const exportSupplierLineMaterialsupplierInventoryApi = data => {
  return CustomRequest({
    url: `/purchase/v1/tenant/supplierInventory/excel/exportData`,
    method: 'post',
    data,
    responseType: 'blob',
  })
}
