<template>
  <UiTable
    title="供应商配额"
    :columns="tableColumns"
    :form-columns="formColumns"
    :data="tableData"
    :tag-columns="tagColumns"
    :page="filterData.page.current"
    :size="filterData.page.size"
    :total="tableDataCount"
    :loading="loading"
    @search="onTableSearch"
    @tablePageChange="onTablePageChange"
    @refresh="handleRefresh"
    @delete="handleDelete"
    @confirm="handleConfirm"
    @invalid="handleInvalid"
    @import="handleImport"
    @export="handleExport"
    @downloadTemplate="handleDownloadTemplate"
  />
</template>

<script setup>
  defineOptions({
    name: 'WeeklySupplierQuota',
  })

  import { ref, onMounted } from 'vue'
  import { message, Modal } from 'ant-design-vue'
  import { getCompanys, getSuppliers } from '@/api/weekly'
  import {
    pageSupplierQuotaDataQuotaDataApi,
    deleteSupplierQuotaDataQuotaDataApi,
    disableSupplierQuotaDataQuotaDataApi,
    confirmSupplierQuotaDataQuotaDataApi,
    importSupplierQuotaDataQuotaDataApi,
    exportSupplierQuotaDataQuotaDataApi,
    importDownloadSupplierQuotaDataQuotaDataApi,
  } from './api.js'
  import UiTable from '@/views/shared-components/UiTable/UiTable.vue'
  import { TAG_STATUS } from '@/views/shared-components/UiTable/enum.js'
  import { STATUS } from './enum.js'
  import { getHeadersFileName, download } from '@/utils/download'

  const formColumns = [
    {
      key: 'companyCodeList',
      title: '公司',
      type: 'multipleSelect',
      options: getCompanys()
        .then(records => {
          const list = records.map((item, index) => ({
            label: item.companyName,
            value: item.companyCode || index,
          }))
          console.log('listlistlist', list)
          return list
        })
        .catch(err => {
          console.error('获取公司列表失败:', err)
          return Promise.resolve([])
        }),
    },
    {
      key: 'statusList',
      title: '状态',
      type: 'multipleSelect',
      options: [
        { label: '草稿', value: STATUS.DRAFT },
        { label: '已确认', value: STATUS.CONFIRMED },
        { label: '已失效', value: STATUS.INVALID },
      ],
    },
    {
      key: 'supplierCodeList',
      title: '供应商',
      type: 'multipleSelect',
      options: getSuppliers()
        .then(records => {
          return records
            .map(item => ({
              label: item.supplierName,
              value: item.supplierCode,
            }))
            .filter((item, index, oriArr) => {
              const firstIndex = oriArr.findIndex(subItem => subItem.value === item.value)
              return firstIndex === index
            })
        })
        .catch(err => {
          console.error('获取供应商列表失败:', err)
          return Promise.resolve([])
        }),
    },
    {
      key: 'dataGroup',
      title: '通代分组',
    },
    {
      key: 'itemCode',
      title: '物料编码',
    },
    {
      key: 'itemName',
      title: '物料名称',
    },
    {
      key: 'categoryCode',
      title: '外部物料组编码',
    },
    {
      key: 'categoryName',
      title: '外部物料组名称',
    },
    {
      key: 'model',
      title: '机型',
    },
    {
      key: 'startDate',
      title: '生效日期',
      type: 'dateRange',
      formatOutput: value => {
        return {
          startDateS: value[0],
          startDateE: value[1],
        }
      },
    },
    {
      key: 'endDate',
      title: '失效日期',
      type: 'dateRange',
      formatOutput: value => {
        return {
          endDateS: value[0],
          endDateE: value[1],
        }
      },
    },
    {
      key: 'createUserName',
      title: '创建人',
    },
    {
      key: 'createTime',
      title: '创建时间',
      type: 'dateRange',
      formatOutput: value => {
        return {
          createStartTime: value[0] + ' 00:00:00',
          createEndTime: value[1] + ' 23:59:59',
        }
      },
    },
    {
      key: 'updateUserName',
      title: '最后更新人',
    },
    {
      key: 'updateTime',
      title: '最后更新时间',
      type: 'dateRange',
      formatOutput: value => {
        return {
          updateStartTime: value[0] + '00:00:00',
          updateEndTime: value[1] + '23:59:59',
        }
      },
    },
  ]
  const tableColumns = [
    {
      key: 'companyCode',
      dataIndex: 'companyCode',
      title: '公司代码',
      width: 120,
    },
    {
      key: 'companyName',
      dataIndex: 'companyName',
      title: '公司名称',
      width: 160,
      ellipsis: true,
    },
    {
      key: 'supplierCode',
      dataIndex: 'supplierCode',
      title: '供应商编码',
      width: 120,
    },
    {
      key: 'supplierName',
      dataIndex: 'supplierName',
      title: '供应商名称',
      width: 160,
      ellipsis: true,
    },
    {
      key: 'status',
      dataIndex: 'status',
      title: '状态',
      width: 100,
    },
    {
      key: 'dataGroup',
      dataIndex: 'dataGroup',
      title: '通代分组',
      width: 120,
    },
    {
      key: 'itemCode',
      dataIndex: 'itemCode',
      title: '物料编码',
      width: 120,
    },
    {
      key: 'itemName',
      dataIndex: 'itemName',
      title: '物料名称',
      width: 160,
      ellipsis: true,
    },
    {
      key: 'categoryCode',
      dataIndex: 'categoryCode',
      title: '外部物料组编码',
      width: 160,
    },
    {
      key: 'categoryName',
      dataIndex: 'categoryName',
      title: '外部物料组名称',
      width: 160,
      ellipsis: true,
    },
    {
      key: 'model',
      dataIndex: 'model',
      title: '机型',
      width: 120,
    },
    {
      key: 'ratio',
      dataIndex: 'ratio',
      title: '配额比例(%)',
      width: 120,
    },
    {
      key: 'startDate',
      dataIndex: 'startDate',
      title: '生效日期',
      width: 120,
    },
    {
      key: 'endDate',
      dataIndex: 'endDate',
      title: '失效日期',
      width: 120,
    },
    {
      key: 'remark',
      dataIndex: 'remark',
      title: '备注',
      width: 120,
      ellipsis: true,
    },
    {
      key: 'createUserName',
      dataIndex: 'createUserName',
      title: '创建人',
      width: 120,
    },
    {
      key: 'createTime',
      dataIndex: 'createTime',
      title: '创建时间',
      width: 160,
    },
    {
      key: 'updateUserName',
      dataIndex: 'updateUserName',
      title: '最后更新人',
      width: 120,
    },
    {
      key: 'updateTime',
      dataIndex: 'updateTime',
      title: '最后更新时间',
      width: 160,
    },
  ]

  const tagColumns = [
    {
      key: 'status',
      relations: {
        1: {
          content: '草稿',
          status: TAG_STATUS.WARNING,
        },
        4: {
          content: '采方已确认',
          status: TAG_STATUS.PRIMARY,
        },
        5: {
          content: '失效',
          status: TAG_STATUS.DEFAULT,
        },
      },
    },
  ]

  const tableData = ref([])
  const tableDataCount = ref(0)
  const loading = ref(false)
  const filterData = ref({
    page: {
      current: 1,
      size: 10,
    },
  })

  // 提取数据获取逻辑为单独函数
  const fetchData = async () => {
    try {
      loading.value = true
      const response = await pageSupplierQuotaDataQuotaDataApi(filterData.value)
      // 确保每条记录都有唯一的 id
      tableData.value = (response.records || []).map((item, index) => ({
        ...item,
        id: item.id || `row_${index}_${Date.now()}`, // 如果没有 id，生成一个唯一的 id
      }))
      tableDataCount.value = Number(response.total) || 0
    } catch (error) {
      console.error('获取供应商配额数据失败:', error)
      tableData.value = []
      tableDataCount.value = 0
    } finally {
      loading.value = false
    }
  }
  const onTableSearch = async value => {
    filterData.value = {
      ...value,
      page: { current: 1, size: filterData.value.page.size },
    }
    await fetchData()
  }
  const onTablePageChange = async ({ current, pageSize }) => {
    Object.assign(filterData.value, { page: { current: current, size: pageSize } })
    await fetchData()
  }

  const handleRefresh = async () => {
    await fetchData()
  }

  // 删除操作
  const handleDelete = async selectedRows => {
    console.log('selectedRows', selectedRows)
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除选中的 ${selectedRows.length} 条记录吗？`,
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          const ids = selectedRows.map(row => row.id)
          await deleteSupplierQuotaDataQuotaDataApi(ids)
          message.success('删除成功')
          await fetchData()
        } catch (error) {
          console.error('删除失败:', error)
          message.error('删除失败')
        }
      },
    })
  }

  // 确认操作
  const handleConfirm = async selectedRows => {
    // 检查选中行的状态，只有草稿才能确认
    const selectedStatuses = selectedRows.map(row => row.status)
    const hasNonDraftStatus = selectedStatuses.some(status => status !== STATUS.DRAFT)

    if (hasNonDraftStatus) {
      message.warning('只有草稿状态的数据才能确认')
      return
    }

    Modal.confirm({
      title: '确认操作',
      content: `确定要确认选中的 ${selectedRows.length} 条记录吗？`,
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          const ids = selectedRows.map(row => row.id)
          await confirmSupplierQuotaDataQuotaDataApi({
            idList: ids,
            status: STATUS.CONFIRMED,
          })
          message.success('确认成功')
          await fetchData()
        } catch (error) {
          console.error('确认失败:', error)
          message.error('确认失败')
        }
      },
    })
  }

  // 失效操作
  const handleInvalid = async selectedRows => {
    // 检查选中行的状态，只有已确认才能失效
    const selectedStatuses = selectedRows.map(row => row.status)
    const hasNonConfirmedStatus = selectedStatuses.some(status => status !== STATUS.CONFIRMED)

    if (hasNonConfirmedStatus) {
      message.warning('只有已确认状态的数据才能失效')
      return
    }

    Modal.confirm({
      title: '失效操作',
      content: `确定要将选中的 ${selectedRows.length} 条记录设为失效吗？`,
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          const ids = selectedRows.map(row => row.id)
          await disableSupplierQuotaDataQuotaDataApi({
            idList: ids,
            status: STATUS.INVALID,
          })
          message.success('失效成功')
          await fetchData()
        } catch (error) {
          console.error('失效失败:', error)
          message.error('失效失败')
        }
      },
    })
  }

  // 导入操作
  const handleImport = async file => {
    try {
      const formData = new FormData()
      formData.append('excel', file)

      const response = await importSupplierQuotaDataQuotaDataApi(formData)

      // 检查是否有错误文件返回
      if (response && response.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
        // 如果返回的是Excel文件，说明有错误数据，自动下载
        const fileName = getHeadersFileName(response)
        download({ fileName: `${fileName}`, blob: response.data })
        message.warning('导入完成，但存在错误数据，已自动下载错误文件供参考')
      }
      await fetchData()
    } catch (error) {
      console.error('导入失败:', error)
      // message.error('导入失败')
    }
  }

  // 导出操作
  const handleExport = async queryParams => {
    try {
      const response = await exportSupplierQuotaDataQuotaDataApi(queryParams)
      const fileName = getHeadersFileName(response)
      download({ fileName: `${fileName}`, blob: response.data })
      message.success('导出成功')
    } catch (error) {
      console.error('导出失败:', error)
      message.error('导出失败')
    }
  }

  // 下载模板
  const handleDownloadTemplate = async () => {
    try {
      const response = await importDownloadSupplierQuotaDataQuotaDataApi({})
      const fileName = getHeadersFileName(response)
      download({ fileName: `${fileName}`, blob: response.data })
      message.success('模板下载成功')
    } catch (error) {
      console.error('模板下载失败:', error)
      message.error('模板下载失败')
    }
  }

  onMounted(() => {
    fetchData()
  })
</script>

<style scoped></style>
