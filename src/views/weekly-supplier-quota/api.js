import { CustomRequest } from '@/utils/CustomRequest'

// 供应商配额数据 - 分页查询
export const pageSupplierQuotaDataQuotaDataApi = data => {
  return CustomRequest({
    url: `/purchase/v1/tenant/supplierQuotaData/pageQuery`,
    method: 'post',
    data,
  })
}

// 供应商配额数据 - 保存
export const saveSupplierQuotaDataQuotaDataApi = data => {
  return CustomRequest({
    url: `/purchase/v1/tenant/supplierQuotaData/save`,
    method: 'post',
    data,
  })
}

// 供应商配额数据 - 删除
export const deleteSupplierQuotaDataQuotaDataApi = data => {
  return CustomRequest({
    url: `/purchase/v1/tenant/supplierQuotaData/del`,
    method: 'post',
    data,
  })
}

// 供应商配额数据 - 启用
export const enableSupplierQuotaDataQuotaDataApi = data => {
  return CustomRequest({
    url: `/purchase/v1/tenant/supplierQuotaData/changeStatus`,
    method: 'post',
    data,
  })
}

// 供应商配额数据 - 停用
export const disableSupplierQuotaDataQuotaDataApi = data => {
  return CustomRequest({
    url: `/purchase/v1/tenant/supplierQuotaData/changeStatus`,
    method: 'post',
    data,
  })
}

// 供应商配额数据 - 确认
export const confirmSupplierQuotaDataQuotaDataApi = data => {
  return CustomRequest({
    url: `/purchase/v1/tenant/supplierQuotaData/changeStatus`,
    method: 'post',
    data,
  })
}

// 供应商配额数据 - 导入
export const importSupplierQuotaDataQuotaDataApi = data => {
  return CustomRequest({
    url: `/purchase/v1/tenant/supplierQuotaData/excel/import`,
    method: 'post',
    data,
    responseType: 'blob',
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
}

// 供应商配额数据 - 导入模板下载
export const importDownloadSupplierQuotaDataQuotaDataApi = data => {
  return CustomRequest({
    url: `/purchase/v1/tenant/supplierQuotaData/downloadImportTemplate`,
    method: 'post',
    data,
    responseType: 'blob',
  })
}

// 供应商配额数据 - 导出
export const exportSupplierQuotaDataQuotaDataApi = data => {
  return CustomRequest({
    url: `/purchase/v1/tenant/supplierQuotaData/excel/exportData`,
    method: 'post',
    data,
    responseType: 'blob',
  })
}
