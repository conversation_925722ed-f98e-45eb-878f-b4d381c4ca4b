import http, { post, get, del, patch } from "@/utils/http";
import qs from "qs";

const handleUrlParams = (url, params) => {
  if (params) {
    params = qs.stringify(params);
    if (!url.includes("?")) params = "?" + params;
    else if (url.split("?")[1]) params = "&" + params;
    url += params;
  }
  return url;
};

// 【会议】查询空闲会议室
export const getIdelMeetingRoom = (params) => {
  const url = handleUrlParams(
    "/gc/ekp/api/km-imeeting/kmImeetingRestService/findFreeImeeting",
    params
  );
  return http.post(
    url,
    {},
    {
      headers: {
        "X-Agp-Appkey": "d6fa38253c0b47188076440cbac4e699",
      },
    }
  );
};

// 推荐空闲会议室
export const recommandFreeMeeting = (params) =>
  http.post(
    "/gc/orch/ekp/api/km-imeeting/kmImeetingRestService/findFreeImeeting",
    params,
    {
      headers: {
        "X-Agp-Appkey": "d6fa38253c0b47188076440cbac4e699",
      },
    }
  );

// 【会议】发起会议室预定申请表单
export const applyMeetingRoom = (params) => {
  const url = handleUrlParams(
    "/orch/gc/ekp/api/km-imeeting/kmImeetingRestService/addImeeting",
    params
  );
  return http.post(
    "/orch/gc/ekp/api/km-imeeting/kmImeetingRestService/addImeeting",
    params,
    {
      headers: {
        "X-Agp-Appkey": "d6fa38253c0b47188076440cbac4e699",
      },
    }
  );
};

// 上报卡片状态
export const updateCardStatus = (params, appCode) => {
  return post(`/api/octopus-api/api/cards/status/save`, params, {
    headers: {
      Authorization: "Bearer " + localStorage.getItem(appCode),
    },
  });
};
// 查询卡片状态
export const getCardStatus = (params, appCode) => {
  return post(`/api/octopus-api/api/cards/status/list`, params, {
    headers: {
      Authorization: "Bearer " + localStorage.getItem(appCode),
    },
  });
};

// 【生产】盖雅考勤-获取token
export const getGYtoken = (params) =>
  post(`/gc/api/common/accesstoken`, params);

// 员工月度考勤结果
export const getMyAttendanceInfo = (params) =>
  http.post("/api/my_attendance/query_month", params, {
    headers: {
      "X-Agp-Appkey": "d6fa38253c0b47188076440cbac4e699",
    },
  });

// 【编排】考勤异常表单提交
export const submitAbnormalAttendances = (params) =>
  http.post("/orch/api/km-review/kmReviewRestService/addReviewNew", params, {
    headers: {
      "X-Agp-Appkey": "d6fa38253c0b47188076440cbac4e699",
    },
  });

// 获取请假类型列表
export const getLeaveTypeOpts = (params) =>
  http.get("/gc/getLeaveType", {
    headers: {
      "X-Agp-Appkey": "d6fa38253c0b47188076440cbac4e699",
    },
  });
// 获取预置文案
export const getQuickOrderConfig = (params) =>
  get("/gc/api/prefabricatedProblemList");

// 请假申请
export const applyLeave = (params) =>
  http.post("/gc/api/km-review/kmReviewRestService/addReview", params, {
    headers: {
      "X-Agp-Appkey": "d6fa38253c0b47188076440cbac4e699",
      // 'Content-Type': 'multipart/form-data'
    },
  });
// 【生产】盖雅考勤-员工假期余额
export const getRemainingHoliday = (params) =>
  http.post("/api/my_attendance/query_leave", params, {
    headers: {
      "X-Agp-Appkey": "d6fa38253c0b47188076440cbac4e699",
    },
  });
// 【生产】盖雅考勤-获取请假时数
export const getLeaveDays = (params) =>
  http.post("/api/leave/hours", params, {
    headers: {
      "X-Agp-Appkey": "d6fa38253c0b47188076440cbac4e699",
    },
  });
// OA信息查询接口
export const queryUserInfo = (params) =>
  http.post("/oa/inner/queryUserInfo", params, {
    headers: {
      "X-Agp-Appkey": "d6fa38253c0b47188076440cbac4e699",
    },
  });

// 登录人直接主管查询
export const queryDirLeaderUserInfo = (params) =>
  http.post("/oa/inner/searchUserByPinyin", params, {
    headers: {
      "X-Agp-Appkey": "d6fa38253c0b47188076440cbac4e699",
    },
  });
// 音频文件转文本
export const voiceFileToText = (params) =>
  http.post(
    "https://ml-api-gw-en.tcl.com/gc/speech/recognition/conversation/cognitiveservices/v1?language=zh-CN",
    params,
    {
      headers: {
        "Content-Type": "application/octet-stream",
        "X-Agp-Appkey": "ddcf9ff445d64473a8ce30a6c6531a02",
      },
    }
  );
