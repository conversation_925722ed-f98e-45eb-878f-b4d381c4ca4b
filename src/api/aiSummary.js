import { userStore } from '@/stores'
import { fetchEventSource } from '@microsoft/fetch-event-source'

/**
 * AI摘要流式请求服务
 */
export class AISummaryService {
  constructor() {
    this.baseURL = import.meta.env.VITE_HTTP_BASE_URL
    this.endpoint = '/purchase/v1/purchase/query'
  }

  /**
   * 获取请求头
   */
  getHeaders() {
    const { token, uid } = userStore()

    const headers = {
      'Content-Type': 'application/json',
      Authorization: `bearer ${token}`,
      accept: '*/*',
    }

    if (token) {
      headers.Authorization = `bearer ${token}`
    }
    if (uid) {
      headers.loginAccount = uid
    }

    return headers
  }

  /**
   * 生成AI摘要（使用 fetchEventSource）
   * @param {Object} params - 请求参数
   * @param {Function} onProgress - 进度回调
   * @param {Function} onError - 错误回调
   * @param {Function} onComplete - 完成回调（可选）
   * @param {AbortController} abortController - 用于中断请求的控制器（可选）
   * @returns {Promise<void>}
   */
  async generateSummaryWithParams(params, onProgress, onError, onComplete, abortController) {
    const url = `${this.baseURL}${this.endpoint}`

    try {
      const fetchOptions = {
        method: 'POST',
        headers: this.getHeaders(),
        body: JSON.stringify(params),

        onopen(response) {
          console.log('连接已打开:', response.status, response.statusText)
          console.log('响应头:', Object.fromEntries(response.headers.entries()))

          if (response.ok) {
            const contentType = response.headers.get('content-type')
            console.log('Content-Type:', contentType)

            // 接受 text/event-stream 或其他可能的内容类型
            if (
              contentType?.includes('text/event-stream') ||
              contentType?.includes('text/plain') ||
              contentType?.includes('application/json')
            ) {
              return // 一切正常
            } else {
              console.warn('意外的Content-Type，但响应状态正常，继续处理')
              return // 继续尝试处理
            }
          } else if (response.status >= 400 && response.status < 500 && response.status !== 429) {
            // 客户端错误，不重试
            throw new Error(`HTTP ${response.status}: ${response.statusText}`)
          } else {
            // 其他错误，可能会重试
            throw new Error(`HTTP ${response.status}: ${response.statusText}`)
          }
        },

        onmessage: event => {
          // console.log('收到SSE消息:', event.data)

          // 处理结束信号
          if (event.data === '[DONE]' || event.data === 'DONE') {
            // console.log('收到结束信号')
            return
          }

          // 使用新的解析方法
          const parseResult = this.parseStreamMessage(event.data)

          if (parseResult.success) {
            // 成功解析，发送内容
            if (parseResult.content !== undefined && parseResult.content !== null) {
              onProgress(parseResult.content)
            }
          } else {
            // 解析失败，但仍然尝试发送原始内容
            console.warn('使用原始数据作为备选')
            if (parseResult.content && parseResult.content.trim()) {
              onProgress(parseResult.content)
            }
          }
        },

        onerror(error) {
          console.error('流式请求错误:', error)
          onError(error)
          throw error // 停止重试
        },

        onclose() {
          // console.log('连接已关闭')
          // 调用完成回调
          if (onComplete && typeof onComplete === 'function') {
            onComplete()
          }
        },
      }

      // 如果提供了 AbortController，添加 signal
      if (abortController) {
        fetchOptions.signal = abortController.signal
      }

      await fetchEventSource(url, fetchOptions)
    } catch (error) {
      console.error('fetchEventSource 错误:', error)
      onError(error)
    }
  }

  /**
   * 解析流式消息数据
   * @param {string} rawData - 原始数据
   * @returns {Object} - 解析结果
   */
  parseStreamMessage(rawData) {
    try {
      const data = JSON.parse(rawData)

      // 详细记录接收到的数据结构
      // console.log('解析的数据结构:', {
      //   event: data.event,
      //   conversation_id: data.conversation_id,
      //   message_id: data.message_id,
      //   answer: data.answer,
      //   created_at: data.created_at,
      //   task_id: data.task_id,
      //   fullData: data
      // })

      return {
        success: true,
        content: data.answer !== undefined ? data.answer : '',
        metadata: {
          event: data.event,
          conversation_id: data.conversation_id,
          message_id: data.message_id,
          created_at: data.created_at,
          task_id: data.task_id,
        },
      }
    } catch (error) {
      console.warn('数据解析失败:', error, '原始数据:', rawData)
      return {
        success: false,
        content: rawData,
        error: error.message,
      }
    }
  }

  /**
   * 测试接口连通性
   * @param {Object} params - 测试参数
   * @returns {Promise<Object>} - 测试结果
   */
  async testConnection(params) {
    const url = `${this.baseURL}${this.endpoint}`

    try {
      // console.log('测试连接到:', url)
      // console.log('请求参数:', params)
      // console.log('请求头:', this.getHeaders())

      // 直接尝试POST请求
      const postResponse = await fetch(url, {
        method: 'POST',
        headers: this.getHeaders(),
        body: JSON.stringify(params),
      })

      // console.log('POST响应:', {
      //   status: postResponse.status,
      //   statusText: postResponse.statusText,
      //   headers: Object.fromEntries(postResponse.headers.entries())
      // })

      return {
        success: postResponse.ok,
        status: postResponse.status,
        statusText: postResponse.statusText,
        response: postResponse,
      }
    } catch (error) {
      console.error('连接测试失败:', error)
      return {
        success: false,
        error: error.message,
      }
    }
  }
}

// 创建单例实例
export const aiSummaryService = new AISummaryService()

// 默认导出
export default aiSummaryService
