// import http, { post, get, del, patch } from "@/utils/http";
import { CustomRequest } from "@/utils/CustomRequest";
import qs from "qs";
import { userStore } from "@/stores";
import http, { post, get, del, patch } from "@/utils/http";

const handleUrlParams = (url, params) => {
  if (params) {
    params = qs.stringify(params);
    if (!url.includes("?")) params = "?" + params;
    else if (url.split("?")[1]) params = "&" + params;
    url += params;
  }
  return url;
};
// 对话
export const getAnswer = (params) =>
  post(`https://api-gw-en-uat.tcl.com/bi/v1/chatbi/query`, params);

// 推荐话题
export const recommendTopic = (domainId) => {
  return CustomRequest({
    url: `/chatbi/v1/problem?domainId=${domainId}`,
    method: "get",
  });
};

