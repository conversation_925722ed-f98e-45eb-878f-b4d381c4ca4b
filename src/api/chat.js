import http, { post, get, del, patch } from '@/utils/http'
import qs from 'qs'
import { userStore } from "@/stores"

const handleUrlParams = (url, params)=> {
  if (params) {
    params = qs.stringify(params)
    if (!url.includes('?')) params = '?' + params
    else if (url.split('?')[1]) params = '&' + params
    url += params
  }
  return url
}


//获取jwt和用户信息 (TCL实业)
export const getTclAuth = (tclToken, appCode) => http.get(`/api/octopus-api/api/passport/tcl` , {
  headers: {
    'TCL-Authorization': tclToken,
    'X-App-Code': appCode,
  }
})

//章鱼小格聊天发送消息
export const msgSent = (params) => post(`/api/octopus-api/api/chat-messages`, params)

//表单字段解析
export const msgSentForForm = (params, fromAppCode) => {
  const { getFormTokenByCode } = userStore()
  return http.post(`/api/octopus-api/api/chat-messages`, params,{
    headers: {
      'Authorization': 'Bearer ' + getFormTokenByCode(fromAppCode),
    }})
}

// 停止流式输出
export const stopStream = (taskId) => post(`/api/octopus-api/api/chat-messages/${taskId}/stop`)


// 根据应用code获取访问该应用相关信息时的token信息
export const getAppTokenByCode = (appCode) => http.get(`/api/octopus-api/api/passport`, {
  headers: {
    'X-App-Code': appCode,
  }
})

// 获取chat聊天应用信息
export const getChatSiteInfo = () => get(`/api/octopus-api/api/site`)

// 获取chat聊天动态参数
export const getChatParameterInfo = () => get(`/api/octopus-api/api/parameters`)


// 获取应用历史会话记录(非置顶)
export const getConversitionListByChatId = (params) => get('/api/octopus-api/api/conversations', params)

// 获取应用会话历史聊天记录
export const getHistoryListByChatId = ( params) => get('/api/octopus-api/api/messages', params)

// 根据messageId更新用户反馈内容
export const updateFeedbackInfo = (msgId, params) => post(`/api/octopus-api/api/messages/${msgId}/feedbacks`, params)

// 重命名会话
export const renameConversation = ( id, name) => post(`/api/octopus-api/api/conversations/${id}/name`, {name})

// 自动更新会话名称
export const autoNameConversation = ( id) => post(`/api/octopus-api/api/conversations/${id}/name`, {auto_generate:true})

// 删除会话
export const delConversationById = ( id) => del(`/api/octopus-api/api/conversations/${id}`)
// 删除所有会话
export const delAllConversations = (id) => del(`/api/octopus-api/api/conversations/all`)

// 置顶会话
export const pinConversation = (id) => patch(`/api/octopus-api/api/conversations/${id}/pin`,{})
// 取消置顶会话
export const unpinConversation = ( id) => patch(`/api/octopus-api/api/conversations/${id}/unpin`,{})
