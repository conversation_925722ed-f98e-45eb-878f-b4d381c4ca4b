// import http, { post, get, del, patch } from "@/utils/http";
import { CustomRequest } from '@/utils/CustomRequest'
import qs from 'qs'
import { userStore } from '@/stores'
import http, { post, get, del, patch } from '@/utils/http'

const handleUrlParams = (url, params) => {
  if (params) {
    params = qs.stringify(params)
    if (!url.includes('?')) params = '?' + params
    else if (url.split('?')[1]) params = '&' + params
    url += params
  }
  return url
}
// 对话
export const getAnswer = params => post(`https://api-gw-en-uat.tcl.com/bi/v1/chatbi/query`, params)

// 推荐话题
export const recommendTopic = domainId => {
  return CustomRequest({
    url: `/chatbi/v1/problem?domainId=${domainId}`,
    method: 'get',
  })
}

export const updateFeedback = data =>
  CustomRequest({
    url: '/chatbi/v1/thumbsUp/addDataThumbsUp',
    method: 'post',
    data,
  })

// 联想词
export const getRecommendListApi = domainId =>
  CustomRequest({
    url: `/chatbi/v1/recommend/${domainId}`,
    method: 'get',
  })

// 重新生成解读
export const getNewAnalyse = data =>
  CustomRequest({
    url: `/chatbi/v1/chatbi/analysis`,
    method: 'post',
    data,
  })

//查询基础设置
export const getBaseSetApi = params => {
  return CustomRequest({
    url: `/chatbi/v1/basic`,
    method: 'get',
    params,
  })
}
//查询维度的枚举值
export const getDimensionApi = domainId => {
  return CustomRequest({
    url: `/chatbi/v1/model/detail/dimension/enum/domain?domainId=${domainId}`,
    method: 'get',
    timeout: 60000,
  })
}

//归因分析
export const getAttributionData = data =>
  CustomRequest({
    url: 'chatbi/v1/chatbi/getAttributionData',
    timeout: 60000,
    method: 'post',
    data,
  })

//点击立即体验,修改已读入门指引
export const changeReadGuide = (domainId, oa, data) =>
  CustomRequest({
    url: `chatbi/v1/domain/update/guide?domainId=${domainId}&oa=${oa}`,
    method: 'post',
    data,
  })

// 切换问题 get  http://localhost:9030/v1/template/handoff?domainId=29
export const changeQsApi = domainId => {
  return CustomRequest({
    url: `/chatbi/v1/template/handoff?domainId=${domainId}`,
    method: 'get',
  })
}

//猜你想问  get http://localhost:9030/v1/template/guess?domainId=2&question=第二季度
export const guessQsApi = (domainId, question) => {
  return CustomRequest({
    url: `/chatbi/v1/template/guess?domainId=${domainId}&question=${question}`,
    method: 'get',
  })
}

//上传图片生成在线url
export const getImgUrl = formData => {
  return CustomRequest({
    url: `/chatbi/v1/upload`,
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
}

//筛选维度，下拉枚举值
export const getFieldsData = (modelId, params) => {
  return CustomRequest({
    url: `/chatbi/v1/model/detail/dimension/${modelId}/enum`,
    method: 'get',
    params,
  })
}

//筛选维度，重新查询
export const getTable = data => {
  return CustomRequest({
    url: `/chatbi/v1/chatbi/queryData`,
    method: 'post',
    data,
  })
}

//筛选维度，下拉pick的值
export const getPickData = data => {
  return CustomRequest({
    url: `/chatbi/v1/chatbi/backFillingDimension`,
    method: 'post',
    data,
  })
}

//AI洞察报告，获取步骤条(板块信息)
export const getStepsData = data => {
  return CustomRequest({
    url: `/chatbi/v1/data/insight/getPlateModuleInfo`,
    method: 'post',
    data,
  })
}

//AI洞察报告，生成报告
export const getReportData = data => {
  return CustomRequest({
    url: `/chatbi/v1/data/insight`,
    method: 'post',
    data,
  })
}

// 获取问题域
export const getQuestionDomain = domainId =>
  CustomRequest({
    url: `/chatbi/v1/data/insight/question/domain/${domainId}`,
    method: 'get',
  })

// 获取历史报告
export const getHistoryReport = params =>
  CustomRequest({
    url: `/chatbi/v1/data/insight/history`,
    method: 'get',
    params,
  })

// 保存为历史报告
//AI洞察报告，数据总结
export const getSummaryApi = data => {
  return CustomRequest({
    url: `/chatbi/v1/chatbi/analysisReportData`,
    method: 'post',
    data,
  })
}

//AI洞察报告，保存报告名称、时间等
export const saveReport = data => {
  return CustomRequest({
    url: `/chatbi/v1/data/insight/history`,
    method: 'post',
    data,
  })
}

//AI洞察报告，获取数据来源和数据备注
export const getDataSource = domainId => {
  return CustomRequest({
    url: `/chatbi/v1/data/insight/getModel/${domainId}`,
    method: 'get',
  })
}

//删除对话历史
export const deleteChatHistoryApi = data => {
  return CustomRequest({
    url: `/chatbi/v1/dialogue`,
    method: 'delete',
    data,
  })
}

//修改单个对话历史
export const updateChatHistoryApi = data => {
  return CustomRequest({
    url: `/chatbi/v1/dialogue`,
    method: 'put',
    data,
  })
}

//新加单个对话历史
export const addChatHistoryApi = data => {
  return CustomRequest({
    url: `/chatbi/v1/dialogue`,
    method: 'post',
    data,
  })
}

//对话历史,传chatId返回详情，不传返回全部
export const getChatHistoryDetailApi = params => {
  return CustomRequest({
    url: `/chatbi/v1/dialogue`,
    method: 'get',
    params,
  })
}
