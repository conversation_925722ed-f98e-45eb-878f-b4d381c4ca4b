import { CustomRequest } from "@/utils/CustomRequest";

const { VITE_CLIENT_ID } = import.meta.env;

export const loginInAiPlatformApi = (params) =>
  CustomRequest({
    url: "/oauth/login",
    method: "post",
    needAuth: false,
    params,
  });

export const loginInPaasApi = (params) =>
  CustomRequest({
    url: "/oauth/token/getAccessTokenByPaasToken",
    method: "get",
    needAuth: false,
    params,
  });

export const loginInIdmApi = (params) =>
  CustomRequest({
    url: "/oauth/token/getAccessTokenByIdmToken",
    method: "get",
    needAuth: false,
    params,
    withCredentials: true,
  });

export const getUserInfoApi = () => {
  return CustomRequest({
    url: "/oauth/public/getUserInfo",
    method: "get",
    params: {
      client_id: VITE_CLIENT_ID,
    },
  });
};

export const loginInTxinApi = (params) =>
  CustomRequest({
    url: `/oauth/third/tXin/auth/getToken/${params.token}?client_id=${params.client_id}`,
    method: "get",
    needAuth: false,
  });
