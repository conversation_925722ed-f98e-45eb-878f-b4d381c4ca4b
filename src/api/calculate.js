import { CustomRequest } from '@/utils/CustomRequest'

// 获取计算任务列表
export const getCalculateTaskList = params => {
  return CustomRequest({
    url: '/purchase/v1/calculate/getCalculateTaskList',
    method: 'get',
    params: params,
  })
}

// 删除计算任务列表
export const delCalculateTask = data => {
  return CustomRequest({
    url: '/purchase/v1/calculate/batch',
    method: 'delete',
    data: data,
  })
}

// 获取品类物料编码列表
export const getCategoryMaterialList = params => {
  return CustomRequest({
    url: '/purchase/v1/calculate/queryCategoryItemCascadeTree',
    method: 'get',
    params: params,
  })
}

// 复制任务
export const copyTask = id => {
  return CustomRequest({
    url: `/purchase/v1/calculate/copy/${id}`,
    method: 'get',
  })
}

// 获取配置项详情
export const getConfigDetails = params => {
  return CustomRequest({
    url: '/purchase/v1/calculate/getTaskDetail',
    method: 'post',
    data: params,
  })
}

// 保存测算配置
export const saveCalculateConfig = data => {
  return CustomRequest({
    url: '/purchase/v1/calculate',
    method: 'post',
    data: data,
  })
}

// 获取品类列表
export const getCategoryList = data => {
  return CustomRequest({
    url: `/purchase/v1/calculate/queryCategoryTree`,
    method: 'get',
    data,
  })
}

// 获取物料
export const getMaterialList = data => {
  return CustomRequest({
    url: `/purchase/v1/calculate/queryItemTree`,
    method: 'get',
    params: data,
  })
}
