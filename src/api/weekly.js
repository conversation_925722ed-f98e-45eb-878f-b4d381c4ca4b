import { CustomRequest } from '@/utils/CustomRequest'

// 查物料的接口
export const materialCode = name => {
  return CustomRequest({
    url: `/purchase/v1/purchase/materialCode?name=${name || ''}`,
    method: 'get',
  })
}
// 获取报表
export const reportForm = materialCode => {
  return CustomRequest({
    url: `/purchase/v1/purchase/report/form?materialCode=${materialCode}`,
    method: 'get',
  })
}
// 产能供需分析趋势
export const purchaseReportTrend = materialCode => {
  return CustomRequest({
    url: `/purchase/v1/purchase/report/trend?materialCode=${materialCode}`,
    method: 'get',
  })
}

// 获取公司选择框
export const getCompanys = () => {
  return CustomRequest({
    url: `/purchase/v1/SRM/queryCompanyList`,
    method: 'post',
    data: {
      organizationLevelCodes: [('ORG01', 'ORG02')],
    },
  })
}

// 供应商选择框
export const getSuppliers = data => {
  return CustomRequest({
    url: `/purchase/v1/SRM/querySupplierList`,
    method: 'post',
    data,
  })
}
