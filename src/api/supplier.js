import { request } from '@/common'

// 专门用于 SRM 接口的请求函数（走 Vite 代理）
const SrmRequest = options => {
  const timeout = options?.timeout || import.meta.env.VITE_HTTP_TIMEOUT
  return request({
    baseURL: '', // 不设置 baseURL，使用相对路径走代理
    timeout,
    requestOptions: {
      url: options.url,
      method: options.method,
      data: options.data,
      params: options.params,
      withCredentials: true, // 允许携带 Cookie
      headers: {
        'Content-Type': 'application/json',
        'api-token':
          'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.e30.lmrVZ0D10hANx003XZhwnv2L3HwWYiXMmzx1VfuF18M',
        ...options.headers,
      },
      ...options,
    },
    errorCallback: (errStatus, errorMessage) => {
      console.error('SRM API Error:', errStatus, errorMessage)
      return Promise.reject(errorMessage)
    },
  })
}

// 获取供应商分页数据
export const getSupplierPagedQuery = params => {
  return SrmRequest({
    url: '/srm/api/contract/tenant/supplierCapacityDataN3/pageQuery',
    method: 'post',
    data: params,
  })
}

// 获取计算任务列表
export const getCalculateTaskList = params => {
  return SrmRequest({
    url: '/srm/v1/calculate/getCalculateTaskList',
    method: 'get',
    params: params,
  })
}
