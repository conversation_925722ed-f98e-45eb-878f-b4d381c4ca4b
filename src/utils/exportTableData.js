import * as FileSaver from 'file-saver'
import ExcelJS from 'exceljs'
import { inTxinMobile } from '@/utils/index'
import { tXinMobileDownload } from './tXinMobileDownload'
function getDateTime() {
  const date = new Date();
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  const hour = date.getHours().toString().padStart(2, '0');
  const minute = date.getMinutes().toString().padStart(2, '0');
  const second = date.getSeconds().toString().padStart(2, '0');

  return `${year}${month}${day}${hour}${minute}${second}`;
}

/**
 * 将table导数据导出为excel文档
 * @param {*} columns 表头
 * @param {*} dataSource 数据
 * @param {*} fileName 名称
 */
export async function exportTableData(columns, dataSource, fileName, imageData = null) {
  console.log("[下载excel] ", columns, dataSource, fileName);

  // 创建一个新的工作簿
  const workbook = new ExcelJS.Workbook();

  // 处理Sheet1（表格数据）
  if (columns.length > 0 && dataSource.length > 0) {
    const worksheet1 = workbook.addWorksheet('Sheet1');

    // 添加表头
    worksheet1.addRow(columns.map(column => column.title));

    // 添加数据行
    dataSource.forEach(row => {
      worksheet1.addRow(columns.map(column => row[column.dataIndex]));
    });
  }

  // 处理Sheet2（图片数据）
  if (imageData) {
    const worksheet2 = workbook.addWorksheet('Sheet2');

    // 设置列宽
    worksheet2.properties.defaultColWidth = 30;

    // 将base64图片数据转换为buffer
    const imageId = workbook.addImage({
      base64: imageData,
      extension: 'png',
    });

    // 添加图片到工作表
    worksheet2.addImage(imageId, {
      tl: { col: 0, row: 0 }, // 左上角位置
      br: { col: 4, row: 14 }, // 右下角位置
      editAs: 'oneCell'
    });
  }

  // 生成文件名
  let _fileName = `${fileName}_${getDateTime()}.xlsx`;

  // 导出文件
  const buffer = await workbook.xlsx.writeBuffer();
  const blob = new Blob([buffer], {
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  });

  // 移动端T信
  if (inTxinMobile()) {
    await tXinMobileDownload(blob, buffer, _fileName)
    return
  }

  FileSaver.saveAs(blob, _fileName);
}