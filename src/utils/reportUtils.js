/**
 * 报表相关工具函数
 */

/**
 * 构建iframe报表链接
 * @param {string} versionNo - 版本号
 * @returns {string} 完整的报表链接
 */
export const buildReportUrl = versionNo => {
  if (!versionNo) {
    console.warn('buildReportUrl: versionNo 参数为空')
    return ''
  }

  const domain = import.meta.env.VITE_IFRAME_REPORT_DOMAIN
  if (!domain) {
    console.error('VITE_IFRAME_REPORT_DOMAIN 环境变量未配置')
    return ''
  }

  // 报表路径和参数
  const reportPath = '/webroot/decision/view/report'
  const viewlet = 'kt_fin/supply/供需平衡多维分析报表.cpt'

  return `${domain}${reportPath}?viewlet=${viewlet}&version_no=${versionNo}`
}

/**
 * 获取当前环境的报表域名
 * @returns {string} 报表域名
 */
export const getReportDomain = () => {
  return import.meta.env.VITE_IFRAME_REPORT_DOMAIN || ''
}

/**
 * 检查报表环境配置是否正确
 * @returns {boolean} 配置是否正确
 */
export const checkReportConfig = () => {
  const domain = import.meta.env.VITE_IFRAME_REPORT_DOMAIN
  const env = import.meta.env.VITE_ENV

  if (!domain) {
    console.error('报表配置错误: VITE_IFRAME_REPORT_DOMAIN 未配置')
    return false
  }

  // 检查生产环境和非生产环境的域名是否正确
  if (env === 'prod') {
    if (!domain.includes('pangu.tcl.com')) {
      console.warn('生产环境应使用 pangu.tcl.com 域名')
    }
  } else {
    if (!domain.includes('pangu-uat.tcl.com')) {
      console.warn('非生产环境应使用 pangu-uat.tcl.com 域名')
    }
  }

  return true
}
