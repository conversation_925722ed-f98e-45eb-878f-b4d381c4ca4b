import { reactive, readonly } from "vue";

// 创建一个响应式对象作为事件存储
let queue = reactive(new Map());

export default readonly({
  // 事件派发
  emit(event, payload) {
    const callbacks = queue.get(event);
    if (callbacks) {
      callbacks.forEach((callback) => callback(payload));
    }
  },
  // 事件监听
  on(event, callback) {
    if (!queue.has(event)) {
      queue.set(event, new Set());
    }
    queue.get(event).add(callback);
  },
  // 移除事件监听
  off(event, callback) {
    const callbacks = queue.get(event);
    if (callbacks) {
      callbacks.delete(callback);
    }
    if (!event) {
      queue = reactive(new Map());
    }
  },
});
