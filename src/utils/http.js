import qs from "qs";
import axios from "axios";
import { getToken, getCookie, setCookie } from "./cookie";
import { useAppsStore, userStore } from "@/stores";
const http = axios.create();

http.defaults.timeout = 60 * 1000 * 5;

http.interceptors.request.use(
  (config) => {
    const { getCurAppToken, getAppToken } = useAppsStore();

    if (userStore().uid) {
      config.headers.loginAccount = userStore().uid;
    }
    if (userStore().token) {
      config.headers.Authorization = "bearer " + userStore().token;
    }

    // if(!getCookie('access_octopus_token')) {
    //   setCookie('access_octopus_token','access_octopus_token=Bearer%20eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************.xq94C2VbXIKEmpiSk-B_2CZKILDeETlQQoBGOrdZhYk' )
    // }

    return config;
  },
  (err) => {
    return Promise.reject(err);
  }
);
http.interceptors.response.use((res) => {
  switch (res.data.code) {
    case 401:
      location.href = "/login";
      return Promise.reject(res.data.msg);
    // case 1004:
    case 1005:
      return Promise.reject(res.data.msg);
    case 4000: // 令牌过期、失效 跳转登录
      location.href = "/login";
      return Promise.reject(res.data.msg);
    default:
      return res;
  }
});
export default http;

export function request(method, url, params) {
  return http[method](url, params).then((res) => {
    return res.data;
  });
}
request.get = get;
request.post = post;
request.put = put;
request.del = del;
request.patch = patch;

export function get(url, params) {
  if (params) {
    params = qs.stringify(params);
    if (!url.includes("?")) params = "?" + params;
    else if (url.split("?")[1]) params = "&" + params;
    url += params;
  }
  return request("get", url);
}
export function post(url, params) {
  return request("post", url, params);
}
export function put(url, params) {
  return request("put", url, params);
}
export function del(url, params) {
  return request("delete", url, params);
}
export function patch(url, params) {
  return request("patch", url, params);
}
