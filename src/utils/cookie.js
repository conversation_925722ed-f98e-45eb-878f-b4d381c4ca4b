import Cookie from 'js-cookie'

// 获取根域名 - 支持 以下类型 域名
// IP
// localhost
// .com
// .com.cn
function getMainHost() {
  const key = `mh_${Math.random()}`
  const value = `mh_${Math.random()}`

  const domain = document.domain
  const domainList = domain.split('.')
  const list = []

  while (domainList.length) {
    list.unshift(domainList.pop())

    const mainHost = list.join('.')

    // 设置 cookie
    Cookie.set(key, value, { domain: mainHost })

    // 判断 cookie 是否设置成功
    if (Cookie.get(key, { domain: mainHost }) === value) {
      Cookie.remove(key, { domain: mainHost }) // 删除 测试的 cookie
      return mainHost
    }
  }
}

const MAIN_HOST = getMainHost()

/**
 * 设置cookie
 * @param  {String} name
 * @param  {String} value
 */
export function setCookie(name, value) {
  Cookie.set(name, value, { domain: MAIN_HOST })
}
/**
 * 读取cookie
 * @param  {String} name
 */
export function getCookie(name) {
  return Cookie.get(name, { domain: MAIN_HOST })
}
/**
 * 移除cookie
 * @param  {String} name
 */
export function removeCookie(name = '') {
  if (name) {
    Cookie.remove(name, { domain: MAIN_HOST })
  }
}
/**
 * 移除所有cookie
 */
export function clearCookie() {
  Object.keys(Cookie.get() || {}).forEach((key) => {
    Cookie.remove(key, { domain: MAIN_HOST })
  })
}

export const ACCESS_TOKEN = 'access_octopus_token'
export const TOKEN_TYPE = 'token_type'

export const getToken = () => {
  const accessToken = getCookie(ACCESS_TOKEN)
  const tokenType = getCookie(TOKEN_TYPE)

  return accessToken || ''
}