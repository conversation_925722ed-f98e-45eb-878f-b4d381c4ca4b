import { historyStore } from "@/stores";
import useWindowSize from "@/hooks/useWindowSize.js";

const DATAZOOM_HEIGHT = 20 // 高度
const DATAZOOM_BOTTOM = 5 // 距离底部距离

const colorArray = [
  ["#7B6FFF", "#CFCBFF"],
  ["#60D8D8", "#A2EFEF"],
  ["#9ED98D", "#CEEBC5"],
  ["#BE99FD", "#E4D4FF"],
  ["#779BFC", "#C3D3FD"],
  ["#F6BD16", "#FFEDB8"],
  ["#FF7D00", "#FFD9B5"],
]

/** 数据标签*/
export const seriesLabel = () => {
  const showGraphNum = historyStore().showGraphNum;
  if (showGraphNum) {
    return {
      show: true, // 显示每个点的数值
      position: "top", // 数值显示的位置，这里选择顶部
    };
  } else {
    return {};
  }
};
/** 图表的颜色配置*/
export const colorList = [
  "#6355FF",
  "#779BFC",
  "#BE99FD",
  "#84DBDB",
  "#9ED98D",
  "#F6BD16",
  "#FF7D00",
  "#8A97B0",
  "#A9A0FF",
  "#C3D3FD",
  "#E4D4FF",
  "#C1EDED",
  "#CEEBC5",
  "#FFEDB8",
  "#FFD9B5",
];

export const themeColor = ["#6355FF", "#779BFC"];

// 添加柱状图渐变色配置
export const barGradientColors = colorArray.map(([startColor, endColor]) => ({
  type: 'linear',
  x: 0,
  y: 0,
  x2: 0,
  y2: 1, // 从上到下的渐变
  colorStops: [
    { offset: 0, color: startColor },
    { offset: 1, color: endColor }
  ]
}));

// 水平柱状图渐变色配置（从左到右）
export const horizontalBarGradientColors = colorArray.map(([startColor, endColor]) => ({
  type: 'linear',
  x: 0,
  y: 0,
  x2: 1, // 从左到右的渐变
  y2: 0,
  // 水平柱状图需要反着来
  colorStops: [
    { offset: 0, color: endColor },
    { offset: 1, color: startColor  }
  ]
}));

// 获取垂直渐变色的函数
export const getBarGradientColor = (index) => {
  return barGradientColors[index % barGradientColors.length];
};

// 获取水平渐变色的函数
export const getHorizontalBarGradientColor = (index) => {
  return horizontalBarGradientColors[index % horizontalBarGradientColors.length];
};

// 线图面积渐变色配置（从上到下的渐变，透明度更低）
export const lineAreaGradientColors = colorArray.map(([startColor, endColor]) => ({
  type: 'linear',
  x: 0,
  y: 0,
  x2: 0,
  y2: 1, // 从上到下的渐变
  colorStops: [
    { offset: 0, color: startColor + '4D' }, // 添加30%透明度
    { offset: 1, color: endColor + '0D' }    // 添加5%透明度
  ]
}));

// 获取线图面积渐变色的函数
export const getLineAreaGradientColor = (index) => {
  return lineAreaGradientColors[index % lineAreaGradientColors.length];
};

// 新增的通用渐变色配置（基于 colorArray）
export const newGradientColors = colorArray.map(([startColor, endColor]) => ({
  type: 'linear',
  x: 0,
  y: 0,
  x2: 1, // 从左到右的渐变
  y2: 0,
  colorStops: [
    { offset: 0, color: startColor },
    { offset: 1, color: endColor }
  ]
}));

// 获取新渐变色的函数
export const getNewGradientColor = (index) => {
  return newGradientColors[index % newGradientColors.length];
};

// 垂直方向的新渐变色配置（从上到下）
export const newVerticalGradientColors = colorArray.map(([startColor, endColor]) => ({
  type: 'linear',
  x: 0,
  y: 0,
  x2: 0,
  y2: 1,
  colorStops: [
    { offset: 0, color: startColor },
    { offset: 1, color: endColor }
  ]
}));

// 获取垂直新渐变色的函数
export const getNewVerticalGradientColor = (index) => {
  return newVerticalGradientColors[index % newVerticalGradientColors.length];
};

export const setLegend = (series) => {
  const legend = {
    type: "scroll",
    orient: "horizontal",
    bottom: series?.length > 1 ? DATAZOOM_HEIGHT + DATAZOOM_BOTTOM : 0 ,
    itemHeight: 3,
    itemWidth: 10,
  };
  legend.data = series?.map((serie) => {
    return {
      name: serie.name || " ",
      lineStyle: {
        width: 4,
        type: "solid",
      },
    };
  });
  return legend;
};

export const setDataZoom = (series) => {
  const { isMobile } = useWindowSize();
  const data = series[0].data
  if (data?.length > 10) {
    if (isMobile.value) {
      // 移动端配置：更大的高度，显示更少的数据
      return [
        {
          type: "slider",
          height: DATAZOOM_HEIGHT, // 滑块高度
          bottom: DATAZOOM_BOTTOM, // 距离底部的距离
          left: 10, // 距离左边的距离
          right: 10, // 距离右边的距离
          start: 0,
          end: Math.ceil(((10 / data.length) * 100 / series.length)) , // 移动端显示更少的数据
          zoomLock: true,
          moveOnMouseMove: false,
          moveOnMouseWheel: false,
        },
      ];
    } else {
      // PC端配置：保持原有配置
      return [
        {
          type: "slider",
          height: DATAZOOM_HEIGHT, // 滑块高度
          bottom: DATAZOOM_BOTTOM, // 距离底部的距离
          left: 20, // 距离左边的距离
          right: 20, // 距离右边的距离
          start: 0,
          end: Math.ceil(((20 / data.length) * 100 / series.length)), // PC端显示20个数据
          zoomLock: true,
          moveOnMouseMove: false,
          moveOnMouseWheel: false,
        },
      ];
    }
  }
  return [];
};

export const setHorizontalDataZoom = (series) => {
  const data = series[0].data
  const { isMobile } = useWindowSize();
  if (data?.length > 10) {
    if (isMobile.value) {
      // 移动端水平图表配置：更大的宽度，显示更少的数据
      return [
        {
          type: "slider",
          width: 20, // 移动端增加宽度，便于触摸操作
          start: 0,
          end: Math.ceil(((5 / data.length) * 100 / series.length)) , // 移动端显示更少的数据（5个）
          yAxisIndex: 0,
          orient: "vertical",
          left: "right",
          zoomLock: true,
          moveOnMouseMove: false,
          moveOnMouseWheel: false,
        },
      ];
    } else {
      // PC端水平图表配置：保持原有配置
      return [
        {
          type: "slider",
          width: 15,
          start: 0,
          end: Math.ceil(((10 / data.length) * 100 / series.length)), // PC端显示10个数据
          yAxisIndex: 0,
          orient: "vertical",
          left: "right",
          zoomLock: true,
          moveOnMouseMove: false,
          moveOnMouseWheel: false,
        },
      ];
    }
  }
  return [];
};

export const mock = {
  data: {
    modelId: "9",
    understandQuestion:
      "今年2月份各区域取值包括('CBG','NABG','EUBG','APBG','MEABG','LABG','EMEA','LA&AP')收入分别同比增长情况",
    semanticSql:
      "  SELECT      '2025-02' AS 日期,     区域 AS 区域,     SUM(CASE WHEN 日期 BETWEEN '2025-02-01' AND '2025-02-28' THEN 收入 ELSE 0 END) AS 当期收入,     SUM(CASE WHEN 日期 BETWEEN '2024-02-01' AND '2024-02-28' THEN 收入 ELSE 0 END) AS 同期收入,     SUM(CASE WHEN 日期 BETWEEN '2025-02-01' AND '2025-02-28' THEN 收入 ELSE 0 END) /      SUM(CASE WHEN 日期 BETWEEN '2024-02-01' AND '2024-02-28' THEN 收入 ELSE 0 END) - 1 AS 收入同比增长 FROM      CHATBI全球营销指标GS统计表 WHERE      品类层级='0'     AND 品牌层级='0'     AND 机构层级='0'     AND 渠道层级='0'     AND 区域 IN ('CBG', 'NABG', 'EUBG', 'APBG', 'MEABG', 'LABG', 'EMEA', 'LA&AP') GROUP BY      区域    limit 1000;",
    chatQueryDebugVo: [
      {
        answer: '{  "time":"今年2月份",  "related":"相关" }',
        step: "ChatUnderstandWithLLM",
        prompt:
          '这是【用户的问题】：\r\n今年2月份各BG收入分别同比增长情况\r\n任务一:时间实体识别\r\n目标是提取出"用户的问题"中的时间实体。实体类别为time\r\n\r\n任务二：判断用户问题与指标是否相关\r\n\r\n输入内容校验：\r\n  用户的问题必须是一个问题，或者提出问题的格式，又或者是查询数据的请求（不能只是描述数据），如果不是，请返回‘不相关’，如果是，请继续执行下面的任务。\r\n以下是【指标信息】:\r\n[收入;销售额;累计收入;合计收入;累计营业额;总销售额;总营业额;营业额;总收入;月结收入;营业收入;销售收入;净收入;年累收入;表现如何;销售额情况;收入占比, 收入达成率;BP收入达成率;预算收入达成率;收入达成;收入目标达成;收入BP达成;符合预期, 销量;累计销量;合计销量;总销量;总销售量;月结销量;月结台数;总台数;台数;卖了多少台;卖了多少;卖的台数;零售台数, 销量达成率;销量达成率;销量达成;销量目标达成;销量BP达成;销量BP达成率, 目标收入;收入bp值;目标值, 目标销售;销量BP值, 当期销量;null, 同期销量;null, 收入目标偏差;收入偏差, 销量目标偏差;null, 收入同比;同比收入, 销量同比;销量同比增长, 同期收入;null, 当期收入;null, 收入BP;null, 销量BP;月度销量BP, 日期;, 品类层级;, 一级品类编码;, 一级品类名称;, 二级品类编码;, 一级品类;, 三级品类编码;, 二级品类;, 品牌层级;, 一级品牌编码;, 一级品牌名称;, 二级品牌编码;, 品牌名称;, 机构层级;, 一级机构编码;, 一级机构名称;, 二级机构编码;, 二级机构名称;, 三级机构编码;, 三级机构名称;, 渠道层级;, 一级渠道编码;, 一级渠道名称;, 二级渠道编码;, 一级渠道;, 三级渠道编码;, 二级渠道;, 四级渠道编码;, 三级渠道;, BP口径收入;, 收入;, 月度收入BP;, BP口径销量;, 销量;, 月度销量BP;, 区域;, 数仓更新时间;, BG分区;, 月分区字段;]\r\n\r\n以下是【任务二的要求】:\r\n  请严格分析用户问题是否与上述指标匹配。如果问题中提到的内容明确出现在上述指标列表中，或者与指标有直接关联，则判定为“相关”。如果问题中提到的内容未明确出现在上述指标列表中，或者与指标无直接关联，则判定为“不相关”。请仅返回“相关”或“不相关”，并确保不输出任何额外的解释或信息。\r\n注意：如果问题中提到的内容可以通过指标中的字段推导或匹配，也应判定为“相关\r\n特别注意：\r\n  1.如果用户问题中仅包含模糊或无意义的词语（如“测试”、“你好”、“你能干什么”等），且无法与指标列表中的任何内容匹配或推导，则判定为“不相关”。\r\n  2.请严格按照问题的实际内容进行判断，不要进行过度推测或联想。\r\n  3.用户的问题不是一个查询数据的请求，或者不是一个问题, 请返回 \'不相关\'."\r\n输出要求：\r\n  只返回相关或毫不相关，不要返回其他额外的解释信息\r\n输出格式:\r\n {\r\n "time":"时间实体",\r\n "related":"请参考任务二的要求回答【相关或者不相关】"\r\n }\r\n输出约束：\r\n  不要输出其他额外的解释信息',
      },
      {
        answer:
          "今年2月份各区域取值包括('CBG','NABG','EUBG','APBG','MEABG','LABG','EMEA','LA&AP')收入分别同比增长情况",
        step: "FixQuestionWithLLM",
        prompt: "今年,2月份,各BG,收入,分别,同比,增长,情况",
      },
      {
        answer:
          "['period_date', 'bg_name', 'sales_amt', 'year_revenue', 'brand_level', 'categ_level', 'org_level', 'channel_level']",
        step: "PickFieldWithLLM",
        prompt:
          '任务：\r\n以下是[表信息定义]：\r\n\n [ 数据表的中文名称： CHATBI全球营销指标GS统计表 数据表的英文名称： ads_sdbg_sl_chatbi_stat_index_gs_m 数据表的描述： null  数据表的 table_name ：ads_sdbg_sl_chatbi_stat_index_gs_m ] \n\r\n以下是表【ads_sdbg_sl_chatbi_stat_index_gs_m】所有字段信息声明:\r\n[{"dataType":"DATE","cnName":"日期","semanticsType":"主键","enName":"period_date","desc":"这个字段表示格式为年月(yyyy-mm-dd)的日期字段，表示该行记录统计时间所在的年份和月份。如果需要按月统计，可以使用这个字段"},{"dataType":"VARCHAR","cnName":"品类层级","semanticsType":"主键","enName":"categ_level","desc":"品类层级"},{"dataType":"VARCHAR","cnName":"一级品类编码","semanticsType":"主键","enName":"categ_code_l1","desc":"一级品类编码"},{"dataType":"VARCHAR","cnName":"一级品类名称","semanticsType":"主键","enName":"categ_name_l1","desc":"这个字段表示\\"一级品类\\"，它的枚举值有\\"ALL\\""},{"dataType":"VARCHAR","cnName":"二级品类编码","semanticsType":"主键","enName":"categ_code_l2","desc":"二级品类编码"},{"dataType":"VARCHAR","cnName":"一级品类","semanticsType":"主键","enName":"categ_name_l2","desc":"这个字段表示\\"一级品类\\"，它的枚举值有雷鸟科技、泛智屏、CIoT、光伏、MP、SCD、SMD、商用、冰洗、空调。"},{"dataType":"VARCHAR","cnName":"三级品类编码","semanticsType":"主键","enName":"categ_code_l3","desc":"三级品类编码"},{"dataType":"VARCHAR","cnName":"二级品类","semanticsType":"主键","enName":"categ_name_l3","desc":"这个字段表示\\"二级品类\\"，它的枚举值有洗衣机、IoT、信息发布、智能门锁、电视、专业显示、会议显示、教育触控、显示器、家用、小家电、系统集成、其他空调、家中、冰箱、音频产品。"},{"dataType":"VARCHAR","cnName":"品牌层级","semanticsType":"主键","enName":"brand_level","desc":"品牌层级"},{"dataType":"VARCHAR","cnName":"一级品牌编码","semanticsType":"主键","enName":"brand_code_l1","desc":"一级品牌编码"},{"dataType":"VARCHAR","cnName":"一级品牌名称","semanticsType":"主键","enName":"brand_name_l1","desc":"一级品牌名称"},{"dataType":"VARCHAR","cnName":"二级品牌编码","semanticsType":"主键","enName":"brand_code_l2","desc":"二级品牌编码"},{"dataType":"VARCHAR","cnName":"品牌名称","semanticsType":"主键","enName":"brand_name_l2","desc":"这个字段表示\\"品牌\\"，它的枚举值有ROWA、TCL、FFALCON、其他。"},{"dataType":"VARCHAR","cnName":"机构层级","semanticsType":"主键","enName":"org_level","desc":"机构层级"},{"dataType":"VARCHAR","cnName":"一级机构编码","semanticsType":"主键","enName":"org_code_l1","desc":"一级机构编码"},{"dataType":"VARCHAR","cnName":"一级机构名称","semanticsType":"主键","enName":"org_name_l1","desc":"一级机构名称"},{"dataType":"VARCHAR","cnName":"二级机构编码","semanticsType":"主键","enName":"org_code_l2","desc":"二级机构编码"},{"dataType":"VARCHAR","cnName":"二级机构名称","semanticsType":"主键","enName":"org_name_l2","desc":"这个字段表示\\"二级机构\\"，或者“大区”或者叫“业务单元”，它的枚举值有工程机业务部、南美分公司、非洲分公司、ROWA物料机构、华北大区、战略ODM、NFF总部、全国连锁业务部、ROWA电子商务、其他分公司、空调工程业务部、东欧分公司、冰洗业务部、NFF哈尔滨分支机构、南亚分公司、NFF南京分支机构、NFF南宁分支机构、印尼分公司、中欧分公司、空调电商业务中心、A项目、东南亚分公司、海外战略OEM部、NFF福州分支机构、NFF成都分支机构、智能家居业务部、NFF武汉分支机构、常规OEM、NFF深圳十分到家服务科技有限公司、常规ODM、NFF北京分支机构、商用业务部、NFF贵阳分支机构、泛家居行业客户部、北美分公司、水机业务、中东分公司、拉美分公司、西欧分公司、菲律宾、智慧厨电业务部、印度尼西亚、ROWA大客户部、NFF广州分支机构、Russia、东南亚、中亚、南高加索南太平洋、西北大区、台湾、西北亚、越南、空调总部直营业务部、印度、泰国、NFF昆明分支机构、T销客业务部、商显业务部、韩国、CIoT业务部、APHQ、澳洲、乐华营销中心、SRHQ、马来西亚、香港、南亚、TCL智屏业务部、日本、新西兰、空调连锁业务部、印太岛国、德国、NFF上海分支机构、荷兰、乌克兰&白俄罗斯、南欧、波兰、泛家居行业客户部、英国、自营大官旗业务部、EUHQ、国内营销中心、瑞典、法国、西班牙、意大利、中欧、秘"},{"dataType":"VARCHAR","cnName":"三级机构编码","semanticsType":"主键","enName":"org_code_l3","desc":"三级机构编码"},{"dataType":"VARCHAR","cnName":"三级机构名称","semanticsType":"主键","enName":"org_name_l3","desc":"这个字段表示\\"三级机构\\"，它的枚举值有北京战区、西亚片区、以色列片区、战略一片区、西北非片区、健康电器-欧美区、广州战区、其他客户、其他片区、墨西哥片区、南昌战区、科威特片区、北京战区、杭州战区、中欧片区、大哥伦比亚片区、深圳战区、北美专业渠道片区、天津战区、石家庄战区、海外战略OEM部（科室）、巴西片区、武汉战区、内蒙战区、沈阳战区、内蒙战区、南昌战区、国美智能、中北欧片区、西北欧片区、哈尔滨战区、西南非片区、两伊片区、健康电器-多元化区、巴尔干片区、武汉战区、俄罗斯片区、伊莱克斯、西安战区、美加片区、新疆战区、印尼片区、志高、合肥战区、南宁战区、福州战区、印巴片区、长春战区、云米、中澳片区、兰州战区、杭州战区、济南战区、巴拿马片区、广州战区、南京战区、青岛战区、哈尔滨战区、A项目、合肥战区、港澳台菲片区、阿根廷片区、伊拉克片区、意大利片区、济南战区、东北非片区、印越片区、长沙战区、阿联酋片区、沙特片区、新马泰片区、昆明战区、战略二片区、健康电器-中东非洲区、战略三片区、西班牙片区、健康电器-亚洲区、郑州战区、上海战区、天津战区、上海战区、福州战区、新疆战区、深圳战区、长春战区、贵阳战区、兰州战区、太原战区、空调泛家居行业客户部、成都战区、空调智能家居业务部、重庆战区、南京战区、贵阳战区、南宁战区、长沙战区、煤改电一部、昆明战区、华南大区、电商业务部、华北大区、行业拓展部、热泵业"},{"dataType":"VARCHAR","cnName":"渠道层级","semanticsType":"主键","enName":"channel_level","desc":"渠道层级"},{"dataType":"VARCHAR","cnName":"一级渠道编码","semanticsType":"主键","enName":"channel_code_l1","desc":"一级渠道编码"},{"dataType":"VARCHAR","cnName":"一级渠道名称","semanticsType":"主键","enName":"channel_name_l1","desc":"一级渠道名称"},{"dataType":"VARCHAR","cnName":"二级渠道编码","semanticsType":"主键","enName":"channel_code_l2","desc":"二级渠道编码"},{"dataType":"VARCHAR","cnName":"一级渠道","semanticsType":"主键","enName":"channel_name_l2","desc":"这个字段表示\\"一级渠道\\"，它的枚举值有创新&O2O、商显渠道、电商、ISBC、KA。"},{"dataType":"VARCHAR","cnName":"三级渠道编码","semanticsType":"主键","enName":"channel_code_l3","desc":"三级渠道编码"},{"dataType":"VARCHAR","cnName":"二级渠道","semanticsType":"主键","enName":"channel_name_l3","desc":"这个字段表示\\"二级渠道\\"，它的枚举值有电商、O2O、传统、区域连锁、连锁(二级)、B2B、创新、商显渠道(二级)、跨境电商、T销客-传统、家用内销总部直营。"},{"dataType":"VARCHAR","cnName":"四级渠道编码","semanticsType":"主键","enName":"channel_code_l4","desc":"四级渠道编码"},{"dataType":"VARCHAR","cnName":"三级渠道","semanticsType":"主键","enName":"channel_name_l4","desc":"这个字段表示\\"三级渠道\\"，它的枚举值有自控、京东专卖店、教育触控渠道、苏宁、会议显示渠道、专业显示渠道、信息发布渠道、区域连锁(三级)、快手、新渠道、礼品渠道、传统代理、电商连锁（停用）、工程机、直供、电商综合（停用）、企业会员商城、TCL之家、淘系、O2O渠道（停用）、京东、代理（囤货）商、天猫优品、甲方户、C网（停用）、商超、泛家居门店、电商分销商、定制客户、其他、抖音、代理、智能家居、家用内销总部直营、系统集成、专卖店、工程直营商、国美、传统直供、连锁(三级)、五星、拼多多。"},{"dataType":"DECIMAL","cnName":"BP口径收入","semanticsType":"主键","enName":"bp_sales_amt","desc":"BP口径收入"},{"dataType":"DECIMAL","cnName":"收入","semanticsType":"主键","enName":"sales_amt","desc":"收入，营收，净收入"},{"dataType":"DECIMAL","cnName":"月度收入BP","semanticsType":"主键","enName":"bp_amt_m","desc":"月度收入BP"},{"dataType":"DECIMAL","cnName":"BP口径销量","semanticsType":"主键","enName":"bp_sales_qty","desc":"BP口径销量"},{"dataType":"DECIMAL","cnName":"销量","semanticsType":"主键","enName":"sales_qty","desc":"销售量"},{"dataType":"DECIMAL","cnName":"月度销量BP","semanticsType":"主键","enName":"bp_qty_m","desc":"月度销量BP"},{"dataType":"VARCHAR","cnName":"区域","semanticsType":"主键","enName":"bg_name","desc":"这个字段表示“区域”或者\\"品牌业务\\"，它的枚举值有实业、NABG、LABG、EUBG、APBG、MEABG、特种空调、商用空调、空调ODM等等，如果字段表示品牌业务时候，那它的查询条件就是 区域 in (\'CBG\'、\'NABG\'、\'EUBG\'、\'APBG\'、\'MEABG\'、\'LABG\')"},{"dataType":"VARCHAR","cnName":"数仓更新时间","semanticsType":"主键","enName":"au_last_update_time","desc":"数仓更新时间"},{"dataType":"VARCHAR","cnName":"BG分区","semanticsType":"主键","enName":"bg_code","desc":"BG分区"},{"dataType":"VARCHAR","cnName":"月分区字段","semanticsType":"主键","enName":"part_m","desc":"月分区字段"}]\r\n\r\n以下是用户的问题：\r\n今年2月份各区域取值包括(\'CBG\',\'NABG\',\'EUBG\',\'APBG\',\'MEABG\',\'LABG\',\'EMEA\',\'LA&AP\')收入分别同比增长情况\r\n\r\n以下是指标表字段定义: \r\n\n [ 中文名称： 收入, 字段名： sales_amt, 同义词： 销售额;累计收入;合计收入;累计营业额;总销售额;总营业额;营业额;总收入;月结收入;营业收入;销售收入;净收入;年累收入;表现如何;销售额情况;收入占比, 指标计算公式： sales_amt, 过滤条件(过滤出复合条件的数据用于指标计算公式)： null,  描述 ：收入 ] \n, \n [ 中文名称： 收入达成率, 字段名： inc_ach_rate, 同义词： BP收入达成率;预算收入达成率;收入达成;收入目标达成;收入BP达成;符合预期, 指标计算公式： sum(sales_amt)/sum(bp_amt_m), 过滤条件(过滤出复合条件的数据用于指标计算公式)： null,  描述 ：收入/BP收入 ] \n, \n [ 中文名称： 销量, 字段名： sales_qty, 同义词： 累计销量;合计销量;总销量;总销售量;月结销量;月结台数;总台数;台数;卖了多少台;卖了多少;卖的台数;零售台数, 指标计算公式： sales_qty, 过滤条件(过滤出复合条件的数据用于指标计算公式)： null,  描述 ：销量 ] \n, \n [ 中文名称： 销量达成率, 字段名： Sale_ach_rate, 同义词： 销量达成率;销量达成;销量目标达成;销量BP达成;销量BP达成率, 指标计算公式： SUM(sales_qty)/SUM(bp_qty_m), 过滤条件(过滤出复合条件的数据用于指标计算公式)： null,  描述 ：销量/目标销量 ] \n, \n [ 中文名称： 目标收入, 字段名： income_bp, 同义词： 收入bp值;目标值, 指标计算公式： bp_amt_m, 过滤条件(过滤出复合条件的数据用于指标计算公式)： null,  描述 ：收入的BP预算值 ] \n, \n [ 中文名称： 目标销售, 字段名： qty_BP, 同义词： 销量BP值, 指标计算公式： bp_qty_m, 过滤条件(过滤出复合条件的数据用于指标计算公式)： null,  描述 ：销量的BP值 ] \n, \n [ 中文名称： 当期销量, 字段名： dq_sales_qty, 同义词： null, 指标计算公式： sales_qty, 过滤条件(过滤出复合条件的数据用于指标计算公式)： null,  描述 ：当期销量 ] \n, \n [ 中文名称： 同期销量, 字段名： tq_sales_qty, 同义词： null, 指标计算公式： sales_qty\t, 过滤条件(过滤出复合条件的数据用于指标计算公式)： null,  描述 ：同期销量 ] \n, \n [ 中文名称： 收入目标偏差, 字段名： income_target_Deviation, 同义词： 收入偏差, 指标计算公式： sum(sales_amt)/sum(bp_amt_m), 过滤条件(过滤出复合条件的数据用于指标计算公式)： null,  描述 ：实际收入-目标收入 ] \n, \n [ 中文名称： 销量目标偏差, 字段名： sales_target_Deviation, 同义词： null, 指标计算公式： sum(sales_qty)/sum(bp_qty_m), 过滤条件(过滤出复合条件的数据用于指标计算公式)： null,  描述 ：实际销量-目标销量 ] \n, \n [ 中文名称： 收入同比, 字段名： year_revenue, 同义词： 同比收入, 指标计算公式： sales_amt, 过滤条件(过滤出复合条件的数据用于指标计算公式)： null,  描述 ：（本期收入-上期收入）/上期收入 ] \n, \n [ 中文名称： 销量同比, 字段名： year_sales, 同义词： 销量同比增长, 指标计算公式： sales_qty, 过滤条件(过滤出复合条件的数据用于指标计算公式)： null,  描述 ：（本期销量-上期销量）/上期销量 ] \n, \n [ 中文名称： 同期收入, 字段名： tq_income, 同义词： null, 指标计算公式： sales_amt, 过滤条件(过滤出复合条件的数据用于指标计算公式)： null,  描述 ：同期收入 ] \n, \n [ 中文名称： 当期收入, 字段名： current_income, 同义词： null, 指标计算公式： sales_amt, 过滤条件(过滤出复合条件的数据用于指标计算公式)： null,  描述 ：当期收入 ] \n, \n [ 中文名称： 收入BP, 字段名： income_BP, 同义词： null, 指标计算公式： bp_amt_m, 过滤条件(过滤出复合条件的数据用于指标计算公式)： null,  描述 ：预算收入 ] \n, \n [ 中文名称： 销量BP, 字段名： sales_BP, 同义词： 月度销量BP, 指标计算公式： bp_qty_m, 过滤条件(过滤出复合条件的数据用于指标计算公式)： null,  描述 ：预算销量 ] \n\r\n\r\n以下是任务描述:\r\n  任务1、请结合表字段定义以及指标表字段定义找出[用户的问题]涉及可能用到的[字段的声明]。\r\n输出要求：\r\n要求1、保留与用户问题相关[字段的声明]并返回；\r\n要求2、与用户问题毫不相关的[字段的声明]， 不要返回给我，直接移除；\r\n要求3、如果[字段的声明]与统计时间相关就无需移除；\r\n要求4、只需要输出[字段的声明]中的"enName"对应的值；\r\n\r\n输出格式样例：\r\n[\r\n"[字段的声明]enName对应的值","[字段的声明]enName对应的值"\r\n]\r\n\r\n输出约束：\r\n  不要输出其他额外的解释信息。',
      },
      {
        answer:
          "```sql\nselect\n'2025-02' as 日期,\nbg_name as 区域,\nsum(case when period_date between '2025-02-01' and '2025-02-28' then sales_amt else 0 end) as 当期收入,\nsum(case when period_date between '2024-02-01' and '2024-02-28' then sales_amt else 0 end) as 同期收入,\nsum(case when period_date between '2025-02-01' and '2025-02-28' then sales_amt else 0 end) / \nsum(case when period_date between '2024-02-01' and '2024-02-28' then sales_amt else 0 end) - 1 as 收入同比增长\nfrom ads_sdbg_sl_chatbi_stat_index_gs_m\nwhere categ_level = '0'\nand brand_level = '0'\nand org_level = '0'\nand channel_level = '0'\nand bg_name in ('CBG', 'NABG', 'EUBG', 'APBG', 'MEABG', 'LABG', 'EMEA', 'LA&AP')\ngroup by '2025-02', bg_name;\n```",
        step: "MapperSemanticSqlWithLLM",
        prompt:
          '当前时间是:2025-05-09 16:30:01\r\n今年是: 2025年\r\n去年是: 2024年\r\n前年是: 2023年\r\n当前月是: 2025年05月\r\n当前季度是: 2025 年第2季度\r\n\r\n以下是[表信息定义]：\r\n\n [ 数据表的中文名称： CHATBI全球营销指标GS统计表 数据表的英文名称： ads_sdbg_sl_chatbi_stat_index_gs_m 数据表的描述： null  数据表的 table_name ：ads_sdbg_sl_chatbi_stat_index_gs_m ] \n\r\n\r\n以下表【ads_sdbg_sl_chatbi_stat_index_gs_m】对应[字段声明]：\r\n["{\\"dataType\\":\\"DATE\\",\\"cnName\\":\\"日期\\",\\"semanticsType\\":\\"主键\\",\\"enName\\":\\"period_date\\",\\"desc\\":\\"这个字段表示格式为年月(yyyy-mm-dd)的日期字段，表示该行记录统计时间所在的年份和月份。如果需要按月统计，可以使用这个字段\\"}","{\\"dataType\\":\\"VARCHAR\\",\\"cnName\\":\\"品类层级\\",\\"semanticsType\\":\\"主键\\",\\"enName\\":\\"categ_level\\",\\"desc\\":\\"品类层级\\"}","{\\"dataType\\":\\"VARCHAR\\",\\"cnName\\":\\"品牌层级\\",\\"semanticsType\\":\\"主键\\",\\"enName\\":\\"brand_level\\",\\"desc\\":\\"品牌层级\\"}","{\\"dataType\\":\\"VARCHAR\\",\\"cnName\\":\\"机构层级\\",\\"semanticsType\\":\\"主键\\",\\"enName\\":\\"org_level\\",\\"desc\\":\\"机构层级\\"}","{\\"dataType\\":\\"VARCHAR\\",\\"cnName\\":\\"渠道层级\\",\\"semanticsType\\":\\"主键\\",\\"enName\\":\\"channel_level\\",\\"desc\\":\\"渠道层级\\"}","{\\"dataType\\":\\"DECIMAL\\",\\"cnName\\":\\"收入\\",\\"semanticsType\\":\\"主键\\",\\"enName\\":\\"sales_amt\\",\\"desc\\":\\"收入，营收，净收入\\"}","{\\"dataType\\":\\"VARCHAR\\",\\"cnName\\":\\"区域\\",\\"semanticsType\\":\\"主键\\",\\"enName\\":\\"bg_name\\",\\"desc\\":\\"这个字段表示“区域”或者\\\\\\"品牌业务\\\\\\"，它的枚举值有实业、NABG、LABG、EUBG、APBG、MEABG、特种空调、商用空调、空调ODM等等，如果字段表示品牌业务时候，那它的查询条件就是 区域 in (\'CBG\'、\'NABG\'、\'EUBG\'、\'APBG\'、\'MEABG\'、\'LABG\')\\"}"]\r\n\r\n以下是指标表字段的定义:\r\n\n [ 中文名称： 收入, 字段名： sales_amt, 同义词： 销售额;累计收入;合计收入;累计营业额;总销售额;总营业额;营业额;总收入;月结收入;营业收入;销售收入;净收入;年累收入;表现如何;销售额情况;收入占比, 指标计算公式： sales_amt, 过滤条件(过滤出复合条件的数据用于指标计算公式)： null,  描述 ：收入 ] \n, \n [ 中文名称： 收入达成率, 字段名： inc_ach_rate, 同义词： BP收入达成率;预算收入达成率;收入达成;收入目标达成;收入BP达成;符合预期, 指标计算公式： sum(sales_amt)/sum(bp_amt_m), 过滤条件(过滤出复合条件的数据用于指标计算公式)： null,  描述 ：收入/BP收入 ] \n, \n [ 中文名称： 销量, 字段名： sales_qty, 同义词： 累计销量;合计销量;总销量;总销售量;月结销量;月结台数;总台数;台数;卖了多少台;卖了多少;卖的台数;零售台数, 指标计算公式： sales_qty, 过滤条件(过滤出复合条件的数据用于指标计算公式)： null,  描述 ：销量 ] \n, \n [ 中文名称： 销量达成率, 字段名： Sale_ach_rate, 同义词： 销量达成率;销量达成;销量目标达成;销量BP达成;销量BP达成率, 指标计算公式： SUM(sales_qty)/SUM(bp_qty_m), 过滤条件(过滤出复合条件的数据用于指标计算公式)： null,  描述 ：销量/目标销量 ] \n, \n [ 中文名称： 目标收入, 字段名： income_bp, 同义词： 收入bp值;目标值, 指标计算公式： bp_amt_m, 过滤条件(过滤出复合条件的数据用于指标计算公式)： null,  描述 ：收入的BP预算值 ] \n, \n [ 中文名称： 目标销售, 字段名： qty_BP, 同义词： 销量BP值, 指标计算公式： bp_qty_m, 过滤条件(过滤出复合条件的数据用于指标计算公式)： null,  描述 ：销量的BP值 ] \n, \n [ 中文名称： 当期销量, 字段名： dq_sales_qty, 同义词： null, 指标计算公式： sales_qty, 过滤条件(过滤出复合条件的数据用于指标计算公式)： null,  描述 ：当期销量 ] \n, \n [ 中文名称： 同期销量, 字段名： tq_sales_qty, 同义词： null, 指标计算公式： sales_qty\t, 过滤条件(过滤出复合条件的数据用于指标计算公式)： null,  描述 ：同期销量 ] \n, \n [ 中文名称： 收入目标偏差, 字段名： income_target_Deviation, 同义词： 收入偏差, 指标计算公式： sum(sales_amt)/sum(bp_amt_m), 过滤条件(过滤出复合条件的数据用于指标计算公式)： null,  描述 ：实际收入-目标收入 ] \n, \n [ 中文名称： 销量目标偏差, 字段名： sales_target_Deviation, 同义词： null, 指标计算公式： sum(sales_qty)/sum(bp_qty_m), 过滤条件(过滤出复合条件的数据用于指标计算公式)： null,  描述 ：实际销量-目标销量 ] \n, \n [ 中文名称： 收入同比, 字段名： year_revenue, 同义词： 同比收入, 指标计算公式： sales_amt, 过滤条件(过滤出复合条件的数据用于指标计算公式)： null,  描述 ：（本期收入-上期收入）/上期收入 ] \n, \n [ 中文名称： 销量同比, 字段名： year_sales, 同义词： 销量同比增长, 指标计算公式： sales_qty, 过滤条件(过滤出复合条件的数据用于指标计算公式)： null,  描述 ：（本期销量-上期销量）/上期销量 ] \n, \n [ 中文名称： 同期收入, 字段名： tq_income, 同义词： null, 指标计算公式： sales_amt, 过滤条件(过滤出复合条件的数据用于指标计算公式)： null,  描述 ：同期收入 ] \n, \n [ 中文名称： 当期收入, 字段名： current_income, 同义词： null, 指标计算公式： sales_amt, 过滤条件(过滤出复合条件的数据用于指标计算公式)： null,  描述 ：当期收入 ] \n, \n [ 中文名称： 收入BP, 字段名： income_BP, 同义词： null, 指标计算公式： bp_amt_m, 过滤条件(过滤出复合条件的数据用于指标计算公式)： null,  描述 ：预算收入 ] \n, \n [ 中文名称： 销量BP, 字段名： sales_BP, 同义词： 月度销量BP, 指标计算公式： bp_qty_m, 过滤条件(过滤出复合条件的数据用于指标计算公式)： null,  描述 ：预算销量 ] \n\r\n\r\n以下是用户的问题:\r\n今年2月份各区域取值包括(\'CBG\',\'NABG\',\'EUBG\',\'APBG\',\'MEABG\',\'LABG\',\'EMEA\',\'LA&AP\')收入分别同比增长情况\r\n\r\n以下是用户给的条件数据：\r\n{{userNewConditions}}\r\n\r\n任务：\r\n以下是任务描述: \r\n1.参考伪SQL生成示例编写伪 SQL 。\r\n\r\n任务输出要求：\r\n  1. 参考伪SQL生成示例生成能够满足【用户问题】以及【用户给的条件数据】的伪SQL；\r\n  2. 如果伪SQL生成示例中存在group by，必须保留group by;\r\n  3. 伪 SQL 出现的表名和字段名和别名采用中文名称 cnName； \r\n  4. 伪 SQL 中涉及到的聚合函数计算需要结合指标里的description字段进行匹配，如果命中，请在伪SQL语句的WHERE过滤条件里添加命中数据的fiterRule字段的内容；\r\n  5. 伪 SQL 必须符合 MySQL 的语法规范，请仔细检查！\r\n  6. 伪 SQL 返回的 column 排序，尽量确保语序\r\n  7. 伪 SQL中出现字段必须在ads_sdbg_sl_chatbi_stat_index_gs_m表中存在，请仔细检查；\r\n  8. 伪 SQL 参考生成示例，请勿捏造\r\n  9.  categ_level、brand_level、org_level、channel_level 四个字段必须都要放在Where条件后面;\r\n 10. categ_level、brand_level、org_level、channel_level 四个字段的值必须以【用户给的条件数据】中的数据为准\'；\r\n\r\n\r\n以下编写伪SQL的注意点：\r\n  1. 当用户的问题出现"各"、"每"、"分别"、"趋势"、"分布"、"各个"、"各大"等字眼的时候,需要按理解进行维度分组。如果没有分组的意图，伪SQL不需要分组操作；\r\n  2. 当用户的问题包含"最近一个季度"等相近的关键词的时候，代表用户想查当前月所属季度的数据(备注:当前月=2025年05月)\r\n  3.当用户的问题包含"上一个季度，前一个季度"等相近的关键词的时候，代表用户想查基于当前季度的前一个季度数据(备注:当前季度是:年第季度)\r\n  4. 当用户的问题包含类似于“最近三个月，最近6个月”等关键词的时候，计算时间的时候，不得包含当前月(备注:当前月=2025年05月)\r\n  5. 当用户的问题包含"上半年、下半年"等字眼的时候, 如果用户问题中没有包含具体年份（如上半年），那就以【2025年的上半年】、【2025年的下半年】来计算时间（如2025年的上半年、2025年的下半年）\r\n  6. 当用户的问题包含"上上个月"等字眼的时候，代表用户想查2025年03月的数据\r\n  7. 当用户的问题包含"年累"字段，从该年1月份开始取数计算总值\r\n  8. 生成的SQL查询中不要包含除以10000的操作\r\n  9. 不要生成SQL注释\r\n\r\n特别注意：\r\n如果伪SQL出现中文的符号全部替换为对应的英文符号（如:中文单引号(’)替换成英文的单引号(\')、中文双引号(“”)替换成英文的单双号(")、中文逗号(，)替换成英文逗号等符号(,)）\r\n\r\n以下是伪SQL生成示例:\r\n{{sqlTemplate}}\r\n\r\n输出约束:\r\n不要输出其他额外的解释信息',
      },
      {
        answer:
          "  SELECT      '2025-02' AS 日期,     bg_name AS 区域,     SUM(CASE WHEN period_date BETWEEN '2025-02-01' AND '2025-02-28' THEN sales_amt ELSE 0 END) AS 当期收入,     SUM(CASE WHEN period_date BETWEEN '2024-02-01' AND '2024-02-28' THEN sales_amt ELSE 0 END) AS 同期收入,     SUM(CASE WHEN period_date BETWEEN '2025-02-01' AND '2025-02-28' THEN sales_amt ELSE 0 END) /      SUM(CASE WHEN period_date BETWEEN '2024-02-01' AND '2024-02-28' THEN sales_amt ELSE 0 END) - 1 AS 收入同比增长 FROM      ads_sdbg_sl_chatbi_stat_index_gs_m WHERE      categ_level='0'     AND brand_level='0'     AND org_level='0'     AND channel_level='0'     AND bg_name IN ('CBG', 'NABG', 'EUBG', 'APBG', 'MEABG', 'LABG', 'EMEA', 'LA&AP') GROUP BY      bg_name    limit 1000;",
        step: "ConvertRealSqlLLM",
        prompt:
          '当前时间是:2025-05-09 16:30:07,所有的时间推理以系统当前时间为基点计算时间\r\n今年是: 2025年\r\n去年是: 2024年\r\n前年是: 2023年\r\n当前月是: 2025年05月\r\n当前季度是: 2025 年第0季度\r\n\r\n以下是表名【ads_sdbg_sl_chatbi_stat_index_gs_m】和【表信息定义】：\r\n\n [ 数据表的中文名称： CHATBI全球营销指标GS统计表 数据表的英文名称： ads_sdbg_sl_chatbi_stat_index_gs_m 数据表的描述： null  数据表的 table_name ：ads_sdbg_sl_chatbi_stat_index_gs_m ] \n\r\n\r\n以下表【ads_sdbg_sl_chatbi_stat_index_gs_m】对应[字段的声明]：\r\n["{\\"dataType\\":\\"DATE\\",\\"cnName\\":\\"日期\\",\\"semanticsType\\":\\"主键\\",\\"enName\\":\\"period_date\\",\\"desc\\":\\"这个字段表示格式为年月(yyyy-mm-dd)的日期字段，表示该行记录统计时间所在的年份和月份。如果需要按月统计，可以使用这个字段\\"}","{\\"dataType\\":\\"VARCHAR\\",\\"cnName\\":\\"品类层级\\",\\"semanticsType\\":\\"主键\\",\\"enName\\":\\"categ_level\\",\\"desc\\":\\"品类层级\\"}","{\\"dataType\\":\\"VARCHAR\\",\\"cnName\\":\\"品牌层级\\",\\"semanticsType\\":\\"主键\\",\\"enName\\":\\"brand_level\\",\\"desc\\":\\"品牌层级\\"}","{\\"dataType\\":\\"VARCHAR\\",\\"cnName\\":\\"机构层级\\",\\"semanticsType\\":\\"主键\\",\\"enName\\":\\"org_level\\",\\"desc\\":\\"机构层级\\"}","{\\"dataType\\":\\"VARCHAR\\",\\"cnName\\":\\"渠道层级\\",\\"semanticsType\\":\\"主键\\",\\"enName\\":\\"channel_level\\",\\"desc\\":\\"渠道层级\\"}","{\\"dataType\\":\\"DECIMAL\\",\\"cnName\\":\\"收入\\",\\"semanticsType\\":\\"主键\\",\\"enName\\":\\"sales_amt\\",\\"desc\\":\\"收入，营收，净收入\\"}","{\\"dataType\\":\\"VARCHAR\\",\\"cnName\\":\\"区域\\",\\"semanticsType\\":\\"主键\\",\\"enName\\":\\"bg_name\\",\\"desc\\":\\"这个字段表示“区域”或者\\\\\\"品牌业务\\\\\\"，它的枚举值有实业、NABG、LABG、EUBG、APBG、MEABG、特种空调、商用空调、空调ODM等等，如果字段表示品牌业务时候，那它的查询条件就是 区域 in (\'CBG\'、\'NABG\'、\'EUBG\'、\'APBG\'、\'MEABG\'、\'LABG\')\\"}"]\r\n\r\n以下是用户给的条件数据：\r\n{{userNewConditions}}\r\n\r\n这是[伪SQL] :\r\n```sql\nselect\n\'2025-02\' as 日期,\nbg_name as 区域,\nsum(case when period_date between \'2025-02-01\' and \'2025-02-28\' then sales_amt else 0 end) as 当期收入,\nsum(case when period_date between \'2024-02-01\' and \'2024-02-28\' then sales_amt else 0 end) as 同期收入,\nsum(case when period_date between \'2025-02-01\' and \'2025-02-28\' then sales_amt else 0 end) / \nsum(case when period_date between \'2024-02-01\' and \'2024-02-28\' then sales_amt else 0 end) - 1 as 收入同比增长\nfrom ads_sdbg_sl_chatbi_stat_index_gs_m\nwhere categ_level = \'0\'\nand brand_level = \'0\'\nand org_level = \'0\'\nand channel_level = \'0\'\nand bg_name in (\'CBG\', \'NABG\', \'EUBG\', \'APBG\', \'MEABG\', \'LABG\', \'EMEA\', \'LA&AP\')\ngroup by \'2025-02\', bg_name;\n```\r\n\r\n任务：\r\n  以下是任务描述:\r\n   1. 将 [伪SQL] 结合【用户给的条件数据】转化为 符合 MySQL 规范标准的可执行 SQL 。\r\n  以下是SQL转化要求：\r\n   1.根据 [表定义] 和 [字段的声明] 替换伪 SQL 出现的表名和字段名为真实的物理表名和物理字段名，确保其可能被 MySQL 解析;\r\n   2.必须符合 MySQL 的语法规范;\r\n   3.转化的真实SQL参考伪SQL，请勿捏造;\r\n   4. 要求不存在于[字段的声明]的 column 名称尽可能的采用中文表述;\r\n   5. categ_level、brand_level、org_level、channel_level 四个字段必须都要放在Where条件后面;\r\n   6. categ_level、brand_level、org_level、channel_level 四个字段的值必须以【用户给的条件数据】中的数据为准\'；\r\n\r\n  以下是SQL转化的注意的点：\r\n   1.一定不能生成SQL注释\r\n\r\n输出约束：\r\n   1.直接返回SQL 语句\r\n   2.不要输出其他额外的解释信息',
      },
    ],
    tableVo: {
      columns: [
        {
          isMetric: 0,
          isAttribution: 0,
          title: "日期",
          dataIndex: "日期",
          keyEn: "period_date",
          key: "日期",
        },
        {
          isMetric: 2,
          isAttribution: 0,
          title: "收入同比增长",
          dataIndex: "收入同比增长",
          key: "收入同比增长",
        },
        {
          isMetric: 1,
          typeParams: "sales_amt",
          isAttribution: 0,
          title: "同期收入(万元)",
          dataIndex: "同期收入(万元)",
          keyEn: "tq_income",
          key: "同期收入(万元)",
        },
        {
          isMetric: 1,
          typeParams: "sales_amt",
          isAttribution: 0,
          title: "当期收入(万元)",
          dataIndex: "当期收入(万元)",
          keyEn: "current_income",
          key: "当期收入(万元)",
        },
        {
          isMetric: 0,
          isAttribution: 0,
          title: "区域",
          dataIndex: "区域",
          keyEn: "bg_name",
          key: "区域",
        },
      ],
      dataSource: [
        {
          收入同比增长: "0.4",
          区域: "LABG",
          日期: "2025-02",
          "同期收入(万元)": "53,523.7",
          "当期收入(万元)": "74,229.8",
        },
        {
          收入同比增长: "0.3",
          区域: "MEABG",
          日期: "2025-02",
          "同期收入(万元)": "35,452.8",
          "当期收入(万元)": "44,947.2",
        },
        {
          收入同比增长: "0.2",
          区域: "CBG",
          日期: "2025-02",
          "同期收入(万元)": "119,588",
          "当期收入(万元)": "141,318.2",
        },
        {
          收入同比增长: "0.3",
          区域: "NABG",
          日期: "2025-02",
          "同期收入(万元)": "84,226.7",
          "当期收入(万元)": "106,551.3",
        },
        {
          收入同比增长: "0.2",
          区域: "APBG",
          日期: "2025-02",
          "同期收入(万元)": "62,187.2",
          "当期收入(万元)": "77,443",
        },
        {
          收入同比增长: "0.1",
          区域: "EUBG",
          日期: "2025-02",
          "同期收入(万元)": "43,457.6",
          "当期收入(万元)": "48,159",
        },
      ],
    },
    realSql:
      "  SELECT      '2025-02' AS 日期,     bg_name AS 区域,     SUM(CASE WHEN period_date BETWEEN '2025-02-01' AND '2025-02-28' THEN sales_amt ELSE 0 END) AS 当期收入,     SUM(CASE WHEN period_date BETWEEN '2024-02-01' AND '2024-02-28' THEN sales_amt ELSE 0 END) AS 同期收入,     SUM(CASE WHEN period_date BETWEEN '2025-02-01' AND '2025-02-28' THEN sales_amt ELSE 0 END) /      SUM(CASE WHEN period_date BETWEEN '2024-02-01' AND '2024-02-28' THEN sales_amt ELSE 0 END) - 1 AS 收入同比增长 FROM      ads_sdbg_sl_chatbi_stat_index_gs_m WHERE      categ_level='0'     AND brand_level='0'     AND org_level='0'     AND channel_level='0'     AND bg_name IN ('CBG', 'NABG', 'EUBG', 'APBG', 'MEABG', 'LABG', 'EMEA', 'LA&AP') GROUP BY      bg_name    limit 1000;",
    emitter: {
      timeout: 120000,
    },
    desc: "用户意图是了解今年2月份各区域收入（CBG、NABG等）同比增长的具体情况。",
    chatType: ["table"],
    analysisReportData:
      "收入同比增长趋势：\n- LABG：同比增长40%\n- MEABG：同比增长30%\n- CBG：同比增长20%\n- NABG：同比增长20%\n- APBG：同比增长20%\n- EUBG：同比增长10%\n\n总体趋势：所有区域的收入均呈现增长态势，其中LABG和MEABG的增长率较高，分别为40%和30%，而EUBG的增长率为最低，为10%。",
  },
  success: true,
  message: "xiache",
};

export const attributionTextMap = {
  AttributionYoySqlProxyImpl: {
    title: "同比归因", //
    curTitle: "本期", //
    comparisonTitle: "同期", //
    comparisonName: "同比", //
  },
  AttributionReachSqlProxyImpl: {
    title: "达成归因",
    curTitle: "实际",
    comparisonTitle: "目标",
    comparisonName: "达成",
  },
};
export const analysisMock = [
  {
    key: "AttributionYoySqlProxyImpl",
    curValue: "1351998.0407(万元)",
    curTime: "2025年11月至2026年12月",
    comparisonValue: "13519889.0407(万元)",
    comparisonTime: "2025年11月至2026年12月",
    comparisonPercent: "+6.4%",
    chartDataList: [
      {
        tabName: "国家",
        tab: "country",
        lineChart: [
          {
            name: "TK1J2",
            value: "194.9%",
            curValue: 789,
          },
          {
            name: "C9W",
            value: "0%",
            curValue: 333,
          },
          {
            name: "OutsourcedS",
            value: "964.3%",
            curValue: 111,
          },
        ],
      },
      {
        tabName: "型号",
        tab: "model",
        lineChart: mock.data.originalData,
      },
      {
        tabName: "业务单元222",
        tab: "businessUnit222",
        lineChart: [],
      },
      {
        tabName: "国家222",
        tab: "country",
        lineChart: mock.data.originalData2,
      },
    ],
  },
  {
    key: "AttributionReachSqlProxyImpl",
    curValue: "104.3W",
    curTime: "2024年",
    comparisonValue: "98.3W",
    comparisonTime: "2023年",
    comparisonPercent: "106.1%",
    chartDataList: [
      {
        tabName: "产品线",
        tab: "productLine",
        lineChart: mock.data.originalData2,
      },
      {
        tabName: "业务单元",
        tab: "businessUnit",
        lineChart: mock.data.originalData,
      },
    ],
  },
];

const getStr = (index) => {
  let str = "";
  toolTipData[index]?.forEach((i) => {
    str = str + (i?.key + ": " + i?.value + "<br/>");
  });
  return str;
};
export const setToolTips = (yAxisName, graphAnalysis) => {
  let tooltip = {
    trigger: "axis",
    axisPointer: {
      type: "shadow",
    },
    confine: true,
    textStyle: {
      fontWeight: "normal", // 确保字体不加粗
      fontSize: 10,
    },
    formatter: function (params) {
      //日期，图例，值，
      let str = `${params[0]?.name || ""}       ${yAxisName || " "}`;
      params.forEach((p) => {
        let value = new Intl.NumberFormat("en-US").format(p?.value); //千分位
        if (p.seriesName && !p.seriesName.includes("series")) {
          let seriesName = p.seriesName;
          let color = p.color;
          str = `${str}<br/><span style="line-height:20px;"><span style="display:inline-block;margin-right:5px;width: 12px; height: 12px;background-color: ${color};border-radius: 50%;"></span>${seriesName}:   ${value}</span>`;
        } else {
          str = `${str}<br/> ${value}`;
        }
      });
      if (graphAnalysis) {
        str = `${str}<br/><label style="font-size: 12px;font-weight: 500;line-height: 12px;height:12px;color: #F4A300;font-family: PingFang SC;">左键点击数据进行归因分析</label> `;
      }
      return str;
    },
  };
  return tooltip;
};

export const estimateLocalStorageSize = () => {
  let totalSize = 0;
  // 遍历 localStorage 中的所有键值对
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    const value = localStorage.getItem(key);
    // 将键和值转换为字符串并计算长度
    totalSize += JSON.stringify(key).length + JSON.stringify(value).length;
  }
  // console.log(`localStorage 已存储数据大小（估算）: ${totalSize} 字符`);
  return totalSize;
};

//X轴展示
export const getAxisLabel = () => {
  return {
    // interval: 0, // 坐标轴刻度标签的显示间隔
    hideOverlap: true,
    formatter: function (v) {
      var value = v?.toString();
      if (value?.length > 8) {
        value = value.slice(0, 7) + "..";
      }
      const first = value.slice(0, 4);
      const second = value.slice(4, 9);
      return `${first}\n${second}`;
    },
  };
};

export const getGrid = (twoDimension, scrollBar) => {
  const scrollHeight = scrollBar ? 45 : 15;
  const exampleHeight = twoDimension ? 30 : 10;
  return {
    left: 50,
    right: 10,
    top: 30,
    bottom: 30 + scrollHeight + exampleHeight,
  };
};

export const textStyle = {
  fontFamily: "PingFang SC",
  fontSize: 6, // 字体大小
  fontWeight: "lighter‌", // 字体粗细
};

export const splitNumber = 3;

export const graphStyle = (enlarge) => {
  const isMobile = window.innerWidth <= 600;
  if (enlarge && isMobile) {
    return {
      height: "16rem",  // 移动端放大时高度增加到320px
      width: "95%",
    };
  } else if (enlarge && !isMobile) {
    return {
      height: "550px",
    };
  } else if (!isMobile) {
    return {
      height: "280px",
    };
  } else {
    return {
      height: "22rem",  // 移动端正常时高度增加到220px
    };
  }
};

export const removeWanName = (name = []) => {
  const arr = Array.from(name);
  const idx = arr.findIndex((i) => i == "万");
  if (idx > -1) {
    arr.splice(idx, 1);
    return arr.join("");
  }
  return name;
};

export const nameHasWan = (name) => {
  return name?.includes("万");
};

export const addWanValue = (value, hasWan) => {
  //如果源数据已经有万，value先乘以万
  if (hasWan) {
    value = Number(value) * 10000;
  }
  let str = value.toString();
  if (str.length > 8) {
    return Number((str / 100000000).toFixed(1)) + "亿";
  } else if (str.length > 4) {
    return Number((str / 10000).toFixed(1)) + "万";
  } else {
    return str;
  }
};

export const getYAxisMin = (series) => {
  const arr = series
    ?.map((i) => i.data)
    ?.flat()
    ?.map((i) => Number(i));
  const min = Math.min(...arr);
  const rate = Number(min) > 0 ? 0.6 : 1.4;
  return Number((min * rate).toFixed(1));
};
