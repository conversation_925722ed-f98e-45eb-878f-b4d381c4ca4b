import { Tracking } from '@ea/ai-sdk'

// 通过环境变量判断是否为开发环境
export const isDev = () => {
  return import.meta.env.MODE === 'dev'
}

// 获取当前环境
export const getCurrentEnv = () => {
  const dev = isDev()
  if (dev) return 'dev'
  // 根据当前域名是否为api-ptpd-ai.tcl.com，如果是，则为生成环境，如果是其他域名，则为测试环境
  if (window.location.hostname === 'api-ptpd-ai.tcl.com') return 'prod'
  return 'sit'
}

// 部署环境：Tracking.ENV.DEV、Tracking.ENV.SIT、Tracking.ENV.PROD
export const getDeployEnv = () => {
  const url = import.meta.env.VITE_HTTP_BASE_URL
  let env = Tracking.ENV.PROD
  if (url.includes('-dev-') || url.includes('localhost/')) {
    env = Tracking.ENV.DEV
  }
  if (url.includes('-sit-')) {
    env = Tracking.ENV.SIT
  }
  if (url.includes('-uat-')) {
    env = Tracking.ENV.PRE
  }
  return env
}

export const FIRST_LOGIN_CODE = 407 // 账号首次登录 - 需修改密码
export const NOT_TENANT_CODE = 1004 // 账号无租户 - 需完善信息
export const NOT_BIND_ACCOUNT_CODE = 6004 // 微信登录 未绑定账号
export const ACCOUNT_LOCK_CODE = 2003 // 密码错误次数超过限制，账号被锁定
export const PASSWORD_ERROR = 2001 // 密码错误
export const ACCESS_TOKEN = 'access_octopus_token'
export const TOKEN_TYPE = 'token_type'
export const whileName = ['system'] //配置即隐藏不展示

// JSON 转 FormData
export const jsonToFormData = params => {
  const formData = new FormData()
  Object.keys(params).forEach(key => {
    formData.append(key, params[key])
  })
  return formData
}

export function isJson(str) {
  if (typeof str === 'string') {
    try {
      const obj = JSON.parse(str)
      return obj && typeof obj === 'object'
    } catch (e) {
      console.log(e)
      return false
    }
  }
  return false
}

/* 判断 页面 是否在 iframe 打开
 * @return {boolean}
 * */
export const isIframeOpen = window.self != window.top

// 解析url域名或ip端口
export function processUrl(url) {
  const ipPortRegex = /^(https?:\/\/)([^:/]+):(\d+)/
  const ipPortMatch = url.match(ipPortRegex)

  if (ipPortMatch) {
    const protocol = ipPortMatch[1]
    const ip = ipPortMatch[2]
    const port = ipPortMatch[3]
    return `${protocol}${ip}:${port}`
  } else {
    const domainRegex = /^(https?:\/\/[^/]+)/
    const domainMatch = url.match(domainRegex)
    if (domainMatch) {
      return domainMatch[0]
    }
  }

  return url
}

// 解析域名或者IP+端口  不返回协议http或者https
export function extractDomainOrIPWithPort(url) {
  // 使用正则表达式提取域名或IP和端口（如果存在）
  const regex = /^(?:https?:\/\/)?(?:www\.)?([^\/:\n]+)(?::(\d+))?/i
  const matches = url.match(regex)

  if (matches && matches[1]) {
    // 如果有端口，则拼接端口信息
    const domainOrIP = matches[1]
    const port = matches[2] ? `:${matches[2]}` : ''
    return domainOrIP + port
  } else {
    return ''
  }
}

// 获取os信息
export function getOS() {
  let os = ''
  if (inTxinMobile()) {
    os = 'T信-移动端'
  } else if (isInTxinPcBrowser()) {
    os = 'T信-PC端'
  } else {
    os = isPC() ? 'WEB-PC端' : 'WEB-移动端'
  }
  return os
}

export function inTxinMobile() {
  const userAgent = window.navigator.userAgent
  return userAgent.includes('teamwork') && userAgent.includes('Mobile')
}

export function isInTxinPcBrowser() {
  return window.navigator.userAgent.includes('teamwork_pc')
}

export function isPC() {
  let userAgentInfo = navigator.userAgent
  let Agents = ['Android', 'iPhone', 'SymbianOS', 'Windows Phone', 'iPad', 'iPod']
  let flag = true
  for (let i = 0; i < Agents.length; i++) {
    if (userAgentInfo.indexOf(Agents[i]) > 0) {
      flag = false
      break
    }
  }
  return flag
}

export const URL_FROM = {
  AI_ASSISTANT: 'ai-assistant', // AI助手
  LIGHT_APPLICATION: 'light-application', // 轻应用
  DATA: 'data', // 太极
  DATA_TXIN: 'data-txin', // 太极-T信环境
}

export const callApi = (name, method, params) => {
  /**
   * 调用cordova的方法
   * @param name {string} 方法组、类别
   * @param method {string} 方法名称
   * @param params {Array} 参数
   * @return {promise}
   */
  const promise = new Promise((resolve, reject) => {
    const cordova = window.cordova || window.Cordova
    if (cordova) {
      console.log('cordova----', cordova)
      try {
        cordova.exec(
          msg => {
            console.log('cordova----resolve--msg', msg)
            resolve(msg)
          },
          msg => {
            console.log('[Cordova Error] ', msg)
            reject(msg)
          },
          name,
          method,
          params || []
        )
      } catch (e) {
        console.error('_error', 'widget error:', e)
        reject(e)
      }
    } else {
      console.log('_debug', 'Cordova is not exist')
      reject('Cordova is not exist')
    }
  })

  return promise
}
