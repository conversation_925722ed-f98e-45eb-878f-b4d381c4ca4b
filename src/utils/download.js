import { message } from 'ant-design-vue'

// 通过 a 标签下载文件
export const download = data => {
  const { fileName, blob } = data
  if (!blob) {
    return
  }
  if (blob?.type === 'application/json') {
    const reader = new FileReader()
    reader.readAsText(blob, 'utf-8')
    reader.onload = function () {
      const readerRes = reader.result
      const resObj = JSON.parse(readerRes)
      message.error(resObj.msg)
    }

    return
  }
  const a = document.createElement('a')
  a.href = URL.createObjectURL(blob)
  a.style.display = 'none'
  a.download = fileName
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
}

export const getHeadersFileName = data => {
  const contentDisposition = data?.headers['content-disposition'] || ''
  const prefix = "attachment;filename*=utf-8'zh_cn'"
  const prefix1 = 'attachment; filename='
  const prefix2 = 'attachment;filename='
  let fileName = contentDisposition
  if (contentDisposition.indexOf(prefix) === 0) {
    fileName = decodeURI(contentDisposition.slice(prefix.length))
  } else if (contentDisposition.indexOf(prefix1) === 0) {
    fileName = decodeURI(contentDisposition.slice(prefix1.length))
  } else if (contentDisposition.indexOf(prefix2) === 0) {
    fileName = decodeURI(contentDisposition.slice(prefix2.length))
  }

  return fileName
}
