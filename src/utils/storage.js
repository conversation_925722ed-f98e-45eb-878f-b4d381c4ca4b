// utils/storage.js
const ExpiresStorage = {
    setItem(key, value, expires = 0) {
        const data = {
            value,
            expires: expires ? new Date().getTime() + expires : 0
        }
        localStorage.setItem(key, JSON.stringify(data))
    },

    getItem(key) {
        const data = localStorage.getItem(key)
        if (!data) return null

        const item = JSON.parse(data)
        // 如果没有设置过期时间，或者未过期，返回值
        if (item.expires === 0 || item.expires > new Date().getTime()) {
            return item.value
        }
        // 已过期，删除数据
        localStorage.removeItem(key)
        return null
    }
}

// 创建带有效期的存储对象
export const createExpiresStorage = (expires) => ({
    setItem: (key, value) => ExpiresStorage.setItem(key, value, expires),
    getItem: (key) => ExpiresStorage.getItem(key)
})