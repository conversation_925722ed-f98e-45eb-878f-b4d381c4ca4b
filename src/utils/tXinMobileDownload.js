import tlinkUtils from "@/utils/tlinkUtils";
import { CustomRequest } from "@/utils/CustomRequest";

/**
 * T信移动端下载预览文件
 * @param {*} blob blob文件
 * @param {*} buffer buffer文件（ios中不能直接传blob）
 * @param {*} fileName 名称
 */
export async function tXinMobileDownload(blob, buffer, fileName) {
  console.log("[导出文件-buffer长度] ", buffer?.length);
  console.log("[导出文件-buffer类型] ", Object.prototype.toString.call(buffer));
  console.log("[导出文件-blob大小] ", blob.size);
  const formData = new FormData();
  formData.append("file", blob, fileName);
  // iOS T信，后端接收到的文件大小为0，所以增加base64
  const isIOS = /iPhone|iPad|iPod/i.test(navigator.userAgent);
  if (isIOS) {
    // 将buffer转换为Base64
    const binaryString = Array.from(new Uint8Array(buffer))
      .map((byte) => String.fromCharCode(byte))
      .join("");
    const base64Data = btoa(binaryString);
    formData.append("base64Data", base64Data);
    formData.append("platform", "ios");
    formData.append("fileName", fileName);
  }
  const res = await CustomRequest({
    url: "/chatbi/v1/upload",
    method: "post",
    data: formData,
    headers: {
      "Content-Type": "multipart/form-data",
    },
    timeout: 60 * 1000,
  });
  if (res) {
    tlinkUtils.callCordova("MideaCommon", "previewFile", [
      encodeURI(res),
      fileName,
    ]);
    addFile(res, fileName); //上面文件存在pass公开桶上，此处addFile是为了定时删除文件
  } else {
    throw Error("下载文件失败");
  }
}

// 定时删除paas文件
const DOWNLOAD_FILE_LIST = "download-file-list";

export const getFileList = () => {
  const filesStr = localStorage.getItem(DOWNLOAD_FILE_LIST);
  return filesStr ? JSON.parse(filesStr) : [];
};

export const addFile = (url, name) => {
  const file = {
    url,
    name,
    timestamp: Date.now(),
  };
  const fileList = getFileList();
  fileList.push(file);
  localStorage.setItem(DOWNLOAD_FILE_LIST, JSON.stringify(fileList));
  setTimeout(() => {
    deleteFile();
  }, 30000);
};

export const deleteFile = async () => {
  const fileList = getFileList();

  const deletePromises = fileList.map((file) =>
    CustomRequest({
      url: "/chatbi/v1/delete",
      method: "post",
      params: { fileUrl: file.url },
    })
      .then(() => file.url)
      .catch((error) => {
        console.error(`删除文件失败: ${file.url}`, error);
        return null; // 返回null表示删除失败
      })
  );

  const deletedUrls = await Promise.all(deletePromises);

  const successfullyDeletedUrls = deletedUrls.filter((url) => url !== null);

  // 更新本地存储，只保留未被成功删除的文件
  const currentFileList = getFileList();
  const updatedFileList = currentFileList.filter(
    (item) => !successfullyDeletedUrls.includes(item.url)
  );

  localStorage.setItem(DOWNLOAD_FILE_LIST, JSON.stringify(updatedFileList));
};
