import <PERSON>ie from "dexie";
import { userStore } from "@/stores";

const db = new <PERSON>ie("myDatabase");
db.version(1).stores({
  chatBIHistoryChat: "uid",
});

//增删改查
//新增、修改数据
export function putDataDB(list) {
  const uid = userStore()?.userInfo?.username;
  const data = {
    uid: uid,
    historyChat: list,
  };
  try {
    db?.chatBIHistoryChat?.put(data)?.catch((error) => {
      console.log("indexDB----put---error", error);
    });
  } catch {
    console.log("indexDB----put失败---", error);
  }
}

// 获取数据
export async function getDataDB() {
  const uid = userStore()?.userInfo?.username;
  try {
    const data = await db?.chatBIHistoryChat?.get(uid);
    console.log("indexDB----getdata---", data);
    return data;
  } catch (error) {
    console.log("indexDB----get失败---", error);
  }
}
