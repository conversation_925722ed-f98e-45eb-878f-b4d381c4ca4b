import { inTxinMobile, isInTxinPcBrowser } from "./index";

// 用于发送消息给宿主页面
class TlinkUtil {
  // 开启录音
  startVoiceRecord(id) {
    const targetWindow = window.parent;
    const params = {
      id,
      action: "tlink-start-voice-record",
    };
    targetWindow.postMessage(JSON.stringify(params), "*");
  }
  // 停止录音 0- 取消录音（不会将语音文件转换成文本）  1-停止录音（将语音文件转换成文本）  2-停止录音并发送消息（将语音文件转换成文本，并发送文本消息）
  stopVoiceRecord(id, autoSend = 0) {
    const targetWindow = window.parent;
    const params = {
      id,
      autoSend,
      action: "tlink-stop-voice-record",
    };
    targetWindow.postMessage(JSON.stringify(params), "*");
  }

  // 打开链接
  openUrl(href, title = "", text = "") {
    const isTchatMobile = inTxinMobile();
    const isTchatPc = isInTxinPcBrowser();
    if (isTchatMobile) {
      const targetWindow = window.parent;
      const params = {
        action: "call-cordova",
        name: "MideaCommon",
        method: "openUrl",
        params: [
          href,
          JSON.stringify({
            title: title || text || href,
            mtitle: "",
            platform: 1,
          }),
        ],
      };
      targetWindow.postMessage(JSON.stringify(params), "*");
    } else {
      window.open(href, "_blank");
    }
  }

  // 返回上一级
  goBack() {
    const targetWindow = window.parent;
    const params = {
      action: "go-back",
    };
    targetWindow.postMessage(JSON.stringify(params), "*");
  }

  // 调用T信cordova
  callCordova(name, method, params) {
    const targetWindow = window.parent;
    const data = {
      action: "call-cordova",
      name,
      method,
      params,
    };
    targetWindow.postMessage(JSON.stringify(data), "*");
  }

  // 异步调用cordova
  asyncCallCordova = (name, method, params) => {
    return new Promise((resolve, reject) => {
      const targetWindow = window.parent;
      const data = {
        action: "call-cordova",
        name,
        method,
        params,
      };

      // 创建一个唯一的请求ID，用于匹配响应
      const requestId = Date.now() + Math.random().toString(36).substring(2, 9);
      data.requestId = requestId;

      // 设置消息监听器
      const messageHandler = (event) => {
        try {
          const response = JSON.parse(event.data);

          // 检查是否是对应的响应消息
          if (
            response.action === "call-cordova-result" &&
            response.requestData &&
            response.requestData.requestId === requestId
          ) {
            // 移除事件监听器，避免内存泄漏
            window.removeEventListener("message", messageHandler);

            // 根据success字段决定resolve还是reject
            if (response.success) {
              resolve(response.data);
            } else {
              reject(response.data || new Error("Cordova call failed"));
            }
          }
        } catch (err) {
          // 忽略无法解析的消息
        }
      };

      // 添加事件监听器
      window.addEventListener("message", messageHandler);

      // 发送消息
      targetWindow.postMessage(JSON.stringify(data), "*");

      // 设置超时处理
      setTimeout(() => {
        window.removeEventListener("message", messageHandler);
        reject(new Error(`Cordova call timed out: ${name}.${method}`));
      }, 60000); // 60秒超时
    });
  };
}

export default new TlinkUtil();
