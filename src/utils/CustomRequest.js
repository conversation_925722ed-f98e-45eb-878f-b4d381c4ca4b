import { request } from '@/common'
import { message } from 'ant-design-vue'

import { userStore } from '@/stores'

export const CustomRequest = options => {
  const baseURL = import.meta.env.VITE_HTTP_BASE_URL
  const timeout = options?.timeout || import.meta.env.VITE_HTTP_TIMEOUT
  const { token, clearUserInfo } = userStore()
  return request({
    baseURL,
    timeout,
    requestOptions: {
      url: options.url,
      method: options.method,
      data: options.data,
      params: options.params,
      ...options,
    },
    errorCallback: (errStatus, errorMessage) => {
      if (errStatus === 401) {
        setTimeout(() => {
          clearUserInfo()
          window.location.reload()
        }, 500)
        return message.error(errorMessage)
      } else {
        message.error(errorMessage)
        return Promise.reject(errorMessage)
      }
    },
    handleGenConfig: config => {
      // config.headers['x-lang'] = `${userStore.currentLocale}`;
      if (options.needAuth === false) {
        return config
      } else {
        // const token = userStore.token;
        config.headers['Authorization'] = `bearer ${token}`
        return config
      }
    },
    handleServiceError: result => {
      const { code, data, message: msg } = result?.data || {}

      // blob下载场景：暴露最外层数据
      if (options.responseType === 'blob' && result.data) {
        return Promise.resolve(result)
      }

      if (code !== 1000) {
        message.error(msg)
        return Promise.reject(data)
      }

      return Promise.resolve(data)
    },
  })
}
