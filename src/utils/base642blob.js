 //根据文件后缀,添加base64前缀,拼接完整的base64
export const getBase64Type=(type)=> {
  switch (type) {
    case 'txt': return 'data:text/plain;base64,'
    case 'doc': return 'data:application/msword;base64,'
    case 'docx': return 'data:application/vnd.openxmlformats-officedocument.wordprocessingml.document;base64,'
    case 'xls': return 'data:application/vnd.ms-excel;base64,'
    case 'xlsx': return 'data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,'
    case 'pdf': return 'data:application/pdf;base64,'
    case 'pptx': return 'data:application/vnd.openxmlformats-officedocument.presentationml.presentation;base64,'
    case 'ppt': return 'data:application/vnd.ms-powerpoint;base64,'
    case 'png': return 'data:image/png;base64,'
    case 'jpg', 'jpeg': return 'data:image/jpeg;base64,'
    case 'gif': return 'data:image/gif;base64,'
    case 'svg': return 'data:image/svg+xml;base64,'
    case 'ico': return 'data:image/x-icon;base64,'
    case 'bmp': return 'data:image/bmp;base64,'
  }
}
 //将完整的base64码转换为blob
export const base64toBlob=(dataurl)=> {
  var arr = dataurl.split(","),
    mimeString = arr[0].match(/:(.*?);/)[1],
    str = atob(arr[1]),
    u8 = new Uint8Array(str.length)
  for (let i = 0; i < str.length; i++) {
    u8[i] = str.charCodeAt(i)
  }
  return new Blob([u8], { type: mimeString })
}