
export function validateXML(xmlContent) {
  //errorCode 0是xml正确，1是xml错误，2是无法验证
  var xmlDoc, errorMessage, errorCode = 0
  // code for IE
  if (window.ActiveXObject) {
    xmlDoc = new ActiveXObject("Microsoft.XMLDOM")
    xmlDoc.async = "false"
    xmlDoc.loadXML(xmlContent)

    if (xmlDoc.parseError.errorCode != 0) {
      errorMessage = "错误code: " + xmlDoc.parseError.errorCode + "\n"
      errorMessage = errorMessage + "错误原因: " + xmlDoc.parseError.reason
      errorMessage = errorMessage + "错误位置: " + xmlDoc.parseError.line
      errorCode = 1
    }
    else {
      errorMessage = "格式正确"
    }
  }
  // code for Mozilla, Firefox, Opera, chrome, safari,etc.
  else if (document.implementation.createDocument) {
    var parser = new DOMParser()
    xmlDoc = parser.parseFromString(xmlContent, "text/xml")
    var error = xmlDoc.getElementsByTagName("parsererror")
    if (error.length > 0) {
      if (xmlDoc.documentElement.nodeName == "parsererror") {
        errorCode = 1
        errorMessage = xmlDoc.documentElement.childNodes[0].nodeValue
      } else {
        errorCode = 1
        errorMessage = xmlDoc.getElementsByTagName("parsererror")[0].innerHTML
      }
    }
    else {
      errorMessage = "格式正确"
    }
  }
  else {
    errorCode = 2
    errorMessage = "浏览器不支持验证，无法验证xml正确性"
  }
  return {
    "msg": errorMessage,
    "error_code": errorCode
  }
}

export function validateJSON(str) {
  if (typeof str === 'string') {
    try {
      const obj = JSON.parse(str)
      return obj && typeof obj === 'object'
    } catch (e) {
      console.log(e)
      return false
    }
  }
  return false
}
/**
 * 格式是由“.”分割的四部分，每部分的范围是0-255；
 * 每段的正则可以分几部分来写：200—255；100-199；10-99；0-9；
 */
export const IPv4Regex = /^((25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d)))\.){3}(25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d)))$/
/**
 * 格式是0-65535
 * 同理可以分为几部分来构造：60000-65535；10000-59999；1000-9999；100-999；10-99；0-9；
 */
export const PortRegex = /^([0-9]|[1-9]\d{1,3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])$/

/**
 * ip + 端口正则
 */
export const IP_PortRegex = /^((25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d)))\.){3}(25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d)))(\:([0-9]|[1-9]\d{1,3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])){0,1}$/
export const Protocol_IP_PortRegex = /(^((https|http|ftp)?:\/\/)([0-9a-z.]+)(:[0-9]+)?([/0-9a-z.]+)?(\?[0-9a-z&=]+)?(#[0-9-a-z]+)?)|^((25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d)))\.){3}(25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d)))(\:([0-9]|[1-9]\d{1,3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])){0,1}$/ig
export const Regular_IP_PortRegex = /(^((https|http|ftp|ws|wss)?:\/\/)([0-9a-z.]+)(:[0-9]+)?([/0-9a-z.]+)?(\?[0-9a-z&=]+)?(#[0-9-a-z]+)?)|^((25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d)))\.){3}(25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d)))(\:([0-9]|[1-9]\d{1,3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])){0,1}$/ig
