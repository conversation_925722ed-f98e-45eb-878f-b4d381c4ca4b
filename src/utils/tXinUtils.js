import tlinkUtils from "@/utils/tlinkUtils";

/**
 * User Agent判断当前设备是否是安卓手机
 * @returns {boolean} - 如果是安卓设备返回true，否则返回false
 */
export const isAndroidByUserAgent = () => {
  if (typeof navigator === "undefined" || !navigator.userAgent) {
    console.warn("无法获取浏览器用户代理信息");
    return false;
  }

  const userAgent = navigator.userAgent.toLowerCase();

  return userAgent.indexOf("android") > -1;
};

/**
 * 比较两个版本号
 * @param {string} version1 - 第一个版本号，如 "4.11.2"
 * @param {string} version2 - 第二个版本号，如 "4.10.0"
 * @returns {number} - 如果version1 > version2返回1，如果version1 < version2返回-1，如果相等返回0
 */
export const compareVersions = (version1, version2) => {
  const parts1 = version1.split(".").map(Number);
  const parts2 = version2.split(".").map(Number);

  const maxLength = Math.max(parts1.length, parts2.length);

  for (let i = 0; i < maxLength; i++) {
    const part1 = parts1[i] || 0;
    const part2 = parts2[i] || 0;

    if (part1 > part2) return 1;
    if (part1 < part2) return -1;
  }

  return 0;
};

/**
 * 检查应用版本是否小于指定版本
 * @param {string} targetVersion - 目标版本，如 "4.11.2"
 * @returns {Promise<boolean>} - 如果当前版本小于目标版本，返回true
 */
export const isAppVersionLessThan = async (targetVersion = "4.11.2") => {
  try {
    const deviceInfo = await tlinkUtils.asyncCallCordova(
      "MideaCommon",
      "getDeviceInfo",
      []
    );

    if (!deviceInfo || !deviceInfo.appVersion) {
      throw new Error("无法获取应用版本信息");
    }

    const currentVersion = deviceInfo.appVersion;
    return compareVersions(currentVersion, targetVersion) < 0;
  } catch (error) {
    console.error("检查应用版本失败:", error);
    throw error;
  }
};
