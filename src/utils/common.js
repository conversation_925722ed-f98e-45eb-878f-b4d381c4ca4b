export const tableScroll = { y: window.innerHeight / 1.5 };
export function getUrlParamObj(url) {
  let objParam = new Map();
  let paramStr = url.split("?")[1];
  const searchParams = new URLSearchParams(paramStr);
  for (const [key, value] of searchParams.entries()) {
    objParam.set(key, value);
  }
  return objParam;
}

export function checkOverViewParam(param) {
  if (!param?.type) return false;
  if (
    (param?.type == 2 || param?.type == 3) &&
    (!param?.orgIds || !param?.orgIds?.length)
  )
    return false;
  if (param?.type == 4 && (!param?.accounts || !param?.accounts?.length))
    return false;
  return true;
}

export const getAvgColor = (type) => {
  switch (type) {
    case "CENTER":
      return "rgb(211,14,134)";
    case "DEPT":
      return "rgb(196,47,74)";
    default:
      return "rgb(255,116,0)";
  }
};

export const getYAxisMin = (serieData, averageValArr) => {
  const minVal = Math.min(...(serieData || []), ...averageValArr);
  return Number((minVal * 0.8).toFixed(2));
};

export const getYAxisMax = (serieData, averageValArr) => {
  const maxVal = Math.max(...(serieData || []), ...averageValArr);
  return Number((maxVal * 1.2).toFixed(2));
};

export const getAvgMarkLinePos = (type) => {
  switch (type) {
    case "CENTER":
      return "insideStartTop";
    case "DEPT":
      return "middle";
    default:
      return "insideEndTop";
  }
};

export const calcStackedData = (arr) => {
  if (!arr?.length) return [];
  let len = arr[0]?.length;
  let brr = new Array(len).fill(0);
  for (let i = 0; i < len; i++) {
    arr.forEach((j) => {
      brr[i] += j[i];
    });
  }
  return brr;
};

export const isDebug = () => {
  return new URLSearchParams(window.location.search).get("debug") === "1";
};

export const isThink = () => {
  return new URLSearchParams(window.location.search).get("think") === "1";
};
