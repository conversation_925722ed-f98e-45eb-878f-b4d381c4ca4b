/**
 * 发布订阅的事件总线
 */
export class EventBus {
  events = new Map()

  on(eventName, cb) {
    const eventMap = this.events.get(eventName)

    if (eventMap) {
      eventMap.set(cb, cb.bind(this))
    } else {
      const map = new Map()
      map.set(cb, cb.bind(this))
      this.events.set(eventName, map)
    }
  }

  remove(eventName, cb) {
    const eventMap = this.events.get(eventName)

    if (eventMap) {
      eventMap.delete(cb)
    }
  }

  emit(eventName, ...arg) {
    const eventMap = this.events.get(eventName)

    if (eventMap) {
      eventMap.forEach((key, cb) => {
        cb(...arg)
      })
    }
  }
}

export default new EventBus()
