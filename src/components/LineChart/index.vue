<template>
  <div class="line-chart-container">
    <!-- 图表标题 -->
    <TitleWithTooltip v-if="chartTitle" :title="chartTitle" :tooltip="titleTooltip" class="chart-title" />

    <div
      ref="chartRef"
      :style="{ width: '100%', height: chartHeight }"
      class="line-chart"
      v-if="xAxisData.length"
    ></div>
    <a-empty v-else description="暂无数据" />
  </div>
</template>

<script setup>
  // markRaw 和 shallowRef 用于解决 Vue3 响应式代理导致的 ECharts 交互问题
  import { ref, computed, onMounted, onUnmounted, watch, nextTick, markRaw, shallowRef } from 'vue'
  import * as echarts from 'echarts'
  import TitleWithTooltip from '@/components/TitleWithTooltip/index.vue'

  // 定义组件 props
  const props = defineProps({
    // 图表高度
    height: {
      type: [Number, String],
      default: 400,
    },
    // X轴数据
    xAxisData: {
      type: Array,
      default: () => ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
    },
    // 系列数据配置
    series: {
      type: Array,
      default: () => [
        {
          name: '实际产能',
          data: [],
          color: '#6355FF',
          lineWidth: 2,
          icon: 'line-chart-tooltip-1',
        },
        {
          name: '需求产能',
          data: [],
          color: '#779BFC',
          lineWidth: 2,
          icon: 'line-chart-tooltip-2',
        },
      ],
    },
    // 图表标题
    chartTitle: {
      type: String,
      default: '',
    },
    // 标题提示信息
    titleTooltip: {
      type: String,
      default: '',
    },
    // 额外数据
    extra: {
      type: Object,
      default: () => ({}),
    },
  })

  const chartRef = ref(null)
  // 【重要】使用 shallowRef 避免 ECharts 实例被 Vue3 响应式系统代理
  // 问题：Vue3 会将 ref 对象转换为 Proxy，影响 ECharts 内部属性访问
  // 症状：交互时可能出现属性访问错误
  // 解决：shallowRef 只对引用进行响应式处理，不深度代理对象内部属性
  const chartInstance = shallowRef(null)

  // 计算图表高度
  const chartHeight = computed(() => {
    if (typeof props.height === 'number') {
      return `${props.height}px`
    }
    return props.height
  })

  // 图表配置
  const chartOptions = computed(() => {
    return {
      // title: {
      //   text: props.chartTitle,
      //   top: '0px', // 标题距离顶部的距离
      //   textStyle: {
      //     fontFamily: 'PingFang SC',
      //     fontSize: 16,
      //     fontWeight: 500,
      //     lineHeight: 16,
      //     color: '#333333',
      //   },
      // },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'white',
        borderColor: 'transparent',
        textStyle: {
          color: '#666',
          fontSize: 12,
        },

        formatter: function (params) {
          let result = `<div class="tooltip-title">${params[0].axisValue}</div>`
          params.forEach(param => {
            // 根据系列名称查找对应的图标配置
            const seriesConfig = props.series.find(s => s.name === param.seriesName)
            // let iconSrc = ''
            // console.log('===>param', param)
            // if (seriesConfig && seriesConfig.icon) {
            //   try {
            //     iconSrc = new URL(`../assets/image/${seriesConfig.icon}.png`, import.meta.url).href
            //   } catch (error) {
            //     console.warn(`图标文件不存在: ${seriesConfig.icon}.png`)
            //   }
            // }
            // const iconHtml = iconSrc
            //   ? `<img src="${iconSrc}" style="width: 12px; height: 12px; margin-right: 4px; " />`
            //   : param.marker
            // result += `${iconHtml} ${param.seriesName}: ${param.value}<br/>`
            const icon = `<span class="icon" style="background-color: ${seriesConfig.color}"></span>`
            result += `<div class="tooltip-item"><span class="name">${icon}${param.seriesName}</span><span class="value"> ${param.value}</span></div>`
          })

          const Gap = `<div class="tooltip-item"><span class="name" style="margin-left: 15px">本周累缺</span><span class="value"> ${
            props.extra?.gapQty[params[0]?.dataIndex]
          }</span></div>`
          const rete = `<div class="tooltip-item"><span class="name" style="margin-left: 15px">产能满足率</span><span class="value"> ${
            props.extra?.rate[params[0]?.dataIndex] + '%'
          }</span></div>`
          result += `${Gap}${rete}`
          return `<div style="background: 'white'">${result}</div>`
        },
      },
      legend: {
        show: true,
        right: '20px',
        top: '10px',
        itemGap: 24,
        // icon: 'arrow',
        formatter: function (name) {
          return name
        },
        textStyle: {
          fontSize: 14,
          color: '#666666',
        },
      },
      grid: {
        left: '2%',
        right: '2%',
        bottom: '2%',
        top: '15%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: props.xAxisData,
        axisLine: {
          lineStyle: {
            color: '#999',
          },
        },
        axisLabel: {
          color: '#6B7280',
          fontSize: 12,
        },
        axisTick: {
          show: false,
        },
      },
      yAxis: {
        type: 'value',
        axisLine: {
          show: false,
        },
        axisLabel: {
          color: '#6B7280',
          fontSize: 12,
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(153, 153, 153, 0.4)',
            type: 'dashed',
          },
        },
      },
      series: props.series.map(seriesItem => ({
        name: seriesItem.name,
        type: 'line',
        data: seriesItem.data,
        lineStyle: {
          color: seriesItem.color,
          width: seriesItem.lineWidth || 2,
        },
        itemStyle: {
          color: seriesItem.color,
        },
        symbolSize: 6,
        smooth: false,
      })),
    }
  })

  // 初始化图表
  const initChart = () => {
    if (!chartRef.value) return

    // 【重要】使用 markRaw 防止 ECharts 实例被 Vue3 响应式代理
    // 配合 shallowRef 双重保护，确保 ECharts 实例不被 Proxy 包装
    // 这样可以避免在交互时出现属性访问错误
    chartInstance.value = markRaw(echarts.init(chartRef.value))

    updateChart()
  }

  // 更新图表
  const updateChart = () => {
    if (!chartInstance.value) return
    chartInstance.value.setOption(chartOptions.value, true)
  }

  // 监听数据变化
  watch(
    () => [props.series, props.xAxisData],
    () => {
      nextTick(() => {
        initChart()
      })
    },
    { deep: true }
  )

  // 监听窗口大小变化
  const handleResize = () => {
    if (chartInstance.value) {
      chartInstance.value.resize()
    }
  }

  onMounted(() => {
    nextTick(() => {
      initChart()
      window.addEventListener('resize', handleResize)
    })
  })

  onUnmounted(() => {
    if (chartInstance.value) {
      chartInstance.value.dispose()
    }
    window.removeEventListener('resize', handleResize)
  })
</script>

<style scoped>
  .line-chart-container {
    position: relative;
    width: 100%;
  }

  .chart-title {
    /* margin-bottom: 16px;
    padding-left: 20px; */
    position: absolute;
    top: 22px;
    z-index: 9;
  }

  .line-chart {
    width: 100%;
  }
  :deep(.tooltip-title) {
    font-family: PingFang SC;
    font-size: 14px;
    font-weight: 500;
    line-height: 20px;
    letter-spacing: 0px;

    color: #000000;
    margin-bottom: 8px;
  }
  :deep(.tooltip-item) {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    .icon {
      display: inline-block;
      width: 10px;
      height: 10px;
      border-radius: 50%;
      margin-right: 4px;
    }
    .name {
      display: flex;

      align-items: center;
      width: 120px;
      font-family: PingFang SC;
      font-size: 14px;
      font-weight: normal;
      line-height: 20px;
      letter-spacing: 0px;

      font-variation-settings: 'opsz' auto;
      color: #666666;
    }
    .value {
      flex: 1;
      font-family: PingFang SC;
      font-size: 14px;
      font-weight: normal;
      line-height: 20px;
      text-align: right;
      letter-spacing: 0px;

      font-variation-settings: 'opsz' auto;
      color: rgba(0, 0, 0, 0.85);
    }
  }
</style>
