<template>
  <div class="page-wrapper flex flex-col h-screen w-full">
    <div class="w-[800px]">
      <!-- 顶部导航栏 -->
      <div class="bg-white h-16 flex items-center justify-between md:flex-col md:h-auto mt-[20px]">
        <div class="flex items-center w-full md:mb-3 relative">
          <div class="flex-1"></div>
          <h2 class="text-xl font-semibold text-#333-500 m-0 md:text-lg absolute left-1/2 transform -translate-x-1/2">
            采购供需平衡Agent
          </h2>
          <div class="flex items-center ml-auto">
            <a-dropdown placement="bottomRight" v-if="false">
              <a-button
                type="text"
                class="flex items-center gap-2 px-3 py-2 rounded-md transition-all duration-300 hover:bg-gray-100"
              >
                <span class="text-gray-800 text-sm">{{ userInfo?.username || '用户' }}</span>
                <down-outlined class="text-xs text-gray-400" />
              </a-button>
              <template #overlay>
                <a-menu>
                  <a-menu-item key="logout" @click="handleLogout">
                    <logout-outlined class="mr-2" />
                    退出登录
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </div>
        </div>

        <div class="flex items-center flex-1 w-full md:w-full">
          <a-tabs
            v-model:activeKey="activeKey"
            @change="handleTabChange"
            class="flex-1 nav-tabs"
            :tabBarStyle="{ margin: 0 }"
          >
            <a-tab-pane key="weekly">
              <template #tab>
                <span class="flex items-center gap-2 md:gap-1 md:text-sm"> N+3供需平衡测算 </span>
              </template>
            </a-tab-pane>
            <a-tab-pane key="yearly">
              <template #tab>
                <span class="flex items-center gap-2 md:gap-1 md:text-sm"> 年度供需平衡测算 </span>
              </template>
            </a-tab-pane>
          </a-tabs>
        </div>
      </div>

      <!-- 子菜单导航 -->
      <div class="sub-menu-nav mt-[24px]" v-if="currentMenuItems.length > 0">
        <div class="sub-menu-container">
          <div
            v-for="item in currentMenuItems"
            :key="item.key"
            class="sub-menu-item"
            :class="{ active: activeSubMenu === item.key }"
            @click="handleSubMenuClick(item.key)"
          >
            {{ item.label }}
          </div>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div class="flex-1 overflow-auto mt-[20px]">
        <router-view v-slot="{ Component }">
          <keep-alive :include="['WeeklyAnalysisReport', 'WeeklyCalculationPlatform']" :max="10">
            <component :is="Component" />
          </keep-alive>
        </router-view>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, watch, computed } from 'vue'
  import { useRouter, useRoute } from 'vue-router'
  import { DownOutlined, LogoutOutlined } from '@ant-design/icons-vue'
  import { userStore } from '@/stores'
  import { Modal } from 'ant-design-vue'
  import { Tracking } from '@ea/ai-sdk'

  // 定义组件名称，用于 keep-alive
  defineOptions({
    name: 'MainLayout',
  })

  const router = useRouter()
  const route = useRoute()
  const activeKey = ref('weekly')
  const activeSubMenu = ref('analysis-report')
  const { clearUserInfo, userInfo } = userStore()

  // 菜单配置 - 两层数据结构
  const menuConfig = {
    weekly: {
      title: 'N+3供需平衡测算',
      children: [
        { key: 'analysis-report', label: '分析报告' },
        { key: 'calculation-platform', label: '测算平台' },
        { key: 'supplier-capacity', label: '供应商日产能维护' },
        { key: 'line-quantity', label: '供应商产线数据' },
        // { key: 'working-days', label: '有效工作天数' },
        { key: 'supplier-quota', label: '供应商配额自定义' },
        { key: 'supplier-inventory', label: '供应商库存管理' },
      ],
    },
    yearly: {
      title: '年度供需平衡测算',
      children: [
        // { key: 'analysis-report', label: '分析报告' },
        { key: 'supplier-capacity', label: '供应商配额&日产能维护' },
        { key: 'calender-config', label: '有效工作天数' },
        // { key: 'supplier-quota', label: '供应商配额' },
      ],
    },
  }

  // 当前菜单项
  const currentMenuItems = computed(() => {
    return menuConfig[activeKey.value]?.children || []
  })

  // 根据当前路由设置选中的菜单项
  watch(
    () => route.path,
    newPath => {
      const pathSegments = newPath.split('/').filter(Boolean)
      if (pathSegments.length >= 1) {
        // 处理新的平铺路由结构
        if (pathSegments[0].startsWith('yearly-')) {
          activeKey.value = 'yearly'
          activeSubMenu.value = pathSegments[0].replace('yearly-', '')
        } else if (pathSegments[0].startsWith('weekly-')) {
          activeKey.value = 'weekly'
          activeSubMenu.value = pathSegments[0].replace('weekly-', '')
        }
      }
    },
    { immediate: true }
  )

  // 处理标签页切换
  const handleTabChange = key => {
    activeKey.value = key
    // 切换到对应模块的第一个子菜单
    const firstSubMenu = menuConfig[key]?.children[0]?.key || 'analysis-report'
    activeSubMenu.value = firstSubMenu
    Tracking.reported('500001', 'TAB切换', '', {
      tab_name: key,
    })
    router.push(`/${key}-${firstSubMenu}`)
  }

  // 处理子菜单点击
  const handleSubMenuClick = key => {
    activeSubMenu.value = key
    Tracking.reported('500002', '二级TAB切换', '', {
      sub_tab_name: key,
    })
    router.push(`/${activeKey.value}-${key}`)
  }

  // 处理退出登录
  const handleLogout = () => {
    Modal.confirm({
      title: '确认退出',
      content: '确定要退出登录吗？',
      okText: '确定',
      cancelText: '取消',
      onOk() {
        clearUserInfo()
        router.replace('/login')
      },
    })
  }
</script>

<style scoped>
  .page-wrapper {
    align-items: center;
  }

  .sub-menu-nav {
    width: 100%;
  }

  .sub-menu-container {
    width: fit-content;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    flex-wrap: nowrap;
    padding: 4px;
    background: #eae9fd;
    border-radius: 8px;
    overflow-x: auto;
  }

  .sub-menu-item {
    padding: 0px 4px;
    cursor: pointer;
    font-size: 14px;
    border-radius: 6px;
    transition: all 0.3s ease;
    white-space: nowrap;

    font-size: 14px;
    font-weight: normal;
    line-height: 22px;
    letter-spacing: 0px;

    font-variation-settings: 'opsz' auto;
    color: #666666;
  }

  .sub-menu-item:hover {
    background: #fff;
    color: #333;
    border-radius: 6px;
    font-weight: 500;
  }

  .sub-menu-item.active {
    background: #fff;
    color: #000;
    border-radius: 6px;
    font-weight: 500;
  }
</style>

<style>
  /* 隐藏原始下划线 */
  .nav-tabs .ant-tabs-ink-bar {
    display: none !important;
  }

  /* 使用伪元素创建与文字等宽的下划线 */
  .nav-tabs .ant-tabs-tab-active .ant-tabs-tab-btn::after {
    content: '';
    position: absolute;
    bottom: -12px;
    left: 0;
    right: 0;
    height: 3px;
    background-color: #6355ff;
    border-radius: 2px 2px 0 0;
  }

  .nav-tabs .ant-tabs-tab .ant-tabs-tab-btn {
    position: relative;
  }

  /* 默认状态样式 */
  .nav-tabs .ant-tabs-tab .ant-tabs-tab-btn {
    font-family: PingFang SC;
    font-size: 16px;
    font-weight: normal;
    color: #999999;
    transition: color 0.3s ease;
    text-shadow: none !important;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* 选中状态样式 */
  .nav-tabs .ant-tabs-tab-active .ant-tabs-tab-btn {
    font-family: PingFang SC;
    font-size: 16px;
    font-weight: 600;
    color: #333333 !important;
    text-shadow: none !important;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
</style>

<style scoped>
  .page-wrapper {
    align-items: center;
  }
  /* Ant Design 组件样式覆盖 */
  .nav-tabs :deep(.ant-tabs-nav) {
    margin: 0;
  }

  .nav-tabs :deep(.ant-tabs-tab) {
    padding: 12px 0px;
    margin: 0 24px 0 0;
  }

  .nav-tabs :deep(.ant-tabs-nav .ant-tabs-ink-bar) {
    height: 3px !important;
  }

  /* 移动端适配 */
  @media (max-width: 768px) {
    .nav-tabs :deep(.ant-tabs-tab) {
      padding: 8px 0px;
      margin: 0 10px 0 0;
      font-size: 14px;
      line-height: 14px;
    }

    .nav-tabs :deep(.ant-tabs-tab-btn) {
      font-size: 14px;
      line-height: 14px;
    }
  }
</style>
