<script lang="jsx">
  import { defineComponent } from 'vue'
  export default defineComponent({
    name: 'SvgIcon',
    props: {
      type: {
        type: String,
        required: true,
      },
    },
    render() {
      return (
        <svg class="svg-icon" aria-hidden="true">
          <use xlink:href={`#icon-${this.type}`} />
        </svg>
      )
    },
  })
</script>

<style lang="less" scoped>
  .svg-icon {
    // width: 1em;
    // height: 1em;
    // vertical-align: -0.15em;
    // fill: currentColor;
    // margin-right: 10px;
    overflow: hidden;
    font-size: 28px;
  }

  .svg-external-icon {
    background-color: currentColor;
    mask-size: cover !important;
    display: inline-block;
  }
</style>
