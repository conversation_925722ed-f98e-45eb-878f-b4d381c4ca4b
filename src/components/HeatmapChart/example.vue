<template>
  <div class="heatmap-examples p-6 space-y-8">
    <h2 class="text-2xl font-bold text-gray-800 mb-6">热力图组件示例</h2>

    <!-- 年度供需Gap热力图 -->
    <div class="example-section">
      <HeatmapChart
        title="年度供需Gap分析"
        subtitle="各产线平台12个月的供需缺口天数分布"
        :height="400"
        :time-axis="yearlyTimeAxis"
        :dimension-axis="platformDimensions"
        :data="yearlyGapData"
        :value-range="[0, 30]"
        :color-config="gapColorConfig"
        unit="天"
        :show-label="true"
      />
    </div>

    <!-- 周度供应商热力图 -->
    <div class="example-section">
      <HeatmapChart
        title="周度供应商供需Gap分析"
        subtitle="各供应商13周的供需缺口天数分布"
        :height="350"
        :time-axis="weeklyTimeAxis"
        :dimension-axis="supplierDimensions"
        :data="weeklySupplierData"
        :value-range="[0, 25]"
        :color-config="supplierColorConfig"
        unit="天"
        :show-label="false"
      />
    </div>

    <!-- 控制面板 -->
    <div class="control-panel bg-gray-50 p-4 rounded-lg">
      <h3 class="text-lg font-semibold mb-4">控制面板</h3>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">显示数值标签</label>
          <a-switch v-model:checked="showLabels" />
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">图表高度</label>
          <a-slider v-model:value="chartHeight" :min="300" :max="600" />
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">最大值范围</label>
          <a-slider v-model:value="maxValue" :min="20" :max="50" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, computed } from 'vue'
  import HeatmapChart from './index.vue'

  // 控制变量
  const showLabels = ref(true)
  const chartHeight = ref(400)
  const maxValue = ref(30)

  // 时间轴数据
  const yearlyTimeAxis = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
  const weeklyTimeAxis = [
    '第1周',
    '第2周',
    '第3周',
    '第4周',
    '第5周',
    '第6周',
    '第7周',
    '第8周',
    '第9周',
    '第10周',
    '第11周',
    '第12周',
    '第13周',
  ]

  // 维度轴数据
  const platformDimensions = ['1200线平台', '600线平台', '750线平台', 'N3平台', 'N4平台']
  const supplierDimensions = ['供应商A', '供应商B', '供应商C', '供应商D']

  // 年度Gap数据 (模拟数据)
  const yearlyGapData = ref([
    // [月份索引, 平台索引, gap天数]
    [0, 0, 5],
    [1, 0, 8],
    [2, 0, 12],
    [3, 0, 3],
    [4, 0, 2],
    [5, 0, 15],
    [6, 0, 18],
    [7, 0, 22],
    [8, 0, 25],
    [9, 0, 20],
    [10, 0, 16],
    [11, 0, 10],

    [0, 1, 3],
    [1, 1, 6],
    [2, 1, 9],
    [3, 1, 1],
    [4, 1, 4],
    [5, 1, 12],
    [6, 1, 15],
    [7, 1, 19],
    [8, 1, 23],
    [9, 1, 18],
    [10, 1, 14],
    [11, 1, 8],

    [0, 2, 7],
    [1, 2, 10],
    [2, 2, 14],
    [3, 2, 5],
    [4, 2, 6],
    [5, 2, 17],
    [6, 2, 20],
    [7, 2, 24],
    [8, 2, 27],
    [9, 2, 22],
    [10, 2, 18],
    [11, 2, 12],

    [0, 3, 2],
    [1, 3, 4],
    [2, 3, 7],
    [3, 3, 0],
    [4, 3, 1],
    [5, 3, 9],
    [6, 3, 12],
    [7, 3, 16],
    [8, 3, 19],
    [9, 3, 15],
    [10, 3, 11],
    [11, 3, 6],

    [0, 4, 9],
    [1, 4, 12],
    [2, 4, 16],
    [3, 4, 7],
    [4, 4, 8],
    [5, 4, 19],
    [6, 4, 22],
    [7, 4, 26],
    [8, 4, 29],
    [9, 4, 24],
    [10, 4, 20],
    [11, 4, 14],
  ])

  // 周度供应商数据 (模拟数据)
  const weeklySupplierData = ref([
    // [周索引, 供应商索引, gap天数]
    [0, 0, 3],
    [1, 0, 5],
    [2, 0, 8],
    [3, 0, 12],
    [4, 0, 2],
    [5, 0, 6],
    [6, 0, 9],
    [7, 0, 15],
    [8, 0, 18],
    [9, 0, 14],
    [10, 0, 11],
    [11, 0, 7],
    [12, 0, 4],

    [0, 1, 1],
    [1, 1, 3],
    [2, 1, 6],
    [3, 1, 10],
    [4, 1, 0],
    [5, 1, 4],
    [6, 1, 7],
    [7, 1, 13],
    [8, 1, 16],
    [9, 1, 12],
    [10, 1, 9],
    [11, 1, 5],
    [12, 1, 2],

    [0, 2, 5],
    [1, 2, 7],
    [2, 2, 10],
    [3, 2, 14],
    [4, 2, 4],
    [5, 2, 8],
    [6, 2, 11],
    [7, 2, 17],
    [8, 2, 20],
    [9, 2, 16],
    [10, 2, 13],
    [11, 2, 9],
    [12, 2, 6],

    [0, 3, 2],
    [1, 3, 4],
    [2, 3, 7],
    [3, 3, 11],
    [4, 3, 1],
    [5, 3, 5],
    [6, 3, 8],
    [7, 3, 14],
    [8, 3, 17],
    [9, 3, 13],
    [10, 3, 10],
    [11, 3, 6],
    [12, 3, 3],
  ])

  // 颜色配置
  const gapColorConfig = {
    inRange: {
      color: ['#50f', '#235', '#678'],
    },
    text: ['低风险', '高风险'],
    textStyle: {
      color: '#333',
    },
  }

  const supplierColorConfig = {
    inRange: {
      color: [
        '#313695',
        '#4575b4',
        '#74add1',
        '#abd9e9',
        '#e0f3f8',
        '#ffffbf',
        '#fee090',
        '#fdae61',
        '#f46d43',
        '#d73027',
      ],
    },
    text: ['供应充足', '供应紧张'],
    textStyle: {
      color: '#333',
    },
  }
</script>

<style scoped>
  .heatmap-examples {
    max-width: 1200px;
    margin: 0 auto;
  }

  .example-section {
    margin-bottom: 2rem;
  }

  .control-panel {
    border: 1px solid #e5e7eb;
  }
</style>
