<template>
  <div class="heatmap-chart-container">
    <!-- <div class="chart-header mb-4" v-if="title">
      <h3 class="text-lg font-semibold text-gray-800">{{ title }}</h3>
      <p class="text-sm text-gray-600" v-if="subtitle">{{ subtitle }}</p>
    </div> -->
    <div ref="chartRef" :style="{ width: '100%', height: chartHeight }" class="heatmap-chart"></div>
  </div>
</template>

<script setup>
  // markRaw 和 shallowRef 用于解决 Vue3 响应式代理导致的 ECharts 交互问题
  import { ref, computed, onMounted, onUnmounted, watch, nextTick, markRaw, shallowRef } from 'vue'
  import * as echarts from 'echarts'

  const props = defineProps({
    // 图表标题
    title: {
      type: String,
      default: '',
    },
    // 图表副标题
    subtitle: {
      type: String,
      default: '',
    },
    // 图表高度
    height: {
      type: [String, Number],
      default: 400,
    },
    // 时间轴数据 (x轴)
    timeAxis: {
      type: Array,
      default: () => ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
    },
    // 维度轴数据 (y轴)
    dimensionAxis: {
      type: Array,
      default: () => ['产线A', '产线B', '产线C', '产线D', '产线E'],
    },
    // 热力图数据 [[x轴索引, y轴索引, 数值], ...]
    data: {
      type: Array,
      default: () => [],
    },
    // 数值范围 [最小值, 最大值]
    valueRange: {
      type: Array,
      default: () => [0, 100],
    },
    // 颜色配置
    colorConfig: {
      type: Object,
      default: () => ({
        // 渐变色配置
        inRange: {
          color: [
            '#313695',
            '#4575b4',
            '#74add1',
            '#abd9e9',
            '#e0f3f8',
            '#ffffbf',
            '#fee090',
            '#fdae61',
            '#f46d43',
            '#d73027',
            '#a50026',
          ],
        },
        // 数值显示配置
        text: ['低', '高'],
        textStyle: {
          color: '#333',
        },
      }),
    },
    // 单位
    unit: {
      type: String,
      default: '天',
    },
    // 是否显示数值标签
    showLabel: {
      type: Boolean,
      default: true,
    },
    // 图表标题
    chartTitle: {
      type: String,
      default: '',
    },
  })

  // 定义事件
  const emit = defineEmits(['cell-click', 'selection-change'])

  const chartRef = ref(null)
  // 使用 shallowRef 避免 ECharts 实例被 Vue3 响应式系统代理
  // 问题：Vue3 会将 ref 对象转换为 Proxy，影响 ECharts 内部属性访问
  // 症状：拖动 visualMap 时报错 "Cannot read properties of undefined (reading 'type')"
  // 解决：shallowRef 只对引用进行响应式处理，不深度代理对象内部属性
  const chartInstance = shallowRef(null)

  // 选中状态管理
  const selectedCell = ref(null) // 当前选中的单元格 {x, y, value, timeLabel, dimensionLabel}

  // 计算数据中的实际最大值和最小值
  const dataRange = computed(() => {
    if (!props.data || props.data.length === 0) {
      return { min: 0, max: 30 } // 默认值
    }

    const values = props.data
      .map(item => {
        // 处理不同的数据格式
        if (Array.isArray(item)) {
          return item[2] // [x, y, value] 格式
        } else if (item && typeof item.value === 'number') {
          return item.value // {value: number} 格式
        } else if (item && Array.isArray(item.value)) {
          return item.value[2] // {value: [x, y, value]} 格式
        }
        return 0
      })
      .filter(val => typeof val === 'number' && !isNaN(val))

    if (values.length === 0) {
      return { min: 0, max: 30 } // 默认值
    }

    return {
      min: Math.min(...values),
      max: Math.max(...values),
    }
  })

  // 计算图表高度
  const chartHeight = computed(() => {
    if (typeof props.height === 'number') {
      return `${props.height}px`
    }
    return props.height
  })

  // 处理数据格式
  const processData = () => {
    return props.data.map(item => {
      if (Array.isArray(item) && item.length >= 3) {
        return [item[0], item[1], item[2] || 0]
      }
      return [0, 0, 0]
    })
  }

  // 查找最大值的第一个位置
  const findMaxValueCell = () => {
    const processedData = processData()
    if (processedData.length === 0) return null

    let maxValue = -Infinity
    let maxCell = null

    processedData.forEach(item => {
      const [x, y, value] = item
      if (value > maxValue) {
        maxValue = value
        maxCell = {
          x,
          y,
          value,
          timeLabel: props.timeAxis[x] || '',
          dimensionLabel: props.dimensionAxis[y] || '',
        }
      }
    })

    return maxCell
  }

  // 设置选中的单元格
  const setSelectedCell = cellData => {
    selectedCell.value = cellData

    // 触发选中变化事件
    emit('selection-change', cellData)

    // 使用 highlight 高亮选中的单元格
    highlightCell(cellData)
  }

  // 高亮指定的单元格
  const highlightCell = cellData => {
    if (!chartInstance.value || !cellData) return

    // 先取消之前的高亮
    chartInstance.value.dispatchAction({
      type: 'downplay',
      seriesIndex: 0,
    })

    // 查找数据索引
    const processedData = processData()
    const dataIndex = processedData.findIndex(item => item[0] === cellData.x && item[1] === cellData.y)

    if (dataIndex !== -1) {
      // 高亮指定的数据点
      chartInstance.value.dispatchAction({
        type: 'highlight',
        seriesIndex: 0,
        dataIndex: dataIndex,
      })
    }
  }

  // 处理单元格点击
  const handleCellClick = params => {
    const { data } = params

    // 处理数据格式：data 可能是数组 [x, y, value] 或对象 {value: [x, y, value]}
    let x, y, value
    if (Array.isArray(data)) {
      ;[x, y, value] = data
    } else if (data && Array.isArray(data.value)) {
      ;[x, y, value] = data.value
    } else {
      console.error('无法解析点击数据:', data)
      return
    }

    const cellData = {
      x,
      y,
      value,
      timeLabel: props.timeAxis[x] || '',
      dimensionLabel: props.dimensionAxis[y] || '',
    }

    // 设置选中状态（会触发样式更新）
    setSelectedCell(cellData)

    // 触发点击事件
    emit('cell-click', cellData)
  }

  // 获取图表配置
  const getChartOption = () => {
    const processedData = processData()

    return {
      title: {
        text: props.chartTitle,
        top: '0px', // 标题距离顶部的距离
        textStyle: {
          fontFamily: 'PingFang SC',
          fontSize: 16,
          fontWeight: 500,
          lineHeight: 16,
          color: '#333333',
        },
      },
      tooltip: {
        position: 'top',
        formatter: function (params) {
          const timeLabel = props.timeAxis[params.data[0]] || ''
          const dimensionLabel = props.dimensionAxis[params.data[1]] || ''
          const value = params.data[2]
          return `${dimensionLabel}<br/>${timeLabel}: ${value}${props.unit}`
        },
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: 'transparent',
        textStyle: {
          color: '#fff',
          fontSize: 12,
        },
      },
      grid: {
        height: '70%',
        top: '15%',
        left: '10%',
        right: '5%',
        bottom: '15%',
      },
      xAxis: {
        type: 'category',
        data: props.timeAxis,
        splitArea: {
          show: true,
        },
        axisLabel: {
          fontSize: 11,
          color: '#666',
        },
        axisTick: {
          show: false,
        },
        axisLine: {
          show: false,
        },
      },
      yAxis: {
        type: 'category',
        data: props.dimensionAxis,
        splitArea: {
          show: true,
        },
        axisLabel: {
          fontSize: 11,
          color: '#666',
        },
        axisTick: {
          show: false,
        },
        axisLine: {
          show: false,
        },
      },
      visualMap: {
        min: dataRange.value.min,
        max: dataRange.value.max,
        calculable: true,
        orient: 'horizontal',
        right: '5%',
        top: '0',
        ...props.colorConfig,
        // 只在两侧显示数值，不在下方显示
        // text: [props.valueRange[1], props.valueRange[0]],
        continuous: 'left',
        show: true,
        // 隐藏下方的 min 和 max 数值标签，只保留两侧的 text
        showLabel: false,
        textStyle: {
          fontSize: 11,
          ...props.colorConfig.textStyle,
        },
      },
      series: [
        {
          name: '供需Gap',
          type: 'heatmap',
          data: processedData,
          label: {
            show: props.showLabel,
            fontSize: 10,
            color: '#333',
          },
          emphasis: {
            itemStyle: {
              borderColor: '#ff4d4f',
              borderWidth: 3,
              shadowBlur: 8,
              shadowColor: 'rgba(255, 77, 79, 0.6)',
            },
          },
        },
      ],
    }
  }

  // 初始化图表
  const initChart = () => {
    if (!chartRef.value) return

    // 【重要】使用 markRaw 防止 ECharts 实例被 Vue3 响应式代理
    // 配合 shallowRef 双重保护，确保 ECharts 实例不被 Proxy 包装
    // 这样可以避免在交互时（如拖动 visualMap）出现属性访问错误
    chartInstance.value = markRaw(echarts.init(chartRef.value))

    // 绑定点击事件
    chartInstance.value.on('click', handleCellClick)

    updateChart()

    // 初始化时选中最大值
    nextTick(() => {
      const maxCell = findMaxValueCell()
      if (maxCell) {
        setSelectedCell(maxCell)
      }
    })
  }

  // 更新图表
  const updateChart = () => {
    if (!chartInstance.value) return

    const option = getChartOption()
    chartInstance.value.setOption(option, true)
  }

  // 响应式处理
  const handleResize = () => {
    if (chartInstance.value) {
      chartInstance.value.resize()
    }
  }

  // 监听数据变化
  watch(
    [
      () => props.data,
      () => props.timeAxis,
      () => props.dimensionAxis,
      () => props.valueRange,
      () => props.colorConfig,
    ],
    () => {
      nextTick(() => {
        updateChart()
        // 数据更新后重新选中最大值
        const maxCell = findMaxValueCell()
        if (maxCell) {
          setSelectedCell(maxCell)
        }
      })
    },
    { deep: true }
  )

  onMounted(() => {
    nextTick(() => {
      initChart()
      window.addEventListener('resize', handleResize)
    })
  })

  onUnmounted(() => {
    if (chartInstance.value) {
      chartInstance.value.dispose()
    }
    window.removeEventListener('resize', handleResize)
  })

  // 暴露方法供父组件调用
  defineExpose({
    updateChart,
    highlightCell,
    setSelectedCell,
    getSelectedCell: () => selectedCell.value,
    getChartInstance: () => chartInstance.value,
  })
</script>

<style scoped>
  .heatmap-chart-container {
    width: 100%;
    background: white;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .heatmap-chart {
    width: 100%;
  }

  /* 移动端适配 */
  @media (max-width: 768px) {
    .heatmap-chart-container {
      padding: 12px;
    }

    .chart-header h3 {
      font-size: 16px;
    }

    .chart-header p {
      font-size: 12px;
    }
  }
</style>
