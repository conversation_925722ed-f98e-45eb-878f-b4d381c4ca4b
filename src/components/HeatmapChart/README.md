# HeatmapChart 热力图组件

基于 ECharts 的 Vue 3 热力图组件，专门用于展示供需 Gap 分析数据。

## 功能特性

- 🎨 **自定义颜色配置** - 支持渐变色和自定义颜色方案
- 📊 **灵活的数据格式** - 支持时间轴（月份/周）和维度轴（产线/供应商）
- 📱 **响应式设计** - 自动适配移动端和桌面端
- 🔧 **高度可配置** - 支持标题、高度、数值范围等配置
- ⚡ **性能优化** - 自动处理图表重绘和内存清理
- 🎯 **智能选中** - 加载完成后自动选中最大值（多个相同取第一个）
- 🖱️ **交互支持** - 支持点击色块高亮，传递 XY 轴信息和 GAP 天数给父组件
- 📡 **事件通信** - 提供 cell-click 和 selection-change 事件

## 基本用法

```vue
<template>
  <HeatmapChart
    ref="heatmapRef"
    title="年度供需Gap分析"
    subtitle="各产线平台12个月的供需缺口天数分布"
    :height="400"
    :time-axis="timeAxis"
    :dimension-axis="dimensionAxis"
    :data="heatmapData"
    :value-range="[0, 30]"
    unit="天"
    :show-label="true"
    @cell-click="handleCellClick"
    @selection-change="handleSelectionChange"
  />
</template>

<script setup>
  import { ref } from 'vue'
  import HeatmapChart from '@/components/HeatmapChart/index.vue'

  const heatmapRef = ref(null)
  const timeAxis = ['1月', '2月', '3月', '4月', '5月', '6月']
  const dimensionAxis = ['产线A', '产线B', '产线C']
  const heatmapData = [
    [0, 0, 5], // [时间索引, 维度索引, 数值]
    [1, 0, 8],
    [2, 0, 12],
    // ... 更多数据
  ]

  // 处理单元格点击事件
  const handleCellClick = cellData => {
    console.log('点击的单元格:', cellData)
    // cellData: { x, y, value, timeLabel, dimensionLabel }
  }

  // 处理选中状态变化事件
  const handleSelectionChange = cellData => {
    console.log('选中状态变化:', cellData)
    // 可以根据选中数据更新其他组件
  }
</script>
```

## Props 参数

| 参数            | 类型          | 默认值                  | 说明                           |
| --------------- | ------------- | ----------------------- | ------------------------------ |
| `title`         | String        | `''`                    | 图表标题                       |
| `subtitle`      | String        | `''`                    | 图表副标题                     |
| `height`        | String/Number | `400`                   | 图表高度                       |
| `timeAxis`      | Array         | `['1月',...,'12月']`    | 时间轴数据（X 轴）             |
| `dimensionAxis` | Array         | `['产线A',...,'产线E']` | 维度轴数据（Y 轴）             |
| `data`          | Array         | `[]`                    | 热力图数据 `[[x,y,value],...]` |
| `valueRange`    | Array         | `[0, 100]`              | 数值范围 `[最小值, 最大值]`    |
| `colorConfig`   | Object        | 见下方                  | 颜色配置                       |
| `unit`          | String        | `'天'`                  | 数值单位                       |
| `showLabel`     | Boolean       | `true`                  | 是否显示数值标签               |

## 数据格式

### timeAxis (时间轴)

```javascript
// 月份示例
const monthlyAxis = [
  '1月',
  '2月',
  '3月',
  '4月',
  '5月',
  '6月',
  '7月',
  '8月',
  '9月',
  '10月',
  '11月',
  '12月',
]

// 周度示例
const weeklyAxis = [
  '第1周',
  '第2周',
  '第3周',
  '第4周',
  '第5周',
  '第6周',
  '第7周',
  '第8周',
  '第9周',
  '第10周',
  '第11周',
  '第12周',
  '第13周',
]
```

### dimensionAxis (维度轴)

```javascript
// 产线平台示例
const platformAxis = [
  '1200线平台',
  '600线平台',
  '750线平台',
  'N3平台',
  'N4平台',
]

// 供应商示例
const supplierAxis = ['供应商A', '供应商B', '供应商C', '供应商D']
```

### data (热力图数据)

```javascript
const heatmapData = [
  [0, 0, 5], // [时间轴索引, 维度轴索引, 数值]
  [1, 0, 8], // 1月, 产线A, 8天
  [2, 0, 12], // 2月, 产线A, 12天
  [0, 1, 3], // 1月, 产线B, 3天
  // ... 更多数据点
]
```

## 颜色配置

### 默认配置

```javascript
const defaultColorConfig = {
  inRange: {
    color: [
      '#313695',
      '#4575b4',
      '#74add1',
      '#abd9e9',
      '#e0f3f8',
      '#ffffbf',
      '#fee090',
      '#fdae61',
      '#f46d43',
      '#d73027',
      '#a50026',
    ],
  },
  text: ['低', '高'],
  textStyle: {
    color: '#333',
  },
}
```

### 自定义颜色

```javascript
// 简单渐变
const simpleGradient = {
  inRange: {
    color: ['#50f', '#235', '#678'],
  },
  text: ['低风险', '高风险'],
}

// 业务相关配置
const businessConfig = {
  inRange: {
    color: ['#00ff00', '#ffff00', '#ff0000'], // 绿-黄-红
  },
  text: ['供应充足', '供应紧张'],
}
```

## 使用场景

### 1. 年度产能分析

```vue
<HeatmapChart
  title="年度产能Gap分析"
  :time-axis="['1月','2月',...,'12月']"
  :dimension-axis="['1200线', '600线', '750线']"
  :data="yearlyGapData"
  unit="天"
/>
```

### 2. 周度供应商分析

```vue
<HeatmapChart
  title="周度供应商Gap分析"
  :time-axis="['第1周','第2周',...,'第13周']"
  :dimension-axis="['供应商A', '供应商B', '供应商C']"
  :data="weeklySupplierData"
  unit="天"
/>
```

## 事件

### @cell-click

单元格点击事件，返回点击的单元格数据。

```javascript
const handleCellClick = cellData => {
  // cellData 结构:
  // {
  //   x: 0,                    // 时间轴索引
  //   y: 1,                    // 维度轴索引
  //   value: 15,               // GAP天数值
  //   timeLabel: '1月',        // 时间轴标签
  //   dimensionLabel: '产线A'  // 维度轴标签
  // }

  console.log('点击的单元格:', cellData)
  // 可以根据这些信息进行其他模块的查询
}
```

### @selection-change

选中状态变化事件，在以下情况触发：

- 组件初始化时自动选中最大值
- 用户点击单元格时
- 数据更新后重新选中最大值时

```javascript
const handleSelectionChange = cellData => {
  console.log('选中状态变化:', cellData)
  // 数据结构与 cell-click 相同
  // 可以根据选中数据更新其他组件的显示
}
```

## 方法

通过 `ref` 可以访问组件方法：

```vue
<template>
  <HeatmapChart ref="chartRef" :data="data" />
</template>

<script setup>
  import { ref } from 'vue'

  const chartRef = ref(null)

  // 手动更新图表
  const updateChart = () => {
    chartRef.value?.updateChart()
  }

  // 获取 ECharts 实例
  const getEChartsInstance = () => {
    return chartRef.value?.getChartInstance()
  }

  // 手动设置选中的单元格
  const setSelectedCell = cellData => {
    chartRef.value?.setSelectedCell(cellData)
  }

  // 获取当前选中的单元格
  const getSelectedCell = () => {
    return chartRef.value?.getSelectedCell()
  }

  // 更新图表选中状态（不重绘整个图表）
  const updateSelection = () => {
    chartRef.value?.updateChartSelection()
  }
</script>
```

## 注意事项

1. **数据索引**: 数据数组中的索引必须对应 `timeAxis` 和 `dimensionAxis` 的索引
2. **响应式**: 组件会自动监听数据变化并重新渲染
3. **性能**: 大量数据时建议适当调整图表高度和标签显示
4. **移动端**: 在移动端会自动调整字体大小和间距
5. **自动选中**: 组件加载完成后会自动选中数值最大的单元格（多个相同取第一个）
6. **事件处理**: 建议在父组件中处理 `cell-click` 和 `selection-change` 事件来实现业务逻辑
7. **选中状态**: 选中的单元格会显示红色边框和阴影效果

## 依赖

- Vue 3
- ECharts 5+
- Tailwind CSS (用于样式)
