<script lang="ts">
// 归因分析-条形图
export default {
  name: "Horizontal",
};
</script>
<script setup lang="ts">
import { ref, onMounted, toRaw, onBeforeUnmount, watch, reactive } from "vue";
import * as echarts from "echarts";
import eventBus from "@/utils/eventBus2.js";
import useWindowSize from "@/hooks/useWindowSize";
import { cloneDeep } from "lodash-es";
import {
  setLegend,
  setHorizontalDataZoom,
  getAxisLabel,
  textStyle,
  nameHasWan,
  getYAxisMin,
  getHorizontalBarGradientColor,
} from "@/utils/chart.js";

const props = defineProps({
  dataChart: {
    type: Object,
  },
  msgId: {
    type: String,
  },
  enlarge: {
    type: Boolean,
  },
  ascending: {
    type: Boolean,
  },
});

const seriesLabel = () => {
  return {
    show: true,
    position: "right",
    formatter: function (params) {
      const obj = props.dataChart.find((i) => i?.name === params?.name);
      return Number(obj?.curValue)?.toFixed(2); //展示当前值
    },
  };
};
const setToolTips = (yAxisName) => {
  let tooltip = {
    trigger: "axis",
    axisPointer: {
      type: "shadow",
    },
    confine: true,
    textStyle: {
      fontWeight: "normal", // 确保字体不加粗
      fontSize: 10,
    },
    formatter: function (params) {
      //日期，图例，值，
      let str = params[0]?.name || " " + "     " + yAxisName || " ";
      params.forEach((p) => {
        let value = new Intl.NumberFormat("en-US").format(p?.value); //千分位
        if (p.seriesName && !p.seriesName.includes("series")) {
          let seriesName = p.seriesName;
          let color = p.color;
          str = `${str}<br/><span style="line-height:20px;"><span style="display:inline-block;margin-right:5px;width: 12px; height: 12px;background-color: ${color};border-radius: 50%;"></span>${seriesName}:   ${value}</span>`;
        } else {
          const obj = props.dataChart.find(
            (i) => i?.name === params[0]?.axisValue
          );
          const curValue = Number(obj?.curValue).toFixed(2);
          str = `${str}<br/> ${curValue || " "} &nbsp&nbsp ${value + "%"}`;
        }
      });
      // if (graphAnalysis) {
      //   str = `${str}<br/><label style="font-size: 12px;font-weight: 500;line-height: 12px;height:12px;color: #F4A300;font-family: PingFang SC;">左键点击数据进行归因分析</label> `;
      // }
      return str;
    },
  };
  return tooltip;
};
var chart;
const initChart = () => {
  chart = echarts.init(document.getElementById(props.msgId));
  const data = cloneDeep(toRaw(props.dataChart));
  data.forEach((i) => {
    i.value = Number(i?.value?.split("%")[0]);
    if (!i.name) {
      i.name = " ";
    }
  });
  const sortedData = data.sort((a, b) => {
    return props.ascending ? b.value - a.value : a.value - b.value;
  });
  let xAxis = { type: "value", name: "%" };
  let yAxis = { type: "category", data: sortedData.map((item) => item.name) };
  let series = [
    {
      type: "bar",
      data: sortedData.map((item) => item.value),
    },
  ];
  yAxis = {
    ...yAxis,
    type: "category",
    // boundaryGap: false,
    axisLabel: getAxisLabel(),
    position: "left",
  };
  const hasWan = nameHasWan();
  xAxis = {
    ...xAxis,
    scale: true,
    type: "value",
    splitNumber: 2,
    name: "%",
    // nameRotate: 90,
    // nameLocation: "end",
    nameGap: -30,
    axisLabel: {
      interval: 0, // 坐标轴刻度标签的显示间隔
      // formatter: function (value) {
      //   return addWanValue(value, hasWan);
      // },
    },
    min: getYAxisMin(series),
  };
  series = series?.map((serie, index) => {
    const gradientColor = getHorizontalBarGradientColor(index);
    const temp = {
      type: "bar",
      barWidth: 12,
      lineStyle: {
        width: 3,
        color: gradientColor,
      },
      symbol: "circle",
      symbolSize: 6,
      itemStyle: { color: gradientColor },
      data: serie.data,
      label: seriesLabel(),
    };

    return temp;
  });
  const dataZoom = setHorizontalDataZoom(series);
  const tooltip = setToolTips("");
  const legend = setLegend(data?.series);

  chart.setOption({
    textStyle,
    color: ["#3979F9", "#EBB20F"],
    grid: {
      left: 60,
      right: 60,
      top: 20,
      bottom: data?.series?.length > 1 ? 60 : 30,
    },
    legend,
    xAxis,
    yAxis,
    series,
    dataZoom,
    tooltip,
  });
  // chart &&
  //   chart.on("click", function (params) {
  //     console.log("横条图点击事件参数:", params);
  //   });
};
onMounted(() => {
  if (props?.dataChart?.length) {
    initChart();
  }
  window.addEventListener("resize", () => {
    if (chart) {
      chart.resize();
    }
  });
});
const { isMobile } = useWindowSize();

watch(
  () => props.ascending,
  (v) => {
    if (props?.dataChart?.length) {
      initChart();
    }
  }
);
onBeforeUnmount(() => {
  eventBus.off("resizeGraph");
});
</script>

<template>
  <div>
    <div
      :style="{ height: isMobile ? '150px' : '200px' }"
      :id="msgId"
      v-show="props?.dataChart?.length"
    ></div>
    <div v-show="!props?.dataChart?.length" style="height: 200px">
      <a-empty description="暂无数据" />
    </div>
  </div>
</template>

<style scoped>
:deep(.ant-empty-image) {
  height: 181px !important;
}
</style>
