<script lang="ts">
// 普通柱
export default {
  name: "ChartBasicBar",
};
</script>
<script setup lang="ts">
import { ref, onMounted, toRaw, onBeforeUnmount } from "vue";
import * as echarts from "echarts";
import eventBus from "@/utils/eventBus2.js";
import {
  setLegend,
  setDataZoom,
  setToolTips,
  themeColor,
  getAxisLabel,
  getGrid,
  textStyle,
  splitNumber,
  graphStyle,
  removeWanName,
  nameHasWan,
  addWanValue,
  getYAxisMin,
  seriesLabel,
  getBarGradientColor,
} from "@/utils/chart.js";

const chartRef = ref();
const props = defineProps({
  dataChart: {
    type: Object,
  },
  msgId: {
    type: String,
  },
  enlarge: {
    type: Boolean,
  },
});
var chart;
onMounted(() => {
  chart = echarts.init(chartRef.value);
  const data = toRaw(props.dataChart);
  let xAxis = data?.xAxis;
  xAxis = {
    ...xAxis,
    type: "category",
    // boundaryGap: false,
    name: "",
    axisLabel: getAxisLabel(),
  };

  let yAxis = data?.yAxis;
  const initYAxisName = yAxis.name;
  const hasWan = nameHasWan(yAxis.name);
  yAxis = {
    ...yAxis,
    name: removeWanName(yAxis.name),
    scale: true,
    type: "value",
    axisLabel: {
      interval: 0, // 坐标轴刻度标签的显示间隔
      formatter: function (value) {
        return addWanValue(value, hasWan);
      },
    },
    splitNumber,
    min: getYAxisMin(data?.series),
  };
  const series = data?.series?.map((serie, index) => {
    const gradientColor = getBarGradientColor(index);
    const temp = {
      name: serie.name || " ",
      type: "bar",
      barWidth: 12,
      lineStyle: {
        width: 3,
        color: gradientColor,
      },
      symbol: "circle",
      symbolSize: 6,
      itemStyle: { color: gradientColor },
      data: serie.data,
      label: seriesLabel(),
    };
    return temp;
  });

  const dataZoom = setDataZoom(series);
  const tooltip = setToolTips(initYAxisName);
  const legend = setLegend(data?.series);
  chart.setOption({
    color: ["#3979F9", "#EBB20F"],
    grid: getGrid(data?.series?.length > 1, data?.series[0]?.data?.length > 12),
    legend,
    xAxis,
    yAxis,
    series,
    dataZoom,
    tooltip,
    textStyle,
  });
  window.addEventListener("resize", () => {
    if (chart) {
      chart.resize();
    }
  });
});

onBeforeUnmount(() => {
  eventBus.off("resizeGraph");
});
</script>

<template>
  <div ref="chartRef" :style="graphStyle(enlarge)" :id="msgId"></div>
</template>

<style scoped></style>
