<template>
  <div
    class="p-[18px] pb-24px text-[#000000] custom-modal"
    :class="isMobile ? 'pt-[24px] pr-[29px]' : ''"
  >
    <div
      class="w-[24px] h-[24px] absolute flex items-center justify-center text-[#666666] text-[14px]"
      :class="
        isMobile
          ? 'top-[16px] right-[18px]'
          : 'top-[11px] right-[11px] cursor-pointer'
      "
      @click="onClose()"
    >
      <CloseOutlined />
    </div>
    <h3
      class=""
      :class="isMobile ? 'text-[16px] mb-[14px]' : 'text-[14px] mb-[12px]'"
    >
      <label class="modal-title">归因分析</label>
    </h3>

    <div class="w-full flex justify-end items-center">
      <a-button
        type="primary"
        class="bg-[#635FFF] rounded-[22px]"
        :class="
          isMobile
            ? 'w-full h-[48px] mt-[24px]  text-[18px]'
            : 'h-[32px] mt-[18px] text-[14px] leading-[14px]'
        "
        :loading="loading"
        @click="onSubmit"
      >
        归因分析
      </a-button>
    </div>
  </div>
</template>
<script setup>
import { ref } from "vue";
import { CloseOutlined } from "@ant-design/icons-vue";

import useWindowSize from "@/hooks/useWindowSize";

defineOptions({
  name: "ModalForm",
});
const { isMobile } = useWindowSize();

const loading = ref(false);
const emit = defineEmits(["onClose"]);
const onSubmit = () => {
  emit("onConfirm");
};
const onClose = () => {
  emit("onClose");
};
</script>
<style scoped>
.modal-title {
  font-weight: 700;
}
.custom-modal {
  border-radius: 12px;
  padding: 18px;
  background: #ffffff;
  box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(99, 85, 255, 0.12);
}
</style>
