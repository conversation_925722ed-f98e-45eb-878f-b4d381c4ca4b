<script lang="ts">
// 普通柱
export default {
  name: "ChartBasicLine",
};
</script>
<script setup lang="ts">
import { ref, onMounted, toRaw, onBeforeUnmount, computed } from "vue";
import * as echarts from "echarts";
import eventBus from "@/utils/eventBus2.js";
import {
  colorList,
  setLegend,
  setDataZoom,
  setToolTips,
  themeColor,
  getAxisLabel,
  getGrid,
  textStyle,
  splitNumber,
  graphStyle,
  removeWanName,
  nameHasWan,
  addWanValue,
  getYAxisMin,
  seriesLabel,
} from "@/utils/chart.js";
import { cloneDeep } from "lodash-es";
import AttributionConfirm from "./AttributionConfirm.vue";

const props = defineProps({
  dataChart: {
    type: Object,
  },
  msgId: {
    type: String,
  },
  enlarge: {
    type: Boolean,
  },
  graphAnalysis: {
    type: <PERSON>olean,
  },
});

var chart;
const visible = ref(false);
const top = ref(0);
const left = ref(0);

// 生成唯一的图表ID，避免多个聊天会话冲突
const uniqueChartId = computed(() => {
  return `chart-${props.msgId}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
});

// 定义resize处理函数，确保可以正确移除监听器
const handleResize = () => {
  if (chart) {
    chart.resize();
  }
};

onMounted(() => {
  const chartDom = document.getElementById(uniqueChartId.value);

  // 检查是否已经有chart实例，如果有就先dispose
  const existingChart = echarts.getInstanceByDom(chartDom);
  if (existingChart) {
    existingChart.dispose();
  }

  chart = echarts.init(chartDom);
  const data = toRaw(props.dataChart);
  let xAxis = data?.xAxis;
  xAxis = {
    ...xAxis,
    type: "category",
    // boundaryGap: false,
    name: "",
    axisLabel: getAxisLabel(),
  };

  let yAxis = data?.yAxis;
  const initYAxisName = yAxis.name;
  const hasWan = nameHasWan(yAxis.name);
  yAxis = {
    ...yAxis,
    name: removeWanName(yAxis.name),
    scale: true,
    type: "value",
    axisLabel: {
      interval: 0, // 坐标轴刻度标签的显示间隔
      formatter: function (value) {
        return addWanValue(value, hasWan);
      },
    },
    splitNumber,
    min: getYAxisMin(data?.series),
  };
  const series = data?.series?.map((serie, index) => {
    const color = colorList[index % 10];
    const temp = {
      name: serie.name || " ",
      type: "line",
      lineStyle: {
        width: 2,
        color,
      },
      symbol: "circle",
      symbolSize: 8,
      itemStyle: {
        color: '#fff', // 填充色设为白色（空心效果）
        borderColor: color, // 边框颜色为线条颜色
        borderWidth: 1 // 边框宽度
      },
      data: serie.data,
      label: seriesLabel(),
      // areaStyle: {
      //   color: color + '1A'
      // }
    };
    return temp;
  });
  const dataZoom = setDataZoom(series);
  const tooltip = setToolTips(initYAxisName); //props.graphAnalysis
  const legend = setLegend(data?.series);
  chart.setOption({
    textStyle,
    color: themeColor,
    grid: getGrid(data?.series?.length > 1, data?.series[0]?.data?.length > 12),
    legend,
    xAxis,
    yAxis,
    series,
    dataZoom,
    tooltip,
  });

  // 添加事件监听器
  window.addEventListener("resize", handleResize);
  // chart.on("click", function (params) {
  //   if (params?.name && params?.value) {
  //     console.log("点击得数据---", params);
  //     top.value = Number(params?.event?.event?.offsetY) + 100;
  //     left.value = params?.event?.event?.offsetX;
  //     visible.value = true;
  //     clickData.value = params;
  //   }
  // });
});
const onClose = () => {
  visible.value = false;
};

const onConfirm = () => {
  //拿数据，
  // eventBus.emit("analysisQsEvent", param);
  visible.value = false;
};

onBeforeUnmount(() => {
  eventBus.off("resizeGraph");
  window.removeEventListener("resize", handleResize);
});
</script>

<template>
  <div :style="graphStyle(enlarge)" :id="uniqueChartId" class="graph"></div>
  <!-- <div
    class="floating-div"
    v-if="visible"
    :style="{ top: `${top}px`, left: `${left}px` }"
  >
    <AttributionConfirm
      @onClose="onClose"
      @onConfirm="onConfirm"
      :clickData="clickData"
    />
  </div> -->
</template>

<style scoped>
.graph {
  position: relative;
}

.floating-div {
  position: absolute;
  z-index: 9999;
}
</style>
