<script lang="ts">
// 堆叠柱图
export default {
  name: "ChartBasicPie",
};
</script>
<script setup lang="ts">
import { ref, onMounted, toRaw, onBeforeUnmount } from "vue";
import * as echarts from "echarts";
import eventBus from "@/utils/eventBus2.js";
import {
  colorList,
  textStyle,
  graphStyle,
  seriesLabel,
} from "@/utils/chart.js";

const chartRef = ref();
const props = defineProps({
  dataChart: {
    type: Object,
  },
  msgId: {
    type: String,
  },
  enlarge: {
    type: <PERSON>olean,
  },
});
var chart;
const findColor = (index) => {
  if (index < colorList.length) {
    return colorList[index];
  } else {
    while (index > colorList.length && index >= 0) {
      index = index - colorList.length;
    }
    return colorList[index];
  }
};
onMounted(() => {
  chart = echarts.init(chartRef.value);
  const source = toRaw(props.dataChart.series[0]?.data || []);
  const yAxisName = props?.dataChart?.yAxis.name;
  const nameList = props?.dataChart?.xAxis?.data || [];
  let serieData = source
    ?.map((item, index) => {
      return { name: nameList[index], value: item };
    })
    .filter((i) => i.value > 0);

  const option = {
    textStyle,
    title: {
      top: "0px", // 标题距离顶部的距离
    },
    tooltip: {
      trigger: "item",
      textStyle: {
        fontWeight: "normal", // 确保字体不加粗
        fontSize: 10,
      },
      formatter: function (param) {
        let value = new Intl.NumberFormat("en-US").format(param.value); //千分位
        return `${param.name}      ${yAxisName}：<br/> ${value}`;
      },
      axisPointer: {
        type: "shadow",
      },
    },
    legend: {
      type: "scroll",
      orient: "horizontal",
      bottom: 0,
    },
    label: {
      show: true,
      formatter: "{b} : {c}",
      fontSize: "12",
    },
    labelLine: { show: true },
    series: [
      {
        name: props?.dataChart?.title || " ",
        type: "pie",
        radius: "50%",
        data: serieData,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: "rgba(0, 0, 0, 0.5)",
          },
        },
        itemStyle: {
          color: function (colors) {
            return findColor(colors.dataIndex);
          },
        },
        label: seriesLabel(),
      },
    ],
    grid: {
      top: 0,
      bottom: 40,
    },
  };
  chart.setOption(option);
  window.addEventListener("resize", () => {
    if (chart) {
      chart.resize();
    }
  });
});
onBeforeUnmount(() => {
  eventBus.off("resizeGraph");
});
</script>

<template>
  <div ref="chartRef" :style="graphStyle(enlarge)" :id="msgId"></div>
</template>

<style scoped></style>
