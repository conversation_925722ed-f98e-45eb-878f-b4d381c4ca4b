<script lang="ts">
// 条形图
export default {
  name: "HorizontalBar",
};
</script>
<script setup lang="ts">
import { ref, onMounted, toRaw, onBeforeUnmount } from "vue";
import * as echarts from "echarts";
import eventBus from "@/utils/eventBus2.js";
import {
  setLegend,
  setToolTips,
  setHorizontalDataZoom,
  getAxisLabel,
  textStyle,
  graphStyle,
  removeWanName,
  nameHasWan,
  addWanValue,
  getYAxisMin,
  seriesLabel,
  getHorizontalBarGradientColor,
} from "@/utils/chart.js";

const chartRef = ref();
const props = defineProps({
  dataChart: {
    type: Object,
  },
  msgId: {
    type: String,
  },
  enlarge: {
    type: Boolean,
  },
});
var chart;
onMounted(() => {
  chart = echarts.init(chartRef.value);
  const data = toRaw(props.dataChart);
  //横向柱形图，X、Y轴反过来
  let yAxis = data?.xAxis;
  yAxis = {
    ...yAxis,
    type: "category",
    // boundaryGap: false,
    name: "",
    axisLabel: getAxisLabel(),
    position: "left",
  };
  console.log(data);
  let xAxis = data?.yAxis;
  const initYAxisName = xAxis.name;
  const hasWan = nameHasWan(xAxis.name);
  xAxis = {
    ...xAxis,
    scale: true,
    type: "value",
    splitNumber: 2,
    name: removeWanName(xAxis.name),
    // nameRotate: 90,
    // nameLocation: "end",
    nameGap: -30,
    axisLabel: {
      interval: 0, // 坐标轴刻度标签的显示间隔
      formatter: function (value) {
        return addWanValue(value, hasWan);
      },
    },
    min: getYAxisMin(data?.series),
  };
  const series = data?.series?.map((serie, index) => {
    const gradientColor = getHorizontalBarGradientColor(index);
    const label = seriesLabel() || {}
    const temp = {
      name: serie.name || " ",
      type: "bar",
      barWidth: 20, // 固定柱子宽度为20像素
      lineStyle: {
        width: 3,
        color: gradientColor,
      },
      symbol: "circle",
      symbolSize: 6,
      itemStyle: { color: gradientColor },
      data: serie.data,
      label: {
        ...label,
        position: "right",
      },
    };

    return temp;
  });
  const dataZoom = setHorizontalDataZoom(series);
  const tooltip = setToolTips(initYAxisName);
  const legend = setLegend(data?.series);

  chart.setOption({
    textStyle,
    color: ["#3979F9", "#EBB20F"],
    grid: {
      left: 40,
      right: 60,
      top: 20,
      bottom: data?.series?.length > 1 ? 60 : 50,
    },
    legend:{
      ...legend,
      bottom: 0
    },
    xAxis,
    yAxis,
    series,
    dataZoom,
    tooltip,
  });
  window.addEventListener("resize", () => {
    if (chart) {
      chart.resize();
    }
  });
});

onBeforeUnmount(() => {
  eventBus.off("resizeGraph");
});
</script>

<template>
  <div ref="chartRef" :style="graphStyle(enlarge)" :id="msgId"></div>
</template>

<style scoped></style>
