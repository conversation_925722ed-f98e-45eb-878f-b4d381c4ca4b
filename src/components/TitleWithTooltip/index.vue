<template>
  <div class="title-with-tooltip">
    <span v-if="title" class="title-text">{{ title }}</span>
    <a-tooltip v-if="tooltip" :title="tooltip" :placement="placement">
      <svg-icon type="tooltip" class="tooltip-icon" />
    </a-tooltip>
  </div>
</template>

<script setup>
  // 定义组件 props
  const props = defineProps({
    // 标题文案
    title: {
      type: String,
      default: '',
      required: true,
    },
    // 提示信息
    tooltip: {
      type: String,
      default: '',
    },
    // tooltip 显示位置
    placement: {
      type: String,
      default: 'top',
    },
  })
</script>

<style scoped>
  .title-with-tooltip {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .title-text {
    font-family: PingFang SC;
    font-size: 14px;
    font-weight: 500;
    line-height: 18px;
    letter-spacing: 0px;

    font-variation-settings: 'opsz' auto;
    color: #333333;
  }

  .tooltip-icon {
    font-size: 14px;
    color: #999999;
    cursor: pointer;
    transition: color 0.3s ease;
    width: 14px !important;
    height: 14px !important;
  }

  .tooltip-icon:hover {
    color: #6355ff;
  }
</style>
