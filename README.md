# AI Purchase Dashboard Frontend

AI采购报表前端项目，基于Vue 3 + Vite构建。

## 项目简介

这是一个AI采购相关的报表系统前端项目，提供数据可视化和报表展示功能。

## 技术栈

- Vue 3 (Composition API)
- Vite
- Ant Design Vue
- Vue Router
- Pinia (状态管理)
- Tailwind CSS

## 开发环境设置

推荐使用 [VS Code](https://code.visualstudio.com/) + [Vue - Official](https://marketplace.visualstudio.com/items?itemName=Vue.volar) 插件

## 启动项目

```bash
# 安装依赖
npm install

# 开发环境启动
npm run serve

# 构建生产版本
npm run build
```
